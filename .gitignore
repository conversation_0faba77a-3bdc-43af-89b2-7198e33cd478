# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
/.pnp
.pnp.js

# testing
/coverage

# cypress
cypress/screenshots
cypress/downloads
cypress/videos
cypress.env.json
cypress/fixtures/*-slugs.json

# next.js
.next
/out

# next-sitemap
public/robots.txt

# Compiled Sanity Studio
**/dist

# Temporary Sanity runtime, generated by the CLI on every dev server start
*.sanity

# Logs
*/logs
*.log

# Typescript
*.tsbuildinfo

# cache
.swc/

# production
/build

# misc
.DS_Store
*.pem
Thumbs.db

# debug
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*

# dotenv local files
.env*.local

# local folder
local

# vercel/netlify
.vercel
.netlify

# Sentry Config File
.env.sentry-build-plugin

# TS type coverage
coverage-ts

# Hosting
.vercel
.netlify
