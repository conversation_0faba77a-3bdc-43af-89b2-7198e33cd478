# If you add a plugin here, make sure to also install the plugin as a dev
# dependency in package.json by running `npm i -D "plugin-name"`

# NOTE: If context not specified it runs on every branch!

#Do not remove or edit this plugin!
[[plugins]]
    package = "@netlify/plugin-nextjs"
    pinned_version = "5.10.0"

[[plugins]]
    package = "@netlify/plugin-lighthouse"
    pinned_version = "6.0.0"
    [plugins.inputs.settings]
        preset = "desktop"

[functions]
    included_files = ["!tontine/public/research/**"]

[[redirects]]
    from = "/FAQs"
    to = "/faqs/"
    force = true


## Build settings

# Only run build on non-sanity branches
[build]
    ignore = "node ignore_builds.js"

# Daily rebuild function
[context.production-tontine.functions."daily-rebuild"]
    schedule = "30 12 * * *"

[context.deploy-preview]
    # Dir where netlify will be looking for the project
    base = "."
    # Command that builds the tontine website
    command = "npm ci && npm run format && npm run lint && npm run build-tontine && npm run postbuild && npm run tc-report:tontine && npm run e2e:headless"
    # Generated publish directory from build command, since this is a monorepo if
    # you adding another project to be build use `/another-project/.next`
    publish = "/tontine/.next"

[context.staging]
    # Dir where netlify will be looking for the project
    base = "."
    # Command that builds the staging branch
    command = "npm ci && npm run format && npm run lint && npm run build-tontine && npm run postbuild && npm run tc-report:tontine && npm run e2e:headless"
    # Generated publish directory from build command, since this is a monorepo if
    # you adding another project to be build use `/another-project/.next`
    publish = "/tontine/.next"

[context.production-tontine]
    # Dir where netlify will be looking for the project
    base = "."
    # Command that builds the tontine website
    command = "npm ci && npm run format && npm run lint && npm run build-tontine && npm run postbuild"
    # Generated publish directory from build command, since this is a monorepo if
    # you adding another project to be build use `/another-project/.next`
    publish = "/tontine/.next"
