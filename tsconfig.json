{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "removeComments": true, "preserveConstEnums": true, "strict": true, "alwaysStrict": true, "noUncheckedIndexedAccess": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "allowUnreachableCode": false, "noFallthroughCasesInSwitch": true, "outDir": "out", "declaration": true, "sourceMap": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "allowJs": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "jsx": "preserve", "noEmit": true, "isolatedModules": true, "incremental": true, "importHelpers": true, "allowImportingTsExtensions": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/public/*": ["./public/*"]}}, "include": ["**.ts", "**.tsx", "**.js", "**.jsx", "**.mjs", ".next/types/**/*.ts"], "exclude": ["out/**", "tontine/.next/**", "sanity-studio/.sanity/**", "sanity-studio/dist/**", "node_modules/**"]}