import strings from '../fixtures/strings.json'
import type { PageSlugs } from '../support/types'
import { TestingIDs } from '../support/ui-component-ids'

const nextLinkDataID = `[data-cy="${TestingIDs.nextLink}"]`

const uniqueLinks = new Set<string>()

/**
 * Checks the validity of a link by visiting the specified URL.
 * - `href` - The URL to check.
 */
const checkValidLink = (link: JQuery<HTMLAnchorElement>) => {
  const href = link?.[0]?.href
  if (href) {
    // Check if the link has already been processed
    if (uniqueLinks.has(href)) {
      cy.log(`Link **${href}** has been skipped as it has already been visited`)
      return
    }

    // Add the link to the set of processed links
    uniqueLinks.add(href)

    const attributes = link.attr('rel')

    // Skip external links, mailto links, and PDF links
    if (
      attributes?.includes('nofollow') ||
      href.includes('mailto:') ||
      href.includes('tel:') ||
      href.includes('.pdf')
    ) {
      cy.log(`External link **${href}** has been skipped`)
      return
    }

    // Visit the link
    cy.visit(href)

    // Handle internal links with anchor
    if (href.includes('#')) {
      const id = href.split('#')[1]
      cy.get(`#${id}`).should('be.visible')
      cy.log(`Internal link **${href}** navigated to element with ID **${id}**`)
      return
    }

    // Verify the pathname
    cy.location('href').should('eq', href)
  }
}

describe('Footer and Navigation menu link validation', () => {
  it('should verify all links in the navigation menu are functional', () => {
    cy.visit(strings.HOME_PAGE)
    cy.getByDataID(TestingIDs.navBar)
      .find(nextLinkDataID)
      .each(($link: JQuery<HTMLAnchorElement>) => {
        checkValidLink($link)
      })
  })

  it('should verify all links in the footer are functional', () => {
    cy.visit(strings.HOME_PAGE)
    cy.getByDataID(TestingIDs.footer)
      .find(nextLinkDataID)
      .each(($link: JQuery<HTMLAnchorElement>) => {
        checkValidLink($link)
      })
  })
})

describe('Functional links verification across website pages', () => {
  it('should verify all links on each page are functional', () => {
    cy.fixture(strings.PARENT_PAGE_SLUGS).then((pageSlugs: PageSlugs) => {
      pageSlugs.forEach((pageSlug: string) => {
        cy.visit(pageSlug)
        cy.getByDataID(TestingIDs.sectionWrapper).then(($sectionWrapper) => {
          if ($sectionWrapper.find(nextLinkDataID).length > 0) {
            cy.wrap($sectionWrapper)
              .find(nextLinkDataID)
              .each(($link: JQuery<HTMLAnchorElement>) => {
                checkValidLink($link)
              })
          } else {
            cy.log('No NextLinks element found in the section.')
          }
        })
      })
    })
  })
})

describe('Functional links verification across website content pages', () => {
  it('should verify all links on each content page are functional', () => {
    cy.fixture(strings.SUB_PAGE_SLUGS).then((pageSlugs: PageSlugs) => {
      pageSlugs.forEach((pageSlug: string) => {
        cy.visit(pageSlug)
        cy.getByDataID(TestingIDs.postContent).then(($postContent) => {
          if ($postContent.find(nextLinkDataID).length > 0) {
            cy.wrap($postContent)
              .find(nextLinkDataID)
              .each(($link: JQuery<HTMLAnchorElement>) => {
                checkValidLink($link)
              })
          } else {
            cy.log('No NextLinks found in the section.')
          }
        })
      })
    })
  })
})
