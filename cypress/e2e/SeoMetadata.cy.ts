import strings from '../fixtures/strings.json'
import { websiteConfig } from '../support/consts'
import type { PageSlugs } from '../support/types'
import { checkMetaData } from '../support/utils'

describe('SEO Metadata validation across main pages', () => {
  it('validates SEO metadata for all pages', () => {
    cy.fixture(strings.SEO_PAGE_SLUGS).then((pageSlugs: PageSlugs) => {
      pageSlugs.forEach((pageUrl: string) => {
        cy.visit(pageUrl)
        checkMetaData({
          tagNames: websiteConfig.META_TAG_NAMES,
          tagProperties: websiteConfig.META_TAG_PROPERTIES,
        })
      })
    })
  })
})
