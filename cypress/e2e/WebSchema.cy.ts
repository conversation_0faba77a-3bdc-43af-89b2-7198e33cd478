import strings from '../fixtures/strings.json'
import { websiteConfig } from '../support/consts'
import type {
  ContentPageWebSchemaType,
  PageSlugs,
  WebSchemaData,
} from '../support/types'
import { TestingIDs } from '../support/ui-component-ids'
import { checkWebSchemaByType } from '../support/utils'

describe('Checks pages that have required webschema', () => {
  it('should check if homepage has required webschemas', () => {
    cy.visit(strings.HOME_PAGE)
    websiteConfig.HOME_PAGE_SCHEMA_TYPES.forEach((schema) => {
      cy.getByDataID(schema).each(($el) => {
        const id = $el.attr('id')
        const htmlContent = $el.html()
        const jsonData = JSON.parse(htmlContent) as WebSchemaData
        cy.log(`Successfully found webschema: ${id}`)
        cy.log(`**Name**: ${jsonData.name}`)
        cy.log(`**Description**: ${jsonData.description}`)
      })
    })
  })
  it(`should check if ${strings.FAQ} has required webschema`, () => {
    cy.visit(strings.FAQ)
    cy.getByDataID(TestingIDs.webschemaScript).each(($el) => {
      const id = $el.attr('id')
      const htmlContent = $el.html()
      const jsonData = JSON.parse(htmlContent) as WebSchemaData
      cy.log(`Successfully found webschema: ${id}`)
      cy.log(`**Type**: ${jsonData['@type']}`)
    })
  })
})

describe('Checks if content gallery pages have required webschema', () => {
  websiteConfig.WEBSCHEMA_CONTENT_PAGES.forEach((pageUrl) => {
    it(`should check if ${pageUrl} page has required webschema`, () => {
      cy.visit(pageUrl)
      cy.getByDataID(TestingIDs.webschemaScript).then(($el) => {
        const id = $el.attr('id')
        const htmlContent = $el.html()
        const jsonData = JSON.parse(htmlContent) as WebSchemaData
        cy.log(`Successfully found webschema: ${id}`)
        cy.log(`**Type**: ${jsonData['@type']}`)
        cy.log(`**Name**: ${jsonData.name}`)
      })
    })
  })
})

describe('Checks if individual content pages have required webschema data', () => {
  it('validates webschema data on all individual content pages', () => {
    cy.fixture(strings.SEO_SUB_PAGE_SLUGS).then((pageSlugs: PageSlugs) => {
      pageSlugs.forEach((pageUrl: string) => {
        cy.visit(pageUrl)
        cy.getByDataID(TestingIDs.webschemaScript).then(($schema) => {
          const id = $schema.attr('id')
          const htmlContent = $schema.html()
          const jsonData = JSON.parse(htmlContent) as ContentPageWebSchemaType
          cy.log(`Successfully found: ${id}`)
          checkWebSchemaByType(jsonData)
        })
      })
    })
  })
})
