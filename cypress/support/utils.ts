import { websiteConfig } from './consts'
import type { ContentPageWebSchemaType, StaticPageParamData } from './types'

/**
 * Checks meta tags based on specified tag names and properties.
 *
 * - `tagNames` - An array of tag names to check.
 * - `tagProperties` - An array of tag properties to check.
 */
export const checkMetaData = ({
  tagNames,
  tagProperties,
}: {
  tagNames: typeof websiteConfig.META_TAG_NAMES
  tagProperties: typeof websiteConfig.META_TAG_PROPERTIES
}) => {
  // Loop through each tagName in the array
  tagNames.forEach((tagName) => {
    cy.checkMetaTagByName(tagName)
  })
  tagProperties.forEach((tagProperty) => {
    cy.checkMetaTagByProperty(tagProperty)
  })
}

/**
 * Processes page slugs based on the provided pages data.
 * We only check the first news, research, blog and videos posts
 * to avoid test flakiness and make it more stable and faster.
 * - `pages` - The array of pages data.
 */
export const processPageSlugs = (pages: StaticPageParamData) => {
  const slugArray: Array<string> = pages?.pageSlugs
    ?.map((item: string) => `/${item}/`)
    .concat('/')

  const newsPosts = pages?.posts?.news?.map((item: string) => `/news/${item}/`)
  const researchPosts = pages?.posts?.research?.map(
    (item: string) => `/research/${item}/`
  )
  const blogPosts = pages?.posts?.blog?.map((item: string) => `/blog/${item}/`)
  const videosPosts = pages?.posts?.videos?.map(
    (item: string) => `/videos/${item}/`
  )

  const subPagesArray = [
    ...newsPosts,
    ...researchPosts,
    ...blogPosts,
    ...videosPosts,
  ]

  const trimmedSubPagesArray = [
    newsPosts?.[0] ?? '',
    researchPosts?.[0] ?? '',
    blogPosts?.[0] ?? '',
    videosPosts?.[0] ?? '',
  ]

  return { slugArray, subPagesArray, trimmedSubPagesArray }
}

/**
 * Checks a web schema object based on its `@type` property and verifies its properties
 * against predefined schema keys.
 * - `webSchema` - The web schema object to be checked.
 */
export const checkWebSchemaByType = (webSchema: ContentPageWebSchemaType) => {
  if (webSchema?.['@type'] === 'VideoObject') {
    websiteConfig.VIDEO_WEBSCHEMA.forEach((key) => {
      cy.checkWebSchemaProperties(webSchema, key)
    })
  }
  if (webSchema?.['@type'] === 'NewsArticle') {
    websiteConfig.NEWS_WEBSCHEMA.forEach((key) => {
      cy.checkWebSchemaProperties(webSchema, key)
    })
  }
}
