const contentPostQuery = /* groq */ `
"combinedPosts": [
  ...postsArray[]->.slug.current,
  ...featuredPost[]->.slug.current,
]
`

// Query that fetches all page slugs from Sanity
const pageSlugQuery = /* groq */ `
*[_id == $WEBSITE_TONTINE][0]{
  "pageSlugs": array::compact([...pagesOnWebsite[]->.pageSlug.current, homepage->.pageSlug.current]),
  "posts": {
    "news": pagesOnWebsite[@->pageSlug.current == 'news']->pageSections[]->{${contentPostQuery}}.combinedPosts[],
    "videos": pagesOnWebsite[@->pageSlug.current == 'videos']->pageSections[]->{${contentPostQuery}}.combinedPosts[],
    "blog": pagesOnWebsite[@->pageSlug.current == 'blog']->pageSections[]->{${contentPostQuery}}.combinedPosts[],
    "research": pagesOnWebsite[@->pageSlug.current == 'research']->pageSections[]->{${contentPostQuery}}.combinedPosts[],
  }
}
`
export { pageSlugQuery }
