const TestingIDs = {
  navBar: 'nav-bar',
  footer: 'footer',
  organizationSchema: 'organization-schema',
  investmentSchema: 'investment-schema',
  sectionWrapper: 'section-wrapper',
  contentPost: 'content-post',
  postContent: 'post-content',
  webschemaScript: 'web-schema-script',
  nextLink: 'next-link',
  feedbackCard: 'feedback-card',
  feedbackModal: 'feedback-modal',
  feedbackRating: 'feedback-rating',
  feedbackText: 'feedback-text',
  feedbackSubmitButton: 'feedback-submit-button',
  feedbackRedirectButton: 'feedback-redirect-button',
  faqFeedback: 'faq-feedback',
  formTextArea: 'form-text-area',
} as const

export type TestID = (typeof TestingIDs)[keyof typeof TestingIDs]

export { TestingIDs }
