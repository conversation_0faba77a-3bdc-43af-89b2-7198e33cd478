/// <reference types="cypress" />

import strings from '../fixtures/strings.json'
import { websiteConfig } from './consts'
import type { ContentPageWebSchemaType, StaticPageParamData } from './types'
import type { TestID } from './ui-component-ids'
import { processPageSlugs } from './utils'

Cypress.Commands.add('checkMetaTagByProperty', (property: string) => {
  cy.get(`head meta[property="${property}"]`).should('exist')
})
Cypress.Commands.add('checkMetaTagByName', (property: string) => {
  cy.get(`head meta[name="${property}"]`).should('exist')
})

Cypress.Commands.add(
  'checkWebSchemaProperties',
  (webSchema: ContentPageWebSchemaType, key: string) => {
    cy.wrap(webSchema).should('have.property', key)
    cy.log(`Key ${key} exists`)
  }
)

Cypress.Commands.add('getByDataID', (selector, ...args) => {
  return cy.get(`[data-cy=${selector}]`, ...args)
})

Cypress.Commands.add('fetchSanityData', (query) => {
  const projectId = 'hl9czw39'
  const dataset = 'production'
  const sanityAuthToken = Cypress.env('SANITY_AUTH_TOKEN')
  const websiteId =
    Cypress.env('NEXT_PUBLIC_WEBSITE_ID') ?? websiteConfig.WEBSITE_ID

  const url = `https://${projectId}.api.sanity.io/v2024-12-31/data/query/${dataset}?perspective=drafts`

  return cy
    .request({
      method: 'post',
      url,
      body: {
        query,
        params: {
          WEBSITE_TONTINE: websiteId,
        },
      },
      headers: {
        Authorization: sanityAuthToken
          ? `Bearer ${sanityAuthToken}`
          : undefined,
      },
    })
    .then((response) => {
      expect(response.status).to.eq(200)
      return response
    })
})

Cypress.Commands.add('writeData', (data) => {
  const { slugArray, subPagesArray, trimmedSubPagesArray } =
    processPageSlugs(data)

  // Used to check broken links on main pages
  cy.writeFile(
    `cypress/fixtures/${strings.PARENT_PAGE_SLUGS}.json`,
    JSON.stringify(slugArray)
  )
  // Used to check broken links on sub pages
  cy.writeFile(
    `cypress/fixtures/${strings.SUB_PAGE_SLUGS}.json`,
    JSON.stringify(subPagesArray)
  )
  // Used to check webschema data on sub pages
  cy.writeFile(
    `cypress/fixtures/${strings.SEO_SUB_PAGE_SLUGS}.json`,
    JSON.stringify(trimmedSubPagesArray)
  )
  // Used to check seo metadata on pages. eg. title, description
  cy.writeFile(
    `cypress/fixtures/${strings.SEO_PAGE_SLUGS}.json`,
    JSON.stringify([...slugArray, ...trimmedSubPagesArray])
  )
})

Cypress.on('uncaught:exception', () => {
  // returning false here prevents Cypress from
  // failing the test
  return false
})

declare global {
  // biome-ignore lint/style/noNamespace: <>
  namespace Cypress {
    interface Chainable {
      /**
       * Retrieves the value of the specified environment variable.
       * - `key` - The name of the environment variable.
       */
      env(key: 'SANITY_AUTH_TOKEN'): string
      /**
       * Checks if a meta tag with the specified property attribute exists and has a non-empty content.
       * - `property` - The property attribute of the meta tag.
       */
      checkMetaTagByProperty(property: string): Chainable<Element>
      /**
       * Checks if a meta tag with the specified name attribute exists and has a non-empty content.
       * - `property` - The name attribute of the meta property
       */
      checkMetaTagByName(property: string): Chainable<Element>
      /**
       * Checks if the specified key exists in the given web schema and logs the result.
       * - `webSchema` - The web schema object.
       * - `key` - The key to check in the web schema.
       */
      checkWebSchemaProperties(
        webSchema: ContentPageWebSchemaType,
        key: string
      ): Chainable
      /**
       * Selects an UI element by the custom `data-cy` attribute if provided
       */
      getByDataID(value: TestID): Chainable<JQuery>
      /**
       * Fetches data from Sanity using the provided query.
       * - `query` - The GROQ query to execute.
       */
      fetchSanityData(query: string): Chainable
      /**
       * Writes the provided data to a fixture file.
       * - `data` - The data to write.
       */
      writeData(data: StaticPageParamData): Chainable
    }
  }
}
