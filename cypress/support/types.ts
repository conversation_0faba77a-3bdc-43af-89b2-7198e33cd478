type WebSchemaData = {
  name: string
  description: string
  '@type': string
}

type PageSlugs = Array<string>

type StaticPageParamData = {
  pageSlugs: Array<string>
  posts: {
    news: Array<string>
    research: Array<string>
    videos: Array<string>
    blog: Array<string>
  }
}

type SanityReturnType = {
  body: {
    result: StaticPageParamData
  }
}

type ContentPageWebSchemaType = {
  '@type': string
  '@context': string
  type: string
  contentUrl: string
  dateCreated: string
  dateModified: string
  description: string
  duration: string
  height: string
  name: string
  thumbnailUrl: string
  videoQuality: string
  width: string
  headline: string
  datePublished: string
  image: string
}

export type {
  ContentPageWebSchemaType,
  PageSlugs,
  SanityReturnType,
  StaticPageParamData,
  WebSchemaData,
}
