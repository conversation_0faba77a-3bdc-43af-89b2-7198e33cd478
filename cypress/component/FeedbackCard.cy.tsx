import { ToastProvider } from '../../tontine/providers/ToasterProvider'
import { FeedbackModal } from '../../tontine/sections/Feedback/FeedbackModal'
import { API } from '../../tontine/serverless/API'
import { TestingIDs } from '../support/ui-component-ids'

const mockFeedbackModalData = {
  ctaButtonLabel: { en: 'Submit Feedback' },
  failedFeedbackTitle: { en: 'Submission Failed' },
  failedFeedbackDescription: { en: 'Please try again later.' },
  feedbackModalTitle: { en: 'We value your feedback' },
  highestRatingText: { en: 'Excellent' },
  lowestRatingText: { en: 'Poor' },
  submitButtonLabel: { en: 'Submit' },
  redirectButtonLabel: { en: 'Go Back' },
  successfulFeedbackTitle: { en: 'Thank you!' },
  successfulFeedbackDescription: {
    en: 'Your feedback has been submitted successfully.',
  },
  textPlaceholder: { en: 'Enter your feedback here...' },
}

describe('Feedback Card', () => {
  it('should open Feedback Modal upon pressing card', () => {
    cy.mount(
      <ToastProvider>
        <FeedbackModal feedbackModalData={mockFeedbackModalData} />
      </ToastProvider>
    )
    cy.getByDataID(TestingIDs.feedbackCard).click({ force: true })
    cy.getByDataID(TestingIDs.feedbackModal).should('be.visible')
  })
})

describe('Feedback Modal', () => {
  const setupFeedbackModal = (
    feedbackStatusCode: number,
    feedbackResponseBody: { message?: string }
  ) => {
    const mockOnSuccessfulSubmit = cy.stub()

    cy.mount(
      <ToastProvider>
        <FeedbackModal
          feedbackModalData={mockFeedbackModalData}
          onSuccessfulSubmit={mockOnSuccessfulSubmit}
        />
      </ToastProvider>
    )

    cy.intercept('POST', API.feedback, {
      statusCode: feedbackStatusCode,
      body: feedbackResponseBody,
    }).as('sendFeedback')

    cy.getByDataID(TestingIDs.feedbackCard).click({ force: true })

    cy.getByDataID(TestingIDs.feedbackRating).click({
      force: true,
      multiple: true,
    })
    cy.getByDataID(TestingIDs.feedbackText).type(
      feedbackStatusCode === 200
        ? 'CYPRESS TEST PASSED'
        : 'CYPRESS TEST FAILED',
      {
        force: true,
      }
    )

    cy.getByDataID(TestingIDs.feedbackSubmitButton).click({ force: true })

    cy.wait('@sendFeedback')

    return mockOnSuccessfulSubmit
  }

  it('should submit feedback successfully, and call onSuccessfulSubmit function', () => {
    const mockOnSuccessfulSubmit = setupFeedbackModal(200, {})
    cy.wrap(mockOnSuccessfulSubmit).should('have.been.calledOnce')
  })

  it('should handle feedback submission failure', () => {
    const mockOnSuccessfulSubmit = setupFeedbackModal(400, {
      message: 'Bad Request',
    })
    cy.wrap(mockOnSuccessfulSubmit).should('not.have.been.called')
  })
})
