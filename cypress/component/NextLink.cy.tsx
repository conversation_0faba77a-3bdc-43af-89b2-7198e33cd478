import { NextLink } from '../../tontine/components/common/NextLink'
import { SectionEvent } from '../../tontine/types/Analytics/AnalyticsEvents.types'
import { TestingIDs } from '../support/ui-component-ids'

describe('NextLink Component', () => {
  const baseProps = {
    href: '/slug/',
    children: 'Test Link',
  }

  it('should handle external links properly', () => {
    cy.mount(<NextLink {...baseProps} href='https://external.com' />)
    cy.getByDataID(TestingIDs.nextLink)
      .should('have.attr', 'href', 'https://external.com')
      .should('have.attr', 'rel', 'nofollow noopener noreferrer')
      .should('have.attr', 'target', '_blank')
  })

  it('should add className', () => {
    cy.mount(<NextLink {...baseProps} className='test-class' />)
    cy.getByDataID(TestingIDs.nextLink).should('have.class', 'test-class')
  })

  it('should add additionalAttributes', () => {
    cy.mount(<NextLink {...baseProps} aria-label='Test link' />)
    cy.getByDataID(TestingIDs.nextLink).should(
      'have.attr',
      'aria-label',
      'Test link'
    )
  })

  it('should trigger click and hover events', () => {
    const onClickSpy = cy.spy().as('onClickSpy')
    const onHoverSpy = cy.spy().as('onHoverSpy')

    cy.mount(
      <NextLink
        {...baseProps}
        objectId={'nav_bar_main_link'}
        customEvent={SectionEvent.btn_clicked}
        trackHover={true}
        customHoverEvent={SectionEvent.btn_hovered}
      />
    )
    cy.getByDataID(TestingIDs.nextLink).invoke('on', 'mouseover', onHoverSpy)

    // Trigger hover event and check that it was called
    cy.getByDataID(TestingIDs.nextLink).trigger('mouseover')
    cy.get('@onHoverSpy').should('have.been.calledOnce')

    // Intercept the click event and prevent the default navigation
    cy.getByDataID(TestingIDs.nextLink).then(($link) => {
      $link.on('click', (e) => {
        e.preventDefault() // Prevents navigation
        onClickSpy()
      })
    })

    cy.getByDataID(TestingIDs.nextLink).click()
    cy.get('@onClickSpy').should('have.been.calledOnce')
  })

  context('Href formatting tests', () => {
    it('should render the link with a correct href', () => {
      cy.mount(<NextLink {...baseProps} />)
      cy.getByDataID(TestingIDs.nextLink).should('have.attr', 'href', '/slug/')
    })
    it('should add only forward slash to hrefs with hash', () => {
      cy.mount(<NextLink {...baseProps} href='slug/#subSlug' />)
      cy.getByDataID(TestingIDs.nextLink).should(
        'have.attr',
        'href',
        '/slug/#subSlug'
      )
    })
    it('should leave href as is if it has forward slash with hash', () => {
      cy.mount(<NextLink {...baseProps} href='/slug/#subSlug' />)
      cy.getByDataID(TestingIDs.nextLink).should(
        'have.attr',
        'href',
        '/slug/#subSlug'
      )
    })
    it('should add forward and trailing slash if href is missing them', () => {
      cy.mount(<NextLink {...baseProps} href='slug' />)
      cy.getByDataID(TestingIDs.nextLink).should('have.attr', 'href', '/slug/')
    })
    it('should add trailing slash if missing', () => {
      cy.mount(<NextLink {...baseProps} href='/slug' />)
      cy.getByDataID(TestingIDs.nextLink).should('have.attr', 'href', '/slug/')
    })
    it('should add forward slash if missing', () => {
      cy.mount(<NextLink {...baseProps} href='slug/' />)
      cy.getByDataID(TestingIDs.nextLink).should('have.attr', 'href', '/slug/')
    })
    it(`should default to '/' if href undefined`, () => {
      cy.mount(<NextLink {...baseProps} href='' />)
      cy.getByDataID(TestingIDs.nextLink).should('have.attr', 'href', '/')
    })
  })
})
