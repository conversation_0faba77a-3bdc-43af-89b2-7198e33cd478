import { CONSTANTS } from '../../../tontine/data-resource/constants'
import strings from '../../../tontine/data-resource/strings.json'
import {
  extractDimensions,
  isEmailValid,
  replaceDynamicMetrics,
  searchForOption,
} from '../../../tontine/helper-functions/UtilFunctions'

describe('function: interpolateMetricsIntoString', () => {
  it('should interpolate dynamic metrics values into the markdown string', async () => {
    const markdown =
      'Return rate is returns-BOL-USD, Annual Yield is george-USA'
    const dynamicMetrics = {
      returnRate: {
        BOL: {
          rate: CONSTANTS.TONTINE_BOL_RATE,
        },
      },
      annualYield: CONSTANTS.ANNUAL_YIELD_RATE,
    }

    expect(await replaceDynamicMetrics(markdown)).equal(
      `Return rate is ${dynamicMetrics?.returnRate?.BOL?.rate}, Annual Yield is ${dynamicMetrics?.annualYield}`
    )
  })
})

describe('function: extractDimensions', () => {
  it('returns null when the filename is empty', () => {
    const result = extractDimensions('')
    expect(result).to.equal(null)
  })

  it('extracts dimensions correctly from a valid filename', () => {
    const fileName = 'image-1920x1080.webp'
    const result = extractDimensions(fileName)
    expect(result).to.deep.equal({ width: 1920, height: 1080 })
  })

  it('returns null when the filename format is invalid', () => {
    const fileName = 'invalid-format.jpg'
    const result = extractDimensions(fileName)
    expect(result).to.equal(null)
  })
})

describe('function: isEmailValid', () => {
  it('returns true and no error message for an empty input', () => {
    const result = isEmailValid('')
    expect(result).to.deep.equal({ isValid: true, errorMessage: '' })
  })

  it('returns false and an error message for an invalid email format', () => {
    const invalidEmails = [
      'plainaddress',
      '@missingusername.com',
      'missingatsign.com',
      'missingdomain@.com',
      'missingdot@domaincom',
    ]
    invalidEmails.forEach((email) => {
      const result = isEmailValid(email)
      expect(result).to.deep.equal({
        isValid: false,
        errorMessage: strings.ERROR_EMAIL_WRONG_FORMAT,
      })
    })
  })

  it('returns true and no error message for a valid email format', () => {
    const validEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ]
    validEmails.forEach((email) => {
      const result = isEmailValid(email)
      expect(result).to.deep.equal({ isValid: true, errorMessage: '' })
    })
  })
})

describe('function: searchForOption', () => {
  it('returns an empty array when there is no search query', () => {
    const options = [{ answer: 'Option 1', tags: ['tag1'] }]
    const result = searchForOption({
      options,
      searchQuery: '',
      searchBy: ['answer'],
    })
    expect(result).to.deep.equal([])
  })

  it('returns an empty array when there are no searchBy fields', () => {
    const options = [{ answer: 'Option 1', tags: ['tag1'] }]
    const result = searchForOption({
      options,
      searchQuery: 'Option',
      searchBy: [],
    })
    expect(result).to.deep.equal([])
  })

  it('returns an empty array when options array is empty', () => {
    const result = searchForOption({
      options: [],
      searchQuery: 'Option',
      searchBy: ['answer'],
    })
    expect(result).to.deep.equal([])
  })

  it('filters options based on the search query and searchBy fields', () => {
    const options = [
      { answer: 'Option 1', tags: ['tag1', 'tag2'] },
      { answer: 'Option 2', tags: ['tag3'] },
      { answer: 'Something Else', tags: ['tag4', 'tag5'] },
    ]
    const result = searchForOption({
      options,
      searchQuery: 'Option',
      searchBy: ['answer'],
    })
    expect(result).to.deep.equal([
      { answer: 'Option 1', tags: ['tag1', 'tag2'] },
      { answer: 'Option 2', tags: ['tag3'] },
    ])
  })

  it('filters options based on the tags field', () => {
    const options = [
      { answer: 'Option 1', tags: ['tag1', 'tag2'] },
      { answer: 'Option 2', tags: ['tag3'] },
      { answer: 'Another Option', tags: ['tag4', 'tag5'] },
    ]
    const result = searchForOption({
      options,
      searchQuery: 'tag1',
      searchBy: ['tags'],
    })
    expect(result).to.deep.equal([
      { answer: 'Option 1', tags: ['tag1', 'tag2'] },
    ])
  })

  it('returns an empty array when no options match the search query', () => {
    const options = [
      { answer: 'Option 1', tags: ['tag1', 'tag2'] },
      { answer: 'Option 2', tags: ['tag3'] },
    ]
    const result = searchForOption({
      options,
      searchQuery: 'Nonexistent',
      searchBy: ['answer', 'tags'],
    })
    expect(result).to.deep.equal([])
  })
})
