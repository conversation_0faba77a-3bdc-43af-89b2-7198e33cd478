# Table of contents

- [Table of contents](#table-of-contents)
  - [Tontine websites](#tontine-websites)
  - [Getting started](#getting-started)
    - [Website](#website)
    - [Sanity Studio](#sanity-studio)
  - [Branches](#branches)
    - [Deploys previews](#deploys-previews)
    - [`staging`](#staging)
    - [`production-tontine`](#production-tontine)
  - [Deploying to production](#deploying-to-production)
    - [Deploying to `production-tontine`](#deploying-to-production-tontine)
    - [Deploying to `production-sanity`](#deploying-to-production-sanity)
  - [Build scripts](#build-scripts)
  - [Build settings](#build-settings)
  - [Scheduled Functions](#scheduled-functions)
    - [APIs](#apis)
  - [Sanity CMS](#sanity-cms)
    - [How pages and sections are built](#how-pages-and-sections-are-built)
    - [Sanity Migration](#sanity-migration)
  - [Tests](#tests)
    - [Run tests locally](#run-tests-locally)
  - [Styling with Chakra UI](#styling-with-chakra-ui)
  - [Scripts and third party scripts](#scripts-and-third-party-scripts)
  - [SEO optimizations and documentation](#seo-optimizations-and-documentation)
  - [Custom sitemaps and how we generate them](#custom-sitemaps-and-how-we-generate-them)
    - [lastModified data replacement](#lastmodified-data-replacement)
    - [Images and Videos in sitemap](#images-and-videos-in-sitemap)

## Tontine websites

Monorepo for the [Tontine website](https://tontine.com), [Sanity Studio](https://tontine-web-5e800.sanity.studio/) and other tontine-related websites. Built with [Next.js](https://nextjs.org/) and [Sanity
CMS](https://www.sanity.io/).

## Getting started

Run the following command on your local environment to start NextJS development server:

1. Clone the project
2. Run `npm setup` or if you are having problems run `npm i` from the root directory and `sanity-studio` individually

### Website

1. Run `npm run dev-tontine`, this starts the NextJS development server
2. Add an `.env.local` file in the `tontine` folder with the `SANITY_AUTH_TOKEN` key, you can grab it from [the staging-tontine-com Netlify environment variables page](https://app.netlify.com/sites/staging-tontine-com/configuration/env).
3. Open <http://localhost:3000> and the tontine.com website should render

### Sanity Studio

1. Go to `sanity-studio` folder
2. Run `npm run dev-sanity`, this starts the Sanity Studio
3. Open <http://localhost:3333> and the Sanity Studio should render

## Branches

In order to check build logs, you need to have Netlify access.

Branches with `-s-` in their name will **skip** Netlify builds but trigger a Sanity dry run, while PRs without `-s-` in their name will trigger **only** a Netlify build and automatically deploy.

### Deploys previews

Deploys previews are built against `staging`. In order to trigger a deploy
preview, you first must open a PR and a deploy preview will start automatically.

Things to be aware of:

1. Deploy preview uses `staging` env variables from Netlify
2. Every commit to an open PR rebuilds the deploy preview
3. Tests are only ran on deploy preview
4. Branches with `-s-` in their name are reserved for Sanity previews.

### `staging`

Build: [![Netlify
Status](https://api.netlify.com/api/v1/badges/7c0f4c7c-f8b7-49e0-9f8e-beffdecfb154/deploy-status)](https://app.netlify.com/sites/staging-tontine-com/deploys)

Live [staging website](https://staging-tontine-com.netlify.app/)

Development branch. Every PR deploy preview is built against this branch. Only
working and approved PRs are to be merged into this branch. **Committing
directly to this branch is NOT allowed.**.

### `production-tontine`

Build: [![Netlify
Status](https://api.netlify.com/api/v1/badges/f0567eac-c53b-4a8a-89a2-bfad88bb29e9/deploy-status)](https://app.netlify.com/sites/production-tontine/deploys)

Live [production website](https://tontine.com/)

`production-tontine` is our [production
branch](https://github.com/TontineTrust/tontine-websites/tree/production-tontine).
This branch is used to deploy to production. It should always be in sync with
the `staging` branch.

## Deploying to production

1. Make sure `staging` changes are working and there are no bugs before
   doing this.
2. Make sure your local `staging` is synched with the origin `staging`
3. Run the following command

### Deploying to `production-tontine`

Run the following command to push all the changes from staging to production-tontine, which triggers a production build:

```shell
git push origin staging:production-tontine
```

This pushes all the changes from `staging` to `production-tontine` of
triggers a production build.

### Deploying to `production-sanity`

Run the following command to push all the changes from staging to production-sanity:

```shell
git push origin staging:production-sanity
```

This pushes all the changes from `staging` to `production-sanity`.

## Build scripts

`dull-build` script is used for building the project without fetching new images data from Sanity. This approach is useful when you want a faster build process without updating the image assets.
`build-tontine` script provides a full, updated build suitable for production deployment.

## Build settings

Located in the project's root `netlify.toml` controls any build settings
necessary for Netlify to build the websites. Plugins should only be installed
via `netlify.toml` and be pinned to the latest working version.

Updating the next js plugin for Netlify can cause some unwanted issues and can
break the website easily. So that should be only updated with a prior discussion
with everyone involved with the website's repo.

Additional details and docs are in `netlify.toml`

## Scheduled Functions

Located in `netlify/functions`

- **`daily-rebuild`**
  - **Purpose**: This function is designed to initiate a daily rebuild of the production site. It checks if a build has occurred in the last 24 hours and if not, it triggers a new build.
  - **Process**: The function first logs that it has been initiated. It then retrieves the `build hook`, `site ID`, and `API key` from the environment variables. If any of these are not available, it returns an error message, else it attempts to get information about previous builds. If a build has occurred in the last 24 hours, it aborts the process. Otherwise, it tries to initiate a new build.
  - **Invocation**: This function gets automatically called at a set time based on the configuration in the `netlify.toml` file. [CRON](https://en.wikipedia.org/wiki/Cron) is used to set the exact time.

### APIs

These are in-house APIs that get deployed to Netlify functions.

Located in `app/api/`

- **`OneSignal` Endpoint**

  - **Purpose**: This endpoint is used for sending a confirmation email to the user who submitted the Live Contact Us form.
  - **Process**: Before a submission is allowed, we first use silent Cloudflare turnstile to verify if a user is a bot. If the user is determined to not be a bot the api call will execute.
  - **Bot Detection**: If a user is detected to be a bot or suspicious activity is detected, we do not allow a submission but we offer an alternative way to contact using `mailto:`. Check `ContactUsSection.tsx` for the frontend implementation.

- **`draft` Endpoint**

  - **Purpose**: Used in Sanity Studio only to preview how a page would look like if the content was published.

- **Util Functions**

  - `filterStackTrace` function that cleans up error stack traces by removing extraneous lines that do not contribute to debugging, specifically those originating from within the node_modules directory of the `tontine-websites` project.

## Sanity CMS

**Important!**

**Sanity CMS at the moment only uses `production` dataset, meaning that if you
run Sanity Studio locally and you publish a document, it will go directly to
the production tontine.com website!**

The website content comes from our Sanity CMS. The Sanity CMS Studio live
version can be found at the [Sanity Studio Structure page](https://tontine-web-5e800.sanity.studio/structure).

You need access to Sanity Studio in order to make changes.

### How pages and sections are built

Data about pages and page sections is pulled from our Sanity CMS via GROQ
queries and NextJS uses SSG to generate website pages and sections.

Sections order and which Sanity section is mapped to NextJS component is located
in `tontine/components/sections/Sections.ts` and more info can be found in
`Sections.ts`

### Sanity Migration

You can migrate document and field data with `sanity/migrate`
An example document can be found in `sanity-studio/migrations/replace-[name]`

Once you have defined your content migrations as code and made sure they are nicely organized in a migrations folder in your Studio project, that is, alongside your sanity.cli.ts configuration, then you’re all set up for a test run.

To quickly list out what migrations the tool can access, run sanity migration list.

Now you can copy-paste the content migration ID and run `npx sanity migration run <migration file name>`

The command will always run in **dry run** mode; unless you add the `--no-dry-run` flag.

## Tests

All page slugs are fetched from Sanity and stored in the `cypress/fixtures` folder

List of E2E tests:

- Check for broken links in the navigation bar, links on all pages and the footer
- SEO metadata validation on all pages
- Web schema validation on home page, faq page, content pages and all of their sub pages

There are also unit tests for most util functions.

### Run tests locally

The tests require a Sanity auth token named `SANITY_AUTH_TOKEN` located at root level in `cypress.env.json` to be able to work.

- To run the cypress gui for more informational tests do:

```shell
npm run e2e
```

- To run the headless tests do:

```shell
npm run e2e:headless
```

## Styling with Chakra UI

Styling is done with the Chakra UI library. For more information regarding getting
started with Chakra UI and its components, hooks, and styled system you can refer
to their official [webpage](https://chakra-ui.com/).

## Scripts and third party scripts

All scripts live in `tontine/components/scripts`

When using a script it should be only used with the NextJS's dedicated `<Script
/>` component. **Scripts should not be injected via Netlify.**

A list of third-party scripts is maintained in the [third-party scripts list](https://3.basecamp.com/5235135/buckets/25036556/messages/6864675368)
for developers and management to have an overview of which scripts are being
used.

## SEO optimizations and documentation

Below, we outline the optimizations and improvements we've implemented to enhance SEO (Search Engine Optimization) performance. These changes are designed to boost visibility and improve the ranking of our website in search engine results (prioritizing Google).

For more information about SEO, please refer to our [SEO Observations and Documentation](https://docs.google.com/document/d/1gUe9qzAWQfyi5ErQHVSI8ZN-KEXh4wilFxMUYwzn4uY/edit#heading=h.afqkocsq9nwa).

## Custom sitemaps and how we generate them

List of improvements to the sitemap we've made:

- Add image, video, and news tags to referenced URLs
- Include PDF links
- Update the `lastModified` tag data with the `_updatedAt` data from Sanity

### lastModified data replacement

`lastModified` data is important for crawling because Google will not re-crawl a URL that has not been updated ([XML Sitemap Google](https://developers.google.com/search/docs/crawling-indexing/sitemaps/build-sitemap#xml)).

To achieve this, we fetch each document using a slug along with its `_updatedAt` property.

### Images and Videos in sitemap

[Google docs for image sitemaps](https://developers.google.com/search/docs/crawling-indexing/sitemaps/image-sitemaps)  
[Google docs for video sitemaps](https://developers.google.com/search/docs/crawling-indexing/sitemaps/video-sitemaps)

Images and videos are included in the sitemap by adding `<image:image>` and `<video:video>` tags, respectively, to each URL entry that contains relevant media. This ensures that search engines can discover and index all images and videos associated with a given page, improving the visibility of both visual and video content in search results.

- **Images:** For each page that contains images, `<image:image>` tags are added to the sitemap entry for that page.
- **Videos:** For each page that contains videos, `<video:video>` tags are added to the sitemap entry for that page.

This approach helps search engines better understand and surface your site's media content in search results.
