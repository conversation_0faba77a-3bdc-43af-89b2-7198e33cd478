#!/bin/bash

DIRECTORY="tontine/.next"

handle_error() {
    echo "An error occurred: $1"
    exit 1
}

find "$DIRECTORY" -name "*.css.map" -type f -delete || handle_error "Failed to delete CSS source maps"

find "$DIRECTORY" -type f -name '*.css' -exec sed -i -E 's/sourceMappingURL=[^ ]*\.css\.map//g' {} + || handle_error "Failed to remove sourceMappingURL references from CSS files"

echo "CSS source maps and their references have been deleted."
