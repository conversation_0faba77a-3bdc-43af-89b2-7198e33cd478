import { getImageDimensions } from '@sanity/asset-utils'
import { useEffect, useState } from 'react'
import {
  type FormPatch,
  type PatchEvent,
  set,
  unset,
  useFormValue,
} from 'sanity'

import { SANITY_CONSTANTS } from '../assets/constants'
import { convertImageIdToUrl } from '../schemas/util-functions/utilFunctions'
import type { Doc, WalkthroughPinPlacementType } from '../types/customTypes'
import { useSanityFetch } from './useSanityFetch'

const { MOBILE_SIZE } = SANITY_CONSTANTS

/** A hook that fetches the image for the walkthrough section, given as `mobileSection`
 * and sets the title of the section, whether it's a mobile image, and the image dimensions.
 */
const useFetchSectionImage = (mobileSection?: boolean) => {
  const [sectionImageUrl, setSectionImageUrl] = useState<string | null>(null)
  const [sectionTitle, setSectionTitle] = useState<string | null>(null)
  const [isMobile, setIsMobile] = useState(false)
  const [imageSizes, setImageSizes] = useState<{
    width?: number
    height?: number
  } | null>(null)

  const document = useFormValue([]) as Doc
  const sectionRef = mobileSection
    ? document?.walkthroughMobile?._ref
    : document?.walkthroughDesktop?._ref

  const sectionQuery = '*[_id == $id][0]'
  const sectionParams = { id: sectionRef }
  const { data: section } = useSanityFetch<Doc>(sectionQuery, sectionParams)

  useEffect(() => {
    if (section) {
      setSectionTitle(section?.title ?? 'the section')

      const imageRef = section?.walkthroughGridImage?.asset?._ref
      if (imageRef) {
        const imageUrl = convertImageIdToUrl(imageRef)
        setSectionImageUrl(imageUrl)

        if (imageUrl) {
          const { width, height } = getImageDimensions(imageRef)
          setImageSizes({ width, height })
          setIsMobile(
            width === MOBILE_SIZE.width && height === MOBILE_SIZE.height
          )
        }
      }
    }
  }, [section])
  return { sectionImageUrl, sectionTitle, isMobile, imageSizes }
}

/** Hook that returns values and functions for handling the placement of a pin
 * in the walkthrough section.
 */
const usePlacementInputs = ({
  value,
  onChange,
}: {
  value?: WalkthroughPinPlacementType
  onChange: (patches: FormPatch | Array<FormPatch> | PatchEvent) => void
}) => {
  const [inputs, setInputs] = useState<WalkthroughPinPlacementType>(
    value ?? { column: 1, row: 1 }
  )
  const [isModalOpen, setIsModalOpen] = useState(false)

  useEffect(() => {
    if (value) {
      setInputs(value)
    }
  }, [value])

  const handleChange = (
    field: keyof WalkthroughPinPlacementType,
    val: number
  ) => {
    const newValues = { ...inputs, [field]: Math.max(1, val) }
    setInputs(newValues)
    onChange(newValues.column && newValues.row ? set(newValues) : unset())
  }

  const handleCellClick = (row: number, col: number) => {
    const newValues: WalkthroughPinPlacementType = { row, column: col }
    setInputs(newValues)
    onChange(set(newValues))
    setIsModalOpen(false)
  }

  return { inputs, isModalOpen, setIsModalOpen, handleChange, handleCellClick }
}

export { useFetchSectionImage, usePlacementInputs }
