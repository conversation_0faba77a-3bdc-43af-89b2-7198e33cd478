import { useEffect, useRef, useState } from 'react'
import { type QueryParams, useClient } from 'sanity'

import { SANITY_CONSTANTS } from '../assets/constants'

/** Custom hook to fetch data from Sanity. */
export const useSanityFetch = <T>(query: string, params?: QueryParams) => {
  const client = useClient({ apiVersion: SANITY_CONSTANTS.API_VERSION })
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState<boolean>(true)

  // Using refs to track query and params
  const queryRef = useRef(query)
  const paramsRef = useRef(params)

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      try {
        const result = await client.fetch<T>(
          queryRef.current,
          paramsRef.current ?? {}
        )
        setData(result)
      } catch (error) {
        // biome-ignore lint/suspicious/noConsole: <explanation>
        console.error('TONTINE_ERROR: Failed to fetching data:', error)
        setData(null)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [client])

  // Update refs if query or params change
  useEffect(() => {
    queryRef.current = query
    paramsRef.current = params
  }, [query, params])

  return { data, loading }
}
