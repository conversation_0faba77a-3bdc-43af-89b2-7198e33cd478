import { BookIcon, EmptyIcon } from '@sanity/icons'

import { SANITY_CONSTANTS } from '../assets/constants'
import CustomImage from '../components/CustomImage'
import { CircleFlagsEarth } from '../components/Icons'
import { cleanDomain } from '../schemas/util-functions/utilFunctions'
import type { StructureToolTypes } from '../types/customTypes'

/** Fetches all websites with their respective pages from Sanity.
 *
 * The query uses the `drafts` perspective to fetch the latest draft of each
 * document. The `apiVersion` is set to a recent version to ensure that the query works
 * with the latest features.
 *
 * The query fetches the `websiteTitle`, `_id`, `websiteFavicon` (if it exists) and
 * `homepage` (if it exists) for each website. If `websiteFavicon` does not exist,
 * it uses the `seoImage` of the homepage instead.
 *
 * It then flattens the `pagesOnWebsite` array for each website and extracts the
 * `pageId` from each item. If the same `pageId` appears in multiple websites, it
 * is added to the `duplicatePageIds` array.
 */
const fetchWebsitePages = async ({
  context,
  documentId,
}: {
  context: StructureToolTypes['context']
  documentId: string
}) => {
  const client = context.getClient({
    apiVersion: SANITY_CONSTANTS.API_VERSION,
    perspective: 'drafts',
  })

  const data = await client.fetch<
    Array<{
      id: string
      title: string
      domain: string
      icon: string
      pagesOnWebsite: Array<string>
    }>
  >(
    `*[_type == 'website']{
      "title": websiteTitle, 
      "domain": pageDomain,
      "id": _id, 
      "icon": coalesce(websiteFavicon.asset->url, homepage->.pageSeo.seoImage.asset->url), 
      "pagesOnWebsite": array::compact([...pagesOnWebsite[]->_id, homepage->._id]),
    }`,
    { documentId }
  )

  const duplicatePageIds: Array<string> = []
  const pageIds: Array<string> = []

  data?.forEach((website) => {
    website?.pagesOnWebsite?.forEach((pageId) => {
      if (pageIds.includes(pageId)) {
        duplicatePageIds.push(pageId)
      } else {
        pageIds.push(pageId)
      }
    })
  })

  return { data, duplicatePageIds, pageIds }
}

/** Builds the structure for the "Pages" section in the Tontine Trust CMS.
 *
 * This function constructs a custom list of pages using the provided
 * `StructureToolTypes.Build` instance and context. It organizes the pages
 * into several categories:
 *
 * - "All Pages": Displays all pages available on the website.
 * - "Orphan": Lists pages that are not associated with any website.
 * - "Common": Shows pages that are linked to multiple websites.
 * - Individual websites: Displays pages specific to each website.
 *
 * The function fetches data using `fetchWebsitePages`, which provides
 * information on websites and their associated pages. The pages are
 * categorized based on their presence in multiple websites or lack thereof.
 */
export const buildPageStructure = async (
  Build: StructureToolTypes['Build'],
  context: StructureToolTypes['context'],
  documentId: string
) => {
  const { data, duplicatePageIds, pageIds } = await fetchWebsitePages({
    context,
    documentId,
  })

  const pageType = 'page'

  return Build.list()
    .title('Pages')
    .items([
      Build.listItem()
        .title('All Pages')
        .icon(BookIcon)
        .child(Build.documentTypeList(pageType).title('All Pages on website')),
      Build.listItem()
        .title('Orphan')
        .icon(EmptyIcon)
        .child(
          Build.documentList()
            .title('Pages not in any website')
            .apiVersion(SANITY_CONSTANTS.API_VERSION)
            .filter(
              `_type == $pageType && !(_id in $pageIds) && !(_id in path("drafts.**"))`
            )
            .params({ pageIds, pageType })
        ),
      Build.listItem()
        .title('Common')
        .icon(CircleFlagsEarth)
        .child(
          Build.documentList()
            .title('Pages in multiple websites')
            .apiVersion(SANITY_CONSTANTS.API_VERSION)
            .filter('_type == $pageType && _id in $duplicatePageIds')
            .params({ duplicatePageIds, pageType })
        ),
      ...data.flatMap((website) => [
        Build.listItem()
          .title(cleanDomain(website?.domain))
          // .subtitle(website?.domain) // Add subtitle here
          .id(website?.id)
          .icon(() => CustomImage(website?.icon))
          .child(
            Build.documentList()
              .title(`${website?.title} pages`)
              .apiVersion(SANITY_CONSTANTS.API_VERSION)
              .filter('_type == $pageType && _id in $pageIds')
              .params({
                pageIds: website?.pagesOnWebsite,
                pageType,
              })
          ),
      ]),
    ])
}
