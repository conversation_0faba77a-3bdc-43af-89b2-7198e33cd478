import {
  BlockContentIcon,
  BlockElementIcon,
  BookIcon,
  ChartUpwardIcon,
  CommentIcon,
  DashboardIcon,
  DocumentTextIcon,
  DownloadIcon,
  EnvelopeIcon,
  HeartIcon,
  HelpCircleIcon,
  InfoOutlineIcon,
  InlineElementIcon,
  LaunchIcon,
  MasterDetailIcon,
  PinIcon,
  PlayIcon,
  RocketIcon,
  SplitVerticalIcon,
  UsersIcon,
} from '@sanity/icons'

import type { ChildSchemaType, StructureToolTypes } from '../types/customTypes'

export const sectionSchemas: Array<ChildSchemaType> = [
  {
    title: 'About us section',
    schemaType: 'aboutUsSection',
    icon: CommentIcon,
  },
  {
    title: 'Carousel hero section',
    schemaType: 'carouselHeroSection',
    icon: InlineElementIcon,
  },
  {
    title: 'Carousel section',
    schemaType: 'carouselSection',
    icon: InlineElementIcon,
  },
  {
    title: 'Companies Logos',
    schemaType: 'companiesLogosSection',
    icon: BlockElementIcon,
  },
  {
    title: 'Contact us section',
    schemaType: 'contactUsSection',
    icon: EnvelopeIcon,
  },
  { title: 'CTA card', schemaType: 'ctaCard', icon: RocketIcon },
  { title: 'CTA section', schemaType: 'ctaSection', icon: LaunchIcon },
  {
    title: 'Download section',
    schemaType: 'downloadSection',
    icon: DownloadIcon,
  },
  { title: 'FAQ section', schemaType: 'faqSection', icon: HelpCircleIcon },
  {
    title: 'Featured section',
    schemaType: 'featuredSection',
    icon: SplitVerticalIcon,
  },
  {
    title: 'Glossary section',
    schemaType: 'glossarySection',
    icon: BookIcon,
  },
  { title: 'Hero section', schemaType: 'heroSection', icon: DashboardIcon },
  {
    title: 'Info banner section',
    schemaType: 'infoBannerSection',
    icon: InfoOutlineIcon,
  },
  {
    title: 'Info block section',
    schemaType: 'infoBlockSection',
    icon: DocumentTextIcon,
  },
  {
    title: 'Info hub section',
    schemaType: 'infoHubSection',
    icon: BlockElementIcon,
  },
  {
    title: 'Markdown section',
    schemaType: 'markdownSection',
    icon: BlockContentIcon,
  },
  { title: 'Partners section', schemaType: 'partnersSection', icon: UsersIcon },
  { title: 'Referral section', schemaType: 'referralSection', icon: UsersIcon },
  { title: 'Share section', schemaType: 'shareSection', icon: HeartIcon },
  {
    title: 'Social media section',
    schemaType: 'socialMediaSection',
    icon: HeartIcon,
  },
  { title: 'Team section', schemaType: 'teamSection', icon: UsersIcon },
  {
    title: 'Testimonials Section',
    schemaType: 'testimonialSection',
    icon: CommentIcon,
  },
  {
    title: 'Tontinator section',
    schemaType: 'tontinatorSection',
    icon: ChartUpwardIcon,
  },
  {
    title: 'Videos section',
    schemaType: 'videosSection',
    icon: PlayIcon,
  },
]

export const allSectionTypes = sectionSchemas
  .map((section) => {
    return { type: section.schemaType }
  })
  .concat([
    {
      type: 'contentSection',
    },
    {
      type: 'walkthroughSection',
    },
  ])

/** A structure tool that generates a list of all section schemas.
 *
 * It creates a custom menu with the following items:
 *
 * - Section Types: A list of all section schemas, each as a separate item.
 * - Walkthrough: A master-detail list with the following items:
 *   - Walkthrough Section: A list of all Walkthrough Section documents.
 *   - Grid: A list of all Walkthrough Grid documents.
 *   - Pin: A list of all Walkthrough Pin documents.
 */
export const buildSectionsStructure = (Build: StructureToolTypes['Build']) =>
  Build.list()
    .title('Section Types')
    .items([
      ...sectionSchemas.map((item) => {
        return Build.listItem()
          .icon(item.icon)
          .title(item.title)
          .child(Build.documentTypeList(item.schemaType))
      }),
      Build.divider(),
      Build.listItem()
        .title('Walkthrough')
        .icon(MasterDetailIcon)
        .child(
          Build.list()
            .title('Walkthrough')
            .items([
              Build.listItem()
                .title('Walkthrough Section')
                .schemaType('walkthroughSection')
                .child(
                  Build.documentTypeList('walkthroughSection').title('Sections')
                ),
              Build.listItem()
                .title('Grid')
                .icon(DashboardIcon)
                .schemaType('walkthroughGrid')
                .child(
                  Build.documentTypeList('walkthroughGrid').title('Grids')
                ),
              Build.listItem()
                .title('Pin')
                .icon(PinIcon)
                .schemaType('walkthroughPin')
                .child(Build.documentTypeList('walkthroughPin').title('Pins')),
            ])
        ),
    ])
