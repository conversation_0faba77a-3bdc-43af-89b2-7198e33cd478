import {
  BlockContentIcon,
  BlockElementIcon,
  BookIcon,
  DocumentTextIcon,
  DocumentVideoIcon,
  EarthGlobeIcon,
  MasterDetailIcon,
  MenuIcon,
  SearchIcon,
} from '@sanity/icons'

import type { StructureToolTypes } from '../types/customTypes'
import { buildMiscellaneousStructure } from './misc-structure'
import { buildPageStructure } from './page-structure'
import { buildSectionsStructure } from './sections-structure'

// Content Post section and documents schemas
const contentPostSchemas = [
  {
    title: 'Content Sections',
    schemaType: 'contentSection',
    icon: BlockElementIcon,
  },
  { title: 'Blog', schemaType: 'blogPost', icon: BlockContentIcon },
  { title: 'News', schemaType: 'newsPost', icon: DocumentTextIcon },
  { title: 'Research', schemaType: 'researchPost', icon: SearchIcon },
  { title: 'Videos', schemaType: 'videoPost', icon: DocumentVideoIcon },
]

/**
 * Custom structure tool for the Tontine Trust CMS.
 *
 * This structure tool creates a custom menu with the following items:
 *
 * - Website: A list of all websites
 * - Pages: A list of all pages. Pages are categorized based on usage.
 * - Navigation Menu: A list of all navigation menus
 * - Sections: A list of all content sections
 * - Footer: A list of all footers
 * - Content Sections: A list of all content sections and separate Lists for all content post types
 * - Miscellaneous: A list of all miscellaneous documents alongside webschemas for website
 */
export const customStructureTool = (
  Build: StructureToolTypes['Build'],
  context: StructureToolTypes['context']
) =>
  Build.list()
    .title('Tontine Trust CMS')
    .items([
      Build.listItem()
        .icon(EarthGlobeIcon)
        .title('Website')
        .child(Build.documentTypeList('website')),

      Build.listItem()
        .icon(BookIcon)
        .title('Pages')
        .child(async (documentId: string) =>
          buildPageStructure(Build, context, documentId)
        ),

      Build.divider(),

      Build.listItem()
        .icon(MenuIcon)
        .title('Navigation Menu')
        .child(Build.documentTypeList('navigationMenu')),

      Build.listItem()
        .icon(BlockElementIcon)
        .title('Sections')
        .child(buildSectionsStructure(Build)),

      Build.listItem()
        .icon(MenuIcon)
        .title('Footer')
        .child(Build.documentTypeList('footer')),

      Build.divider(),

      ...contentPostSchemas.map((item) =>
        Build.listItem()
          .icon(item.icon)
          .title(item.title)
          .child(Build.documentTypeList(item.schemaType))
      ),

      Build.divider(),

      Build.listItem()
        .icon(MasterDetailIcon)
        .title('Miscellaneous')
        .child(buildMiscellaneousStructure(Build)),
    ])
