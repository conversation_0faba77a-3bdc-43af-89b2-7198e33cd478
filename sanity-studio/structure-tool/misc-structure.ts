import {
  BillIcon,
  BookIcon,
  CaseIcon,
  DashboardIcon,
  DocumentSheetIcon,
  EllipsisVerticalIcon,
  InfoOutlineIcon,
  LinkIcon,
  MasterDetailIcon,
  PinIcon,
  UnknownIcon,
  UserIcon,
  UsersIcon,
} from '@sanity/icons'

import { ButtonIcon } from '../components/Icons'
import type { ChildSchemaType, StructureToolTypes } from '../types/customTypes'

const webSchemas: Array<ChildSchemaType> = [
  {
    title: 'Corporation',
    schemaType: 'corporationSchema',
    icon: BillIcon,
  },
  {
    title: 'Investment or Deposit',
    schemaType: 'investmentOrDepositSchema',
    icon: CaseIcon,
  },
]

const miscellaneousSchemas: Array<ChildSchemaType> = [
  {
    title: 'Button custom type',
    schemaType: 'buttonCustomType',
    icon: ButtonIcon,
  },
  {
    title: 'Glossary card',
    schemaType: 'glossary',
    icon: BookIcon,
  },
  {
    title: 'Image custom type',
    schemaType: 'customImage',
    icon: DocumentSheetIcon,
  },
  { title: 'Info Block', schemaType: 'infoBlock', icon: InfoOutlineIcon },
  { title: 'Link custom type', schemaType: 'linkCustomType', icon: LinkIcon },
  {
    title: 'Navigation Item',
    schemaType: 'navigationItem',
    icon: MasterDetailIcon,
  },
  { title: 'Partner', schemaType: 'partner', icon: UserIcon },
  { title: 'Person', schemaType: 'person', icon: UserIcon },
  {
    title: 'Questions and Answers',
    schemaType: 'questionAndAnswer',
    icon: UnknownIcon,
  },
  { title: 'Social Link', schemaType: 'socialLink', icon: LinkIcon },
  {
    title: 'SubMenu Item',
    schemaType: 'subMenuItem',
    icon: EllipsisVerticalIcon,
  },
  {
    title: 'Feedback Modal',
    schemaType: 'feedbackModal',
    icon: UsersIcon,
  },
  {
    title: 'Walkthrough Pin',
    schemaType: 'walkthroughPin',
    icon: PinIcon,
  },
  {
    title: 'Walkthrough Grid',
    schemaType: 'walkthroughGrid',
    icon: DashboardIcon,
  },
]

/** Custom structure tool for miscellaneous schema types.
 *
 * This structure tool creates a custom menu with the following items:
 *
 * - Miscellaneous Schema Types: A list of all miscellaneous schema types.
 * - Webschemas: A list of all webschemas.
 */
export const buildMiscellaneousStructure = (
  Build: StructureToolTypes['Build']
) =>
  Build.list()
    .title('Miscellaneous Schema Types')
    .items([
      ...miscellaneousSchemas.map((item) => {
        return Build.listItem()
          .icon(item.icon)
          .title(item.title)
          .child(Build.documentTypeList(item.schemaType))
      }),
      Build.listItem()
        .icon(BookIcon)
        .title('Webschemas')
        .child(
          Build.list()
            .title('Webschemas')
            .items(
              webSchemas.map((item) => {
                return Build.listItem()
                  .icon(item.icon)
                  .title(item.title)
                  .child(Build.documentTypeList(item.schemaType))
              })
            )
        ),
    ])
