import { validationRules } from '../validation'
import { headerSchema<PERSON>ields } from './headerFields'

const Partner = {
  // Schema that represents single partner from partners in tontine.com/about-us#partners
  name: 'partner',
  title: 'Partner',
  type: 'document',
  preview: {
    select: {
      title: 'partnerName',
      media: 'partnerImage',
      subtitle: 'partnerExternalLink',
    },
  },
  fields: [
    ...headerSchemaFields({
      title: `Partner's name`,
      description: 'The name of the partner organization or company.',
      type: 'stringTitle',
      subtitle: {
        showSubtitle: true,
        type: 'richTextContentLite',
        title: `Partner's description`,
        description:
          'A brief description of the partner, including what the partner does or represents.',
      },
    }),
    {
      // Field for partner's image
      name: 'partnerImage',
      title: `Partner's image`,
      type: 'image',
      description: `An image that represents the partner's company or organization.`,
      validation: validationRules.imageValidation,
      options: {
        hotspot: true,
      },
    },
    {
      // Field of type url that represent's partners website
      name: 'partnerExternalLink',
      title: `Partner's website link`,
      type: 'url',
      description: `The URL of the partner's official website.`,
    },
  ],
}

export default Partner
