import type { Rule } from 'sanity'

import { SANITY_CONSTANTS } from '../../assets/constants'
import { WrappedField } from '../../components/WrappedField'
import type { ComponentProps } from '../../types/customTypes'
import { validationRules } from '../validation'

const { SUPPORTED_LANGUAGES } = SANITY_CONSTANTS

const createLocalizedFieldType = ({
  name,
  type,
  title,
  validation,
  fieldWrapperClass,
  fieldSetWrapperClass,
  isLite,
  of,
  allRequired,
}: {
  name: string
  type: string
  title?: string
  validation?: (rule: Rule, isRequired?: boolean) => Array<Rule> | Rule
  fieldWrapperClass?: string
  fieldSetWrapperClass?: string
  isLite?: boolean
  of?: Array<{ type: string }>
  allRequired?: boolean
}) => ({
  title: title || `Localized ${type?.replace(/([A-Z])/g, ' $1').toLowerCase()}`,
  name,
  type: 'object',
  components: {
    field: (props: ComponentProps) =>
      WrappedField({
        customClass: fieldSetWrapperClass ?? 'locale-fieldset',
        props,
      }),
  },
  fieldsets: [
    {
      title: 'Translations',
      name: 'translations',
      options: { collapsible: true, collapsed: true },
    },
  ],

  fields: SUPPORTED_LANGUAGES.map((lang) => ({
    title: lang.title,
    name: lang.id,
    type,
    ...(of ? { of } : {}), // Must be like this since if it's undefined it will throw an error
    fieldset: lang.isDefault ? null : 'translations',
    validation: (rule: Rule) =>
      validation?.(rule, allRequired ?? lang.isDefault),
    components: {
      //TODO: Use TranslateFieldWrapper here when AI ready
      input: (props: ComponentProps) =>
        WrappedField({
          customClass: isLite ? 'rich-text-lite' : fieldWrapperClass,
          props,
        }),
    },
  })),
})

function generateRichTextFields(fieldConfig: typeof fieldsConfig) {
  const generatedFields: {
    [key: string]: ReturnType<typeof createLocalizedFieldType>
  } = {}
  fieldConfig.forEach(({ name, validation }) => {
    const capitalized = name.charAt(0).toUpperCase() + name.slice(1)

    /* Only english required fields */

    // Regular string
    generatedFields[`string${capitalized}`] = createLocalizedFieldType({
      name: `string${capitalized}`,
      validation,
      type: 'string',
    })

    // Regular Rich Text
    generatedFields[`richText${capitalized}`] = createLocalizedFieldType({
      name: `richText${capitalized}`,
      validation,
      type: 'richText',
      fieldWrapperClass: 'locale-richtext',
      fieldSetWrapperClass: 'locale--fieldset-richtext',
    })

    // Rich Text Lite
    generatedFields[`richText${capitalized}Lite`] = createLocalizedFieldType({
      name: `richText${capitalized}Lite`,
      validation,
      type: 'richTextLite',
      fieldWrapperClass: 'locale-richtext',
      fieldSetWrapperClass: 'locale--fieldset-richtext',
      isLite: true,
    })

    /* All required fields */
    // TODO: Should remove these when everything is translated adn make normal ones required

    // Regular string
    generatedFields[`sr${capitalized}`] = createLocalizedFieldType({
      name: `sr${capitalized}`,
      validation,
      type: 'string',
      // allRequired: true,
    })

    // Rich Text
    generatedFields[`rtr${capitalized}`] = createLocalizedFieldType({
      name: `rtr${capitalized}`,
      validation,
      type: 'richText',
      fieldWrapperClass: 'locale-richtext',
      fieldSetWrapperClass: 'locale--fieldset-richtext',
      // allRequired: true,
    })

    // Rich Text Lite
    generatedFields[`rtr${capitalized}Lite`] = createLocalizedFieldType({
      name: `rtr${capitalized}Lite`,
      validation,
      type: 'richTextLite',
      fieldWrapperClass: 'locale-richtext',
      fieldSetWrapperClass: 'locale--fieldset-richtext',
      isLite: true,
      // allRequired: true,
    })

    // Regular Rich Text version
    generatedFields[`rth${capitalized}`] = createLocalizedFieldType({
      name: `rth${capitalized}`,
      validation,
      type: 'richTextHeader',
      fieldWrapperClass: 'locale-richtext rth',
      fieldSetWrapperClass: 'locale--fieldset-richtext rth',
    })

    // Rich Text Title Lite version
    generatedFields[`rth${capitalized}Lite`] = createLocalizedFieldType({
      name: `rth${capitalized}Lite`,
      validation,
      type: 'richTextHeaderLite',
      fieldWrapperClass: 'locale-richtext rth',
      fieldSetWrapperClass: 'locale--fieldset-richtext rth',
      isLite: true,
    })
  })

  return generatedFields
}

const fieldsConfig = [
  { name: 'title', validation: validationRules.titleValidation },
  { name: 'subtitle', validation: validationRules.subtitleValidation },
  { name: 'longTitle', validation: validationRules.longTitleValidation },
  { name: 'longSubtitle', validation: validationRules.longSubtitleValidation },
  { name: 'midTitle', validation: validationRules.midTitleValidation },
  { name: 'midSubtitle', validation: validationRules.midSubtitleValidation },
  { name: 'shortTitle', validation: validationRules.shortTitleValidation },
  {
    name: 'shortSubtitle',
    validation: validationRules.shortSubtitleValidation,
  },
  { name: 'content', validation: validationRules.contentValidation },
  { name: 'unlimited', validation: undefined },
] as const

const textFields = generateRichTextFields(fieldsConfig)

const seoKeywords = createLocalizedFieldType({
  name: 'seoKeywords',
  type: 'array',
  of: [{ type: 'string' }],
  // validation: validationRules.isRequired,
  // allRequired: true, // No need for this here since isRequired, but we need to check out if we need special validation for keywords then it will be needed
})

export { fieldsConfig, seoKeywords, textFields }
