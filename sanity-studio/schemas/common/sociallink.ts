import { LinkIcon } from '@sanity/icons'

import { validationRules } from '../validation'
import { headerSchemaFields, internalUseTitle } from './headerFields'

const SocialLink = {
  // Social media links in a form of clickable icons
  name: 'socialLink',
  title: 'Social Link',
  type: 'document',
  icon: LinkIcon,
  preview: {
    select: {
      title: 'url',
      subtitle: 'title',
      media: 'icon',
    },
  },
  fields: [
    internalUseTitle,
    ...headerSchemaFields({
      title: false,
      icon: {
        showIcon: true,
        title: 'Social media icon',
        description:
          'An image that represents the icon for a social media platform.',
        validation: validationRules.imageValidation,
      },
    }),
    {
      // Field for external social media link
      title: 'Social media URL',
      name: 'url',
      type: 'url',
      description: 'The URL of the social media page.',
      validation: validationRules.isRequired,
    },
  ],
}

export default SocialLink
