import { createSectionPreview } from '../util-functions/utilFunctions'
import { headerSchemaFields } from './headerFields'

export const linkWithImage = {
  name: 'linkWithImage',
  title: 'Link with Image',
  type: 'document',
  preview: createSectionPreview(),
  fields: [
    ...headerSchemaFields({
      title: 'Title',
      description: 'Title for internal use',
    }),
    {
      name: 'href',
      title: 'Link url',
      type: 'url',
      description: 'The URL for the link.',
    },
    {
      name: 'linkImage',
      title: 'Image',
      type: 'image',
      options: {
        hotspot: true,
      },
    },
    {
      name: 'comingSoon',
      title: 'Coming Soon',
      type: 'boolean',
      initialValue: false,
      description: 'Toggle to display the "Coming Soon" badge on the image.',
    },
  ],
}
