import { validationRules } from '../validation'

const CustomImage = {
  // Custom image schema
  name: 'customImage',
  title: 'Image Schema',
  type: 'document',
  preview: {
    select: {
      title: 'schemaTitle',
      media: 'imageField',
    },
  },
  fields: [
    {
      // Place for schema title
      title: 'Schema title for preview',
      name: 'schemaTitle',
      type: 'string',
      description:
        'The title of the schema. This will be displayed in the Sanity preview.',
      validation: validationRules.isRequired,
    },
    {
      // Field for image
      title: 'Image',
      name: 'imageField',
      type: 'image',
      description: 'A field for adding an image.',
      validation: validationRules.imageValidation,
      options: {
        hotspot: true,
      },
    },
  ],
}

export default CustomImage
