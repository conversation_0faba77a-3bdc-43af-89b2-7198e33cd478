import type { Rule } from 'sanity'
import { Disclaimer } from '../../components/Disclaimer'
import SectionImageRoundedCorners from '../../components/SectionImageRoundedCorners'
import type { Doc } from '../../types/customTypes'

type SectionMediaProps = {
  hideItem?: 'video' | 'image'
  hideKey?: string
  hideValue?: string
  hideDisclaimer?: string
}

const sectionMedia = ({
  hideItem,
  hideKey,
  hideValue,
  hideDisclaimer,
}: SectionMediaProps) => {
  return [
    {
      title: 'Media type',
      name: 'mediaType',
      type: 'string',
      description: 'Choose between an image or a video',
      options: {
        list: [
          { title: 'Image', value: 'image' },
          { title: 'Video', value: 'video' },
          { title: 'None', value: 'none' },
        ],
        layout: 'radio',
        direction: 'horizontal',
      },
      initialValue: 'none',

      validation: (Rule: Rule) =>
        Rule.custom((value, context) => {
          const document = context.document as Doc

          if (
            value === hideItem &&
            hideKey &&
            document?.[hideKey] === hideValue
          ) {
            return (
              hideDisclaimer ??
              `Adding ${hideItem} is not allowed in this when ${hideKey} is set to ${hideValue}`
            )
          }

          return true
        }),
    },
    {
      type: 'string',
      name: 'disclaimer',
      readOnly: true,
      components: {
        input: () =>
          Disclaimer({
            level: 'warning',
            disclaimerText:
              hideDisclaimer ??
              `Adding ${hideItem} is not allowed in this when ${hideKey} is set to ${hideValue}`,
          }),
      },
      hidden: ({ parent }: { parent: Doc }) => {
        if (hideKey)
          return !(
            parent?.mediaType === hideItem && parent?.[hideKey] === hideValue
          )
        return true
      },
    },
    {
      name: 'sectionVideo',
      title: 'Video file',
      type: 'mux.video',
      description: 'A field for adding a video file.',
      hidden: ({ parent }: { parent: Doc }) => {
        if (parent?.mediaType !== 'video') return true
        if (hideKey && hideItem === 'video')
          return parent?.[hideKey] === hideValue

        return false
      },

      validation: (Rule: Rule) =>
        Rule.custom((_, context) => {
          const document = context.document as Doc
          if (document?.sectionImage && document?.sectionVideo) {
            return 'You cannot add both image and video. Please remove one.'
          }
          return true
        }),
    },
    {
      title: 'Video thumbnail image',
      name: 'videoThumbnail',
      type: 'image',
      description: 'Add a custom thumbnail image for the video.',
      options: {
        hotspot: true,
      },
      hidden: ({ parent }: { parent: Doc }) => {
        if (parent?.mediaType !== 'video') return true
        if (hideKey && hideItem === 'video')
          return parent?.[hideKey] === hideValue

        return false
      },
      validation: (Rule: Rule) =>
        Rule.custom((_, context) => {
          const document = context.document as Doc
          if (document?.sectionVideo && !document?.videoThumbnail) {
            return 'A video thumbnail is required when a video is added.'
          }
          return true
        }),
    },
    {
      type: 'boolean',
      name: 'hideControls',
      hidden: ({ parent }: { parent: Doc }) => {
        if (parent?.mediaType !== 'video') return true
        if (hideKey && hideItem === 'video')
          return parent?.[hideKey] === hideValue

        return false
      },
      initialValue: false,
    },
    {
      name: 'sectionImage',
      title: 'Section image',
      type: 'image',
      description: 'The image for featured section',
      components: {
        input: SectionImageRoundedCorners,
      },
      fields: [
        {
          name: 'cornerRadius',
          title: 'Corner Radius',
          type: 'number',
          hidden: true,
        },
        {
          name: 'rounded',
          title: 'Use Elliptical Corners',
          type: 'boolean',
          hidden: true,
        },
      ],
      options: {
        hotspot: true,
      },
      hidden: ({ parent }: { parent: Doc }) => {
        if (parent?.mediaType !== 'image') return true
        if (hideKey && hideItem === 'image')
          return parent?.[hideKey] === hideValue

        return false
      },
      validation: (Rule: Rule) =>
        Rule.custom((_, context) => {
          const document = context.document as Doc
          if (document?.sectionImage && document?.sectionVideo) {
            return 'You cannot add both image and video. Please remove one.'
          }
          return true
        }),
    },
  ]
}

export default sectionMedia
