import type { PreviewPrepareProps } from '../../types/customTypes'
import { validationRules } from '../validation'

const QuestionAndAnswerType = {
  // Field that contains question and answer
  title: 'Question and answer type',
  name: 'questionAndAnswer',
  type: 'object',
  preview: {
    select: {
      title: 'localizedQuestion.en',
      subtitle: 'tags',
    },
    prepare({
      title,
      subtitle,
    }: PreviewPrepareProps & { subtitle: Array<string> }) {
      return {
        title,
        subtitle: `${subtitle ? `- ${subtitle?.join(', ')}` : ''}`,
      }
    },
  },
  fields: [
    {
      // Field that contains question
      title: 'Question',
      name: 'localizedQuestion',
      type: 'stringTitle',
      description: 'A field for entering the frequently asked question.',
    },
    {
      // Field that gives answer to field Question
      title: 'Answer',
      name: 'localizedAnswer',
      type: 'richTextUnlimited',
      description:
        'A field for providing the answer to the frequently asked question.',
    },
    {
      title: 'Tags',
      name: 'tags',
      type: 'array',
      description:
        'Tags used to help differentiate and allow the question to be searched by the given tags.',
      of: [
        { type: 'string', validation: validationRules.questionTagValidation },
      ],
      options: {
        layout: 'tags',
      },
    },
  ],
}

export default QuestionAndAnswerType
