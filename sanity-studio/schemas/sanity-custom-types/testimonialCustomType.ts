import { validationRules } from '../validation'

// Testimonial post schema is designed for creating a single testimonial content object.
const TestimonialCustomType = {
  name: 'testimonialCustomType',
  title: 'Testimonial post',
  preview: {
    select: {
      title: 'testimonialPostReviewerName',
      subtitle: 'testimonialReviewerProfession',
      media: 'testimonialReviewerImage',
    },
  },
  type: 'object',
  fields: [
    {
      title: "Reviewer's name",
      name: 'testimonialPostReviewerName',
      type: 'string',
      description: "The reviewer's name in the testimonial post.",
      validation: validationRules.titleValidation,
    },
    {
      title: 'The company where the reviewer works',
      name: 'testimonialReviewerCompany',
      type: 'string',
      description:
        'The company or organization the reviewer represents. (if the reviewer is a company, leave it blank)',
    },
    {
      title: "Reviewer's profession",
      name: 'testimonialReviewerProfession',
      type: 'string',
      description:
        'The profession of the reviewer (if the reviewer is a company, leave it blank)',
    },
    {
      title: 'Link Text',
      name: 'reviewerLinkText',
      type: 'string',
      description:
        "Text used as a description for the link. It serves the same purpose as 'Click for more info' text on a website button.",
    },
    {
      title: "Reviewer's relevant link ",
      name: 'reviewerLinkUrl',
      type: 'string',
      description:
        "The actual link that sends us to a certain section or a video/article. It wraps the text from the field 'Reviewer's website address' with a path from our website. Can be omitted if not needed.",
    },
    {
      title: 'Testimonial post text content',
      name: 'localizedTestimonialPost',
      type: 'richTextContent',
      description: 'The text content of the testimonial post.',
      validation: validationRules.isRequired,
    },
    {
      // Reviewer image usually it's an image of a person or company logo
      title: 'Reviewer image',
      name: 'testimonialReviewerImage',
      type: 'image',
      description:
        'The image of the reviewer. For a company logo, use images with 16:9 aspect ratio or similar. For a person, use square images.',
      options: {
        hotspot: true,
      },
      validation: validationRules.imageValidation,
    },
  ],
}

export default TestimonialCustomType
