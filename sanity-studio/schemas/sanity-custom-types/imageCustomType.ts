import { validationRules } from '../validation'

const ImageCustomType = {
  // Schema for image with alt text
  name: 'imageCustomType',
  title: 'Image Schema',
  type: 'object',
  preview: {
    select: {
      title: 'altImageText',
      media: 'imageField',
    },
  },
  fields: [
    {
      // Field for image
      title: 'Image',
      name: 'imageField',
      type: 'image',
      description: 'A field for placing an image.',
      validation: validationRules.imageValidation,
      options: {
        hotspot: true,
      },
    },
  ],
}

export default ImageCustomType
