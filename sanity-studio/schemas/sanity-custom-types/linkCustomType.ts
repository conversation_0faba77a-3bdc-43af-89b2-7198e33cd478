import { LinkIcon } from '@sanity/icons'

import linkSelector from '../navigation/linkSelector'
import { validationRules } from '../validation'

// Custom type Link
const LinkCustomType = {
  name: 'linkCustomType',
  title: 'Link custom type',
  type: 'document',
  icon: LinkIcon,
  preview: {
    select: {
      title: 'linkLabel',
      subtitle: 'linkType',
      media: 'icon',
    },
  },
  fields: [
    // Link label
    {
      title: 'Link label',
      name: 'link<PERSON>abel',
      type: 'string',
      description: 'The text that will be displayed on the link.',
      validation: validationRules.titleValidation,
    },
    ...linkSelector.fields,
  ],
}

export default LinkCustomType
