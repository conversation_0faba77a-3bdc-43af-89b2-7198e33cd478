import { headerSchemaFields } from '../common/headerFields'
import { validationRules } from '../validation'

// Carousel custom type schema
const CarouselCustomType = {
  name: 'carouselCustomType',
  title: 'Carousel post',
  type: 'object',
  preview: {
    select: {
      title: 'localizedTitle.en',
      subtitle: 'carouselPostReviewerName',
      media: 'carouselPostReviewerImage',
    },
  },
  fields: [
    // Carousel post title
    ...headerSchemaFields({
      title: 'Carousel post title',
      description: 'The title of the carousel post',
      type: 'richTextLongSubtitle',
    }),
    // carousel reviewer name
    {
      title: 'Reviewer name',
      name: 'carouselPostReviewerName',
      type: 'string',
      description: 'The name of the reviewer in the carousel post.',
      validation: validationRules.titleValidation,
    },
    // carousel reviewer description
    {
      title: 'Reviewer description',
      name: 'carouselReviewerDescription',
      type: 'string',
      description:
        'A brief description of the company or organization that the reviewer represents.',
    },
    {
      // Reviewer image usually it's an image of a person or company logo
      title: 'Reviewer image',
      name: 'carouselPostReviewerImage',
      type: 'image',
      description:
        'An image that represents the reviewer in the carousel post. This is typically an image of the person or the company logo.',
      validation: validationRules.imageValidation,
      options: {
        hotspot: true,
      },
    },
  ],
}

export default CarouselCustomType
