import { filterExistingReferences } from '../util-functions/schemaFilters'
import { validationRules } from '../validation'

const QuestionCategoryType = {
  // Field that contains categories
  title: 'Category',
  name: 'questionCategory',
  type: 'object',
  preview: {
    select: {
      title: 'categoryTitle.en',
    },
  },
  fields: [
    {
      title: 'Category Title',
      name: 'categoryTitle',
      type: 'stringTitle',
    },
    {
      title: 'Q&A',
      name: 'faqQuestionAndAnswer',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [
            {
              type: 'questionAndAnswer',
            },
          ],
          options: {
            filter: filterExistingReferences,
          },
        },
      ],
      description:
        'List of questions and their corresponding answers for this FAQ category. Each entry should contain a unique question and its answer.',
      validation: validationRules.faqCategoryValidation,
    },
  ],
}

export default QuestionCategoryType
