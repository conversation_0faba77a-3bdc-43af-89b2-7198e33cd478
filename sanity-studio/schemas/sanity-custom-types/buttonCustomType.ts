import { ButtonIcon } from '../../components/Icons'
import type { PreviewPrepareProps } from '../../types/customTypes'
import { headerSchemaFields } from '../common/headerFields'
import linkSelector from '../navigation/linkSelector'
import { generateLinkSelectorPreview } from '../util-functions/utilFunctions'

// Custom type button
const ButtonCustomType = {
  name: 'buttonCustomType',
  title: 'Button custom type',
  type: 'document',
  icon: ButtonIcon,
  preview: {
    select: {
      title: 'stringTitle.en',
      pageSlug: 'allPages.pageSlug.current',
      pageRef: 'allPages._ref',
      sectionSlug: 'allPageSections.slug.current',
      customLink: 'customLink',
      media: 'icon',
    },
    prepare(selection: PreviewPrepareProps) {
      return generateLinkSelectorPreview({ ...selection })
    },
  },
  fields: [
    ...headerSchemaFields({
      title: 'Button label',
      description: 'The text that will be displayed in a button.',
      type: 'stringTitle',
    }),
    ...linkSelector.fields,
  ],
}

export default ButtonCustomType
