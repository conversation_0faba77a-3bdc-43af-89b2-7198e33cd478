import { UsersIcon } from '@sanity/icons'

import { filterExistingReferences } from '../util-functions/schemaFilters'
import { validationRules } from '../validation'

const FeedbackModal = {
  name: 'feedbackModal',
  title: 'Feedback Modal',
  icon: UsersIcon,
  type: 'document',
  preview: {
    select: {
      title: 'localizedCtaButtonLabel.en',
      subtitle: 'localizedFeedbackModalTitle.en',
      media: 'icon',
    },
  },
  fieldsets: [
    {
      name: 'general',
      title: 'General',
      options: { collapsible: true, collapsed: false },
    },
    {
      name: 'ratingDescriptors',
      title: 'Rating Descriptors',
      options: { collapsible: true, collapsed: true },
    },
    {
      name: 'messages',
      title: 'Messages',
      options: { collapsible: true, collapsed: true },
    },
    {
      name: 'exclusions',
      title: 'Exclusions',
      options: { collapsible: true, collapsed: true },
    },
  ],
  fields: [
    {
      title: 'CTA Button label',
      name: 'localizedCtaButtonLabel',
      type: 'stringTitle',
      description:
        'The label displayed on the call-to-action button that triggers the feedback modal. It should be brief and direct.',
      validation: validationRules.isRequired,
      fieldset: 'general',
    },
    {
      title: 'Feedback Modal Title',
      name: 'localizedFeedbackModalTitle',
      type: 'stringTitle',
      description:
        'The heading text that appears at the top of the feedback modal, usually posing a question or statement prompting user response.',
      validation: validationRules.isRequired,
      fieldset: 'general',
    },
    {
      title: 'Lowest Rating Descriptor',
      name: 'localizedLowestRatingText',
      type: 'stringTitle',
      description:
        'The descriptive text accompanying the lowest value on the rating scale within the feedback modal, indicating negative sentiment.',
      validation: validationRules.isRequired,
      fieldset: 'ratingDescriptors',
    },
    {
      title: 'Highest Rating Descriptor',
      name: 'localizedHighestRatingText',
      type: 'stringTitle',
      description:
        'The descriptive text accompanying the highest value on the rating scale within the feedback modal, indicating positive sentiment.',
      validation: validationRules.isRequired,
      fieldset: 'ratingDescriptors',
    },
    {
      title: 'Feedback Input Placeholder',
      name: 'localizedTextPlaceholder',
      type: 'stringTitle',
      description:
        'Placeholder text within the open response input field in the feedback modal that suggests what kind of information users can provide.',
      validation: validationRules.isRequired,
      fieldset: 'general',
    },
    {
      title: 'Submit Feedback Button Label',
      name: 'localizedSubmitButtonLabel',
      type: 'stringTitle',
      description:
        'The label on the button used to submit responses in the feedback modal; should convey action completion.',
      validation: validationRules.isRequired,
      fieldset: 'general',
    },
    {
      title: 'Redirect Feedback Button Label',
      name: 'localizedRedirectButtonLabel',
      type: 'stringTitle',
      description:
        'The label on the button used to redirect the user to our community forum.',
      validation: validationRules.isRequired,
      fieldset: 'general',
    },
    {
      title: 'Successful feedback message title',
      name: 'localizedSuccessfulFeedbackTitle',
      type: 'stringTitle',
      description:
        'The title displayed to the user when their feedback is successfully submitted.',
      validation: validationRules.isRequired,
      fieldset: 'messages',
    },
    {
      title: 'Successful feedback message description',
      name: 'localizedSuccessfulFeedbackDescription',
      type: 'stringTitle',
      description:
        'The description displayed to the user when their feedback is successfully submitted.',
      validation: validationRules.isRequired,
      fieldset: 'messages',
    },
    {
      title: 'Unsuccessful feedback message title',
      name: 'localizedFailedFeedbackTitle',
      type: 'stringTitle',
      description:
        'The title displayed to the user when their feedback submission fails.',
      validation: validationRules.isRequired,
      fieldset: 'messages',
    },
    {
      title: 'Unsuccessful feedback message description',
      name: 'localizedFailedFeedbackDescription',
      type: 'stringTitle',
      description:
        'The description displayed to the user when their feedback submission fails.',
      validation: validationRules.isRequired,
      fieldset: 'messages',
    },
    {
      title: 'Exclude from Pages',
      name: 'excludePages',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [{ type: 'page' }],
          options: {
            filter: filterExistingReferences,
          },
        },
      ],
      description:
        'List of pages where the feedback modal should not be displayed.',
      fieldset: 'exclusions',
    },
  ],
}

export default FeedbackModal
