import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const InfoBlock = {
  // Schema for "Info Block" that appears on info block section
  name: 'infoBlock',
  title: 'Info block',
  type: 'document',
  preview: createSectionPreview({ subtitle: 'localizedSubtitle.en' }),
  fields: [
    ...headerSchemaFields({
      title: 'Info block title',
      description: 'Title field for the info block, rendered beneath the icon.',
      subtitle: {
        showSubtitle: true,
        type: 'richTextContent',
        title: 'Info block content',
        description:
          'The main content of the info block, which provides additional information or context.',
      },
      icon: {
        showIcon: true,
        title: 'Info block icon',
        description: 'Icon that should be rendered at the on top of the title.',
        validation: validationRules.imageValidation,
      },
    }),
    slugSchemaField({
      title: 'Info block card path',
      description:
        'The path for the card that will act as anchor link. Do not add "#" symbol. (e.g. "bitcoin")',
      isRequired: false,
    }),
    {
      // Field for buttons
      name: 'infoBlockButtons',
      title: 'Buttons',
      type: 'array',
      description: `Button with a label and a link that can redirect to an internal page or section, or to an external website such as "https://google.com"`,
      validation: null,
      of: [
        {
          type: 'reference',
          to: [
            {
              type: 'buttonCustomType',
            },
          ],
        },
      ],
    },
  ],
}

export default InfoBlock
