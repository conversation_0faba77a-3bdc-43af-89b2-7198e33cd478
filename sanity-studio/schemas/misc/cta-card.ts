import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const CTACard = {
  // Schema for "CTA Card" section
  name: 'ctaCard',
  title: 'CTA card',
  type: 'document',
  preview: createSectionPreview({
    title: 'stringTitle.en',
    subtitle: 'stringSubtitle.en',
  }),

  fields: [
    ...headerSchemaFields({
      title: 'CTA card title',
      description: 'The title of the Call to Action (CTA) card.',
      type: 'stringShortTitle',
      subtitle: {
        showSubtitle: true,
        title: 'CTA card subtitle',
        description: 'The subtitle that appears beneath the CTA card title.',
        type: 'stringMidSubtitle',
      },
      icon: {
        showIcon: true,
        description: 'CTA card icon',
        title: 'An icon that represents the CTA card.',
      },
    }),
    slugSchemaField({
      title: 'CTA card slug',
      description:
        'Unique identifier that is used to differentiate the section. (e.g. "save-my-spot-now")',
      source: 'subtitle',
    }),
    {
      // Field for button
      name: 'ctaCardButton',
      title: 'Button',
      type: 'reference',
      description:
        'Button with a label and a link that can redirect to an internal page or section, or to an external website such as "https://google.com"',
      validation: null,
      to: [
        {
          type: 'buttonCustomType',
        },
      ],
    },
    {
      title: 'Hide CTA card from sections',
      name: 'intersectSections',
      type: 'array',
      description: 'Enter slugs of the sections to hide the CTA card from.',
      of: [
        {
          type: 'string',
          validation: validationRules.intersectSectionsValidation,
        },
      ],
      options: {
        layout: 'tags',
      },
    },
    {
      // CTA card position
      name: 'position',
      title: 'Position',
      description: 'The position of the CTA card on the screen.',
      type: 'string',
      options: {
        list: [
          { title: 'Top right', value: 'top-right' },
          { title: 'Bottom right', value: 'bottom-right' },
          { title: 'Top left', value: 'top-left' },
          { title: 'Bottom left', value: 'bottom-left' },
        ],
        layout: 'dropdown',
      },
    },
  ],
}
export default CTACard
