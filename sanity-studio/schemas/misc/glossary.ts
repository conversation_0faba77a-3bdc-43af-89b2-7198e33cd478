import { BookIcon } from '@sanity/icons'
import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const Glossary = {
  name: 'glossary',
  title: 'Glossary',
  type: 'document',
  preview: createSectionPreview({
    title: 'stringTitle.en',
    subtitle: 'localeMarkdown.en',
  }),
  icon: BookIcon,
  fields: [
    ...headerSchemaFields({
      title: 'Word',
      description: 'Word for which the glossary is written for',
      type: 'stringTitle',
    }),
    slugSchemaField({
      description:
        'The path for the glossary that will act as anchor link. Do not add "#" symbol. (e.g. "markdown-info")',
    }),
    {
      name: 'localeMarkdown',
      title: 'Markdown text',
      type: 'richTextUnlimited',
      description: 'Explanation for the glossary',
      validation: validationRules.isRequired,
    },
  ],
}

export default Glossary
