import type { Doc, PreviewPrepareProps } from '../../types/customTypes'
import { headerSchemaFields } from '../common/headerFields'
import { flattenRichText } from '../util-functions/utilFunctions'
import { commonPosts } from './contentPostUtils'

const ContentSections = {
  // Schema for "Content" section
  name: 'contentSection',
  type: 'document',
  preview: {
    select: {
      title: 'localizedTitle.en',
      subtitle: 'dropdown',
      media: 'featuredPost.0.postImage',
    },
    prepare: ({
      title,
      subtitle,
      media,
    }: PreviewPrepareProps & { subtitle: Array<string> }) => {
      const formattedTitle = flattenRichText(title)
      const formattedSubtitle = subtitle?.[0]

      return {
        title: formattedTitle,
        subtitle: formattedSubtitle
          ? formattedSubtitle?.toUpperCase() + subtitle?.slice(1, -4)
          : '',
        media,
      }
    },
  },
  fields: [
    {
      name: 'dropdown',
      type: 'string',
      title: 'Section Type',
      description: `Choose a section type. Once selected, you won't be able to change it.`,
      // Sets readOnly once a user has selected a type.
      readOnly: ({ document }: { document: Doc }) => Boolean(document.dropdown),
      options: {
        list: [
          { title: 'Blog', value: 'blogPost' },
          { title: 'News', value: 'newsPost' },
          { title: 'Videos', value: 'videoPost' },
          { title: 'Research', value: 'researchPost' },
        ],
        layout: 'radio',
      },
      initialValue: 'blogPost',
    },

    ...headerSchemaFields({
      description: 'A title for the section.',
      type: 'richTextMidTitleLite',
      subtitle: {
        showSubtitle: true,
        type: 'richTextMidSubtitleLite',
        description:
          'A brief summary or subtitle that appears beneath the section title.',
      },
    }),

    ...commonPosts,
  ],
}

export default ContentSections
