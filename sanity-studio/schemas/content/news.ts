import type { PreviewPrepareProps } from '../../types/customTypes'
import { filterExistingReferences } from '../util-functions/schemaFilters'
import {
  createObjectArray,
  flattenRichText,
} from '../util-functions/utilFunctions'
import { validationRules } from '../validation'
import contentPosts from './contentPosts'

const news = {
  ...contentPosts,
  // Schema for "news" designed for publishing news to tontine.com
  name: 'newsPost',
  title: 'News Common',
  preview: {
    select: {
      title: 'localizedTitle.en',
      media: 'postImage',
      ...createObjectArray({
        amount: 3,
        arrayName: 'authorArray',
        propertyName: 'personName',
        objectName: 'author',
      }),
    },
    prepare(selection: PreviewPrepareProps) {
      const { title, media, ...authors } = selection
      const formattedTitle = flattenRichText(title)
      const subtitle = Object.values(authors).filter(Boolean).join(', ')
      return {
        title: formattedTitle,
        subtitle,
        media,
      }
    },
  },
  fields: [
    ...contentPosts.fields,
    {
      // An array reference to the `person.ts` schema
      title: 'Authors',
      name: 'authorArray',
      type: 'array',
      of: [
        {
          type: 'reference',
          title: 'A reference to the author/s.',
          to: [
            {
              type: 'person',
            },
          ],
          options: {
            filter: filterExistingReferences,
          },
        },
      ],
      options: {
        sortable: false,
      },
    },
    {
      // Estimated amount of time it would take to read the entire post
      title: 'Reading time',
      name: 'readingTime',
      type: 'number',
      description:
        'An estimate of how long it will take to read the news, in seconds. (e.g. "130")',
      validation: validationRules.readTimeValidation,
    },
  ],
}

export { news }
