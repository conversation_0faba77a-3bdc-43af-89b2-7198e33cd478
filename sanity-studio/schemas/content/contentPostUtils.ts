import { ArrayTypeFilter } from '../../components/CustomArrayInput'
import type { PostType, SchemaUtilProps } from '../../types/customTypes'
import {
  filterExistingReferencesAndTypes,
  filterFeaturedPost,
} from '../util-functions/schemaFilters'
import { validationRules } from '../validation'

const createPostSchema = ({
  featuredPostReferences,
  postsArrayReferences,
  isRequired = false,
}: {
  featuredPostReferences: Array<string>
  postsArrayReferences: Array<string>
  isRequired?: boolean
}) => {
  return [
    {
      name: 'featuredPost',
      type: 'array',
      description: 'Choose a featured post for this section.',
      of: [
        {
          type: 'reference',
          title: 'Reference to selected type',
          to: featuredPostReferences.map((type) => ({ type })),
          options: {
            filter: filterFeaturedPost,
            disableNew: true,
          },
        },
      ],
      options: {
        sortable: false,
      },
      components: {
        input: ArrayTypeFilter,
      },
      validation: isRequired ? validationRules.featuredPostValidation : null,
    },
    {
      name: 'postsArray',
      description: 'Include posts to show in this section.',

      type: 'array',
      of: [
        {
          type: 'reference',
          title: 'Reference to selected type',
          to: postsArrayReferences.map((type) => ({ type })),

          options: {
            filter: ({ parent, document }: SchemaUtilProps) => {
              return filterExistingReferencesAndTypes({ parent, document })
            },
            disableNew: true,
          },
        },
      ],
      components: {
        input: ArrayTypeFilter,
      },
      validation: isRequired ? validationRules.isRequired : null,
    },
  ]
}

const postTypes: Array<PostType> = [
  'blogPost',
  'newsPost',
  'videoPost',
  'researchPost',
]

const commonPosts = createPostSchema({
  featuredPostReferences: postTypes,
  postsArrayReferences: postTypes,
  isRequired: true,
})

export { commonPosts }
