import { validationRules } from '../validation'
import contentPosts from './contentPosts'

const video = {
  ...contentPosts,
  // Schema for "Video"
  name: 'videoPost',
  title: 'Video',

  fields: [
    ...contentPosts.fields,
    {
      // Field for image
      title: 'Video thumbnail image',
      name: 'thumbnail',
      type: 'image',
      description:
        'Add a custom thumbnail image for the video. By default, it uses the post image.',
      options: {
        hotspot: true,
      },
    },
    {
      // Field for video
      title: 'Video file',
      name: 'videoFile',
      type: 'mux.video',
      description: 'A field for adding a video file.',
      validation: validationRules.isRequired,
    },
  ],
}

export { video }
