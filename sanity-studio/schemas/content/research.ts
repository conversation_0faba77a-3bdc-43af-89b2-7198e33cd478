import type { PreviewPrepareProps } from '../../types/customTypes'
import { filterExistingReferences } from '../util-functions/schemaFilters'
import {
  createObjectArray,
  flattenRichText,
} from '../util-functions/utilFunctions'
import { validationRules } from '../validation'
import contentPosts from './contentPosts'

const research = {
  ...contentPosts,
  // Schema for "Research"
  name: 'researchPost',
  title: 'Research',
  preview: {
    select: {
      title: 'localizedTitle.en',
      media: 'postImage',
      ...createObjectArray({
        amount: 3,
        arrayName: 'authorArray',
        propertyName: 'personName',
        objectName: 'author',
      }),
    },
    prepare(selection: PreviewPrepareProps) {
      const { title, media, ...authors } = selection
      const formattedTitle = flattenRichText(title)
      const subtitle = Object.values(authors).filter(Boolean).join(', ')
      return {
        title: formattedTitle,
        subtitle,
        media,
      }
    },
  },
  fields: [
    ...contentPosts.fields,
    {
      // An array reference to the `person.ts` schema
      title: 'Authors',
      name: 'authorArray',
      type: 'array',
      of: [
        {
          type: 'reference',
          title: 'A reference to the author/s.',
          to: [
            {
              type: 'person',
            },
          ],
          options: {
            filter: filterExistingReferences,
          },
        },
      ],
      options: {
        sortable: false,
      },
    },

    {
      // Field of type file that allows to attach pdf files.
      title: 'File in PDF format',
      name: 'manuscript',
      type: 'file',
      description:
        'A field for attaching a PDF file related to the research post.',
      validation: validationRules.isRequired,
    },
  ],
}

export { research }
