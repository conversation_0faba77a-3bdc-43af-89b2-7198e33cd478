import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const MarkdownSection = {
  // Schema for "Testimonials" section
  name: 'markdownSection',
  title: 'Markdown section',
  type: 'document',
  preview: createSectionPreview({
    title: 'stringTitle.en',
    fallbackTitle: 'localeMarkdown.en',
  }),
  fields: [
    ...headerSchemaFields({
      title: 'Title',
      description:
        'A title for the markdown section, used internally to differentiate between different documents (not visible to end users).',
      type: 'stringTitle',
      isRequired: false,
    }),
    {
      // A field to set the text styling
      name: 'styling',
      type: 'string',
      title: 'Text styling',
      description:
        'Choose a styling for the text. Normal styling is same as the featured section text while disclaimer styling is grayed out.',
      options: {
        list: [
          { title: 'Normal', value: 'normal' },
          { title: 'Disclaimer', value: 'disclaimer' },
        ],
        layout: 'radio',
        direction: 'horizontal',
      },
      initialValue: 'normal',
      validation: validationRules.isRequired,
    },
    {
      // A field to set the text position
      name: 'position',
      type: 'string',
      title: 'Text position',
      description: 'Choose a position for the text.',
      options: {
        list: [
          { title: 'Left', value: 'left' },
          { title: 'Center', value: 'center' },
          { title: 'Right', value: 'right' },
        ],
        layout: 'radio',
        direction: 'horizontal',
      },
      initialValue: 'left',
      validation: validationRules.isRequired,
    },
    {
      // A field to represent the markdown content of the section
      name: 'localeMarkdown',
      title: 'Markdown text',
      type: 'richTextUnlimited',
      description: 'A field for adding the subtitle of the section.',
      validation: validationRules.isRequired,
    },
    slugSchemaField({
      source: 'stringTitle.en',
      description:
        'The path for the featured section that will act as anchor link. Do not add "#" symbol. (e.g. "markdown-info")',
    }),
  ],
}

export default MarkdownSection
