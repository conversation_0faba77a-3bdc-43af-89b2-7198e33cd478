import { LaunchIcon } from '@sanity/icons'

import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'

const CTASection = {
  // Schema for "CTA" section that appears on landing page and many others
  name: 'ctaSection',
  title: 'CTA section',
  icon: LaunchIcon,
  type: 'document',
  preview: createSectionPreview(),
  fields: [
    ...headerSchemaFields({
      title: 'CTA section title',
      description: 'The title representing the CTA section.',
      subtitle: {
        showSubtitle: true,
        title: 'CTA section subtitle',
        type: 'richTextContent',
        description: 'Content displayed within the CTA section.',
      },
    }),
    // Field for CTA section slug
    slugSchemaField({
      title: 'CTA section path',
      description:
        'The path for the CTA section that will act as anchor link. Do not add "#" symbol. (e.g. "cta-info")',
    }),
    {
      // Field for buttons
      name: 'ctaSectionButtons',
      title: 'Buttons',
      type: 'array',
      description: `List of buttons with labels and it's coresponding links`,
      validation: null,
      of: [
        {
          type: 'reference',
          to: [
            {
              type: 'buttonCustomType',
            },
          ],
        },
      ],
    },
    {
      // Field for additional text on CTA section that appears under button
      name: 'ctaAdditionalText',
      title: 'CTA Additional text',
      type: 'string',
      description: 'Additional subtle text that is displayed under the button',
    },
  ],
}

export default CTASection
