import { EnvelopeIcon } from '@sanity/icons'

import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const ContactUsSection = {
  // Schema for "Contact Us" section
  name: 'contactUsSection',
  title: 'Contact us section',
  icon: EnvelopeIcon,
  type: 'document',
  preview: createSectionPreview(),
  fields: [
    ...headerSchemaFields({
      title: 'Contact us section title',
      description: 'The title representing the contact us section.',
    }),
    slugSchemaField({
      title: 'Contact us section path',
      description:
        'The path for the contact us section that will act as anchor link. Do not add "#" symbol. (e.g. "contact-team")',
    }),
    // Form fields for contact us section
    // Placeholder for name input field
    {
      name: 'placeholderNameInput',
      title: 'Placeholder for name input',
      type: 'string',
      description: 'Placeholder text for name input field.',
      validation: validationRules.isRequired,
    },
    // Placeholder for Surname input field
    {
      name: 'placeholderSurnameInput',
      title: 'Placeholder for surname input',
      type: 'string',
      description: 'Placeholder text for surname input field.',
      validation: validationRules.isRequired,
    },
    // Placeholder for Email input field
    {
      name: 'placeholderEmailInput',
      title: 'Placeholder for email input',
      type: 'string',
      description: 'Placeholder text for email input field.',
      validation: validationRules.isRequired,
    },
    // Select Input field placeholder
    {
      name: 'placeholderSelectInput',
      title: 'Placeholder for select input',
      type: 'string',
      description: 'Placeholder text for select input field.',
      validation: validationRules.isRequired,
    },
    // Select Input field options
    {
      name: 'selectInputOptions',
      title: 'Select input options',
      type: 'array',
      of: [{ type: 'string' }],
      description: 'Options available for the select input field.',
      validationRules: validationRules.isRequired,
    },
    // Placeholder for Textarea input field
    {
      name: 'placeholderTextareaInput',
      title: 'Placeholder for textarea input',
      type: 'string',
      description: 'Placeholder text for textarea input field.',
      validation: validationRules.isRequired,
    },
    // Submit button label
    {
      name: 'submitButtonLabel',
      title: 'Submit button label',
      type: 'string',
      description: 'Label displayed on the submit button of the contact form.',
      validation: validationRules.isRequired,
    },
    {
      name: 'localizedDisclaimer',
      title: 'Disclaimer',
      type: 'richTextLite',
      description: 'Text that appears below the submit button.',
      validation: validationRules.isRequired,
    },
  ],
}

export default ContactUsSection
