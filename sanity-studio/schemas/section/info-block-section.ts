import { DocumentTextIcon } from '@sanity/icons'

import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'

const InfoBlockSection = {
  // Schema for "Info Block" section
  name: 'infoBlockSection',
  title: 'Info block section',
  icon: DocumentTextIcon,
  type: 'document',
  preview: createSectionPreview(),
  fields: [
    ...headerSchemaFields({
      title: 'Info block section title',
      description: 'The title representing the info block section.',
    }),
    slugSchemaField({
      title: 'Info block section path',
      description:
        'The path for the info block section that will act as anchor link. Do not add "#" symbol. (e.g. "info-block")',
    }),
    {
      // A reference to info block section
      title: 'Info block section list',
      name: 'infoBlockSectionList',
      type: 'array',
      of: [{ type: 'infoBlock' }],
      description: 'List of elements for the info block section.',
    },
  ],
}

export default InfoBlockSection
