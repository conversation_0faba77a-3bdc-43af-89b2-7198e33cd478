import { PlayIcon } from '@sanity/icons'
import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { filterExistingReferences } from '../util-functions/schemaFilters'
import { createSectionPreview } from '../util-functions/utilFunctions'

const VideosSection = {
  // Schema for "Videos Section"
  name: 'videosSection',
  title: 'Videos Section',
  type: 'document',
  icon: PlayIcon,
  preview: createSectionPreview(),
  fields: [
    ...headerSchemaFields({
      title: 'Videos section title',
      description: 'The title representing the videos section.',
      subtitle: {
        showSubtitle: true,
        description: 'The subtitle for the videos section.',
        type: 'richTextSubtitleLite',
      },
    }),
    slugSchemaField({
      title: 'Videos section path',
      description:
        'The path for the featured section that will act as anchor link. Do not add "#" symbol. (e.g. "recommended-videos")',
    }),
    {
      // Field for videos
      title: 'Videos',
      name: 'videos',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: { type: 'videoPost' },
          options: {
            filter: filterExistingReferences,
          },
        },
      ],
    },
  ],
}

export default VideosSection
