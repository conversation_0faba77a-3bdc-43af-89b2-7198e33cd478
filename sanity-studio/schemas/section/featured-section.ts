import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import SectionMedia from '../sanity-custom-types/sectionMediaType'
import { createSectionPreview } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const FeaturedSection = {
  // Schema for "Featured" section
  name: 'featuredSection',
  title: 'Featured Section',
  type: 'document',
  preview: createSectionPreview({ fallbackTitle: 'localizedSubtitle.en' }),
  groups: [
    {
      name: 'content',
      title: 'Content',
    },
    {
      name: 'toggle',
      title: 'Toggles',
    },
  ],
  fields: [
    ...headerSchemaFields({
      title: 'Featured section title',
      description: 'The title for the featured section.',
      isRequired: false,
      subtitle: {
        type: 'richTextContent',
        showSubtitle: true,
        title: 'Featured section subtitle',
        isRequired: false,
      },
      group: 'content',
    }),
    // Field for featured section slug
    slugSchemaField({
      title: 'Featured section path',
      description:
        'The path for the featured section that will act as anchor link. Do not add "#" symbol. (e.g. "featured-info")',
    }),
    {
      // Toggle for hero section
      name: 'isHero',
      title: 'Is hero section',
      type: 'boolean',
      initialValue: false,
      description:
        'Toggle to treat this section as a hero section on the website (has a blue background).',
      validation: validationRules.isRequired,
      group: 'toggle',
    },

    {
      // Toggle for media direction
      name: 'featuredSectionMediaDirectionToggle',
      title: 'Display media on the right-hand side',
      type: 'boolean',
      initialValue: false,
      description: 'Default is set to display media on the left-hand side.',
      validation: validationRules.isRequired,
      group: 'toggle',
    },
    {
      // Field for buttons
      name: 'featuredSectionButtons',
      title: 'Buttons',
      group: 'content',
      type: 'array',
      description:
        'Buttons with a labels and a links that can redirect to an internal pages or sections, or to an external websites such as "https://google.com"',
      validation: null,
      of: [
        {
          type: 'reference',
          to: [
            {
              type: 'buttonCustomType',
            },
          ],
        },
      ],
    },
    ...SectionMedia({}),
  ],
}

export default FeaturedSection
