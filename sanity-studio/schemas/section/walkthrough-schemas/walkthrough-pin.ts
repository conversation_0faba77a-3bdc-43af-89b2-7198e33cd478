import { PinIcon } from '@sanity/icons'

import { Disclaimer } from '../../../components/Disclaimer'
import { headerSchemaFields } from '../../common/headerFields'
import { filterExcludeSchemas } from '../../util-functions/schemaFilters'
import { createSectionPreview } from '../../util-functions/utilFunctions'

const WalkThroughPin = {
  // Schema for "Info Block" that appears on info block section
  name: 'walkthroughPin',
  title: 'Walkthrough Pin',
  icon: PinIcon,
  type: 'document',
  preview: createSectionPreview({ subtitle: 'localizedSubtitle.en' }),

  fields: [
    {
      type: 'string',
      name: 'disclaimer',
      readOnly: true,
      components: {
        input: () =>
          Disclaimer({
            disclaimerText:
              'You can only add this pin to singular Desktop and Mobile walkthrough grids, but neither is required.',
          }),
      },
    },

    {
      name: 'walkthroughDesktop',
      title: 'Walkthrough Desktop Section',
      type: 'reference',
      to: [{ type: 'walkthroughGrid' }],
      description: 'Reference to the parent walkthrough section',
      options: {
        filter: filterExcludeSchemas({
          inputFieldName: 'walkthroughGrid',
          singleFieldName: 'walkthroughMobile',
        }),
      },
    },

    {
      name: 'walkthroughMobile',
      title: 'Walkthrough Mobile Section',
      type: 'reference',
      to: [{ type: 'walkthroughGrid' }],
      description: 'Reference to the parent walkthrough section',
      options: {
        filter: filterExcludeSchemas({
          inputFieldName: 'walkthroughGrid',
          singleFieldName: 'walkthroughDesktop',
        }),
      },
    },

    {
      name: 'placement',
      title: 'Placement of the pin',
      type: 'walkthroughPinPlacement',
      description: 'Open the grid for easier placement',
    },

    ...headerSchemaFields({
      title: 'Title',
      type: 'richTextTitleLite',
      options: {
        maxHeight: '2.5rem',
      },
      description: 'The title for the pin',

      subtitle: {
        showSubtitle: true,
        type: 'richTextContent',

        description: 'The subtitle for the pin',
      },
    }),
  ],
}

export default WalkThroughPin
