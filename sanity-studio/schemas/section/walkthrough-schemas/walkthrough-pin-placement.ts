import type { ObjectInputProps } from 'sanity'

import { ColumnRowInput } from '../../../components/CustomRowInput'
import type { Doc } from '../../../types/customTypes'

const desktopPlacementSchema = {
  name: 'desktopPlacement',
  title: 'Desktop Placement',
  type: 'object',
  fields: [
    { name: 'column', title: 'Column', type: 'number' },
    { name: 'row', title: 'Row', type: 'number' },
  ],
  components: { input: ColumnRowInput },
  hidden: ({ document }: { document: Doc }) =>
    !document.walkthroughDesktop?._ref,
}

const mobilePlacementSchema = {
  name: 'mobilePlacement',
  title: 'Mobile Placement',
  type: 'object',
  fields: [
    { name: 'column', title: 'Column', type: 'number' },
    { name: 'row', title: 'Row', type: 'number' },
  ],
  components: {
    input: (
      props: ObjectInputProps<{ column: number; row: number }> & {
        document: { walkthroughGridImage: { asset: { url: string } } }
        mobileSection?: boolean
      }
    ) => ColumnRowInput({ ...props, mobileSection: true }),
  },
  hidden: ({ document }: { document: Doc }) =>
    !document.walkthroughMobile?._ref,
}

const walkthroughPinPlacementSchema = {
  name: 'walkthroughPinPlacement',
  title: 'Placement of the pin',
  type: 'object',
  fields: [
    {
      name: 'desktop',
      title: 'Desktop Walkthrough Grid',
      type: 'desktopPlacement',
      description: 'Column should not exceed 60 and row should not exceed 35',
    },
    {
      name: 'mobile',
      title: 'Mobile Walkthrough Grid',
      description: 'Column should not exceed 20 and row should not exceed 40',
      type: 'mobilePlacement',
    },
  ],
}

export {
  desktopPlacementSchema,
  mobilePlacementSchema,
  walkthroughPinPlacementSchema,
}
