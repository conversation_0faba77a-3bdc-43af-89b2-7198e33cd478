import { JoystickIcon } from '@sanity/icons'

import { Disclaimer } from '../../../components/Disclaimer'
import { headerSchemaFields } from '../../common/headerFields'
import { slugSchemaField } from '../../common/slug'
import { createExcludeSchemaFilter } from '../../util-functions/schemaFilters'
import { createSectionPreview } from '../../util-functions/utilFunctions'

const WalkthroughSection = {
  // Schema for "Walkthrough" section
  name: 'walkthroughSection',
  title: 'Walkthrough section',
  type: 'document',
  icon: JoystickIcon,
  preview: createSectionPreview(),
  fields: [
    ...headerSchemaFields({
      title: 'Walkthrough title',
      description: 'The title representing the walkthrough section.',
      subtitle: {
        showSubtitle: true,
        title: 'Walkthrough subtitle',
        description:
          'A field for adding a descriptive subtitle that gives more insight on the section.',
      },
    }),
    slugSchemaField({
      title: 'Walkthrough section path',
      description:
        'The path for the Walkthrough section that will act as anchor link. Do not add "#" symbol. (e.g. "walkthrough")',
    }),

    {
      type: 'string',
      name: 'disclaimer',
      readOnly: true,
      components: {
        input: () =>
          Disclaimer({
            disclaimerText:
              'Each walkthrough grid you add will be show on a separate slide in the same section. Each grid also has unique pins.',
          }),
      },
    },

    {
      name: 'desktopWalkthroughs',
      title: 'Desktop Walkthrough Grids',
      description:
        'Select or create walkthrough grids that will be displayed in the desktop version of this section.',
      type: 'array',
      of: [
        {
          type: 'reference',
          options: {
            filter: createExcludeSchemaFilter({
              inputFieldName: 'walkthroughGrid',
              otherFieldNames: ['mobileWalkthroughs'],
            }),
          },
          to: [
            {
              type: 'walkthroughGrid',
            },
          ],
        },
      ],
    },
    {
      name: 'mobileWalkthroughs',
      title: 'Mobile Walkthrough Grids',
      description:
        'Select or create walkthrough grids that will be displayed in this mobile version of this section.',
      type: 'array',
      of: [
        {
          type: 'reference',
          options: {
            filter: createExcludeSchemaFilter({
              inputFieldName: 'walkthroughGrid',
              otherFieldNames: ['desktopWalkthroughs'],
            }),
          },
          to: [
            {
              type: 'walkthroughGrid',
            },
          ],
        },
      ],
    },
  ],
}

export default WalkthroughSection
