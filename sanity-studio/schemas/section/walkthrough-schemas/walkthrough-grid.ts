import { getImageDimensions } from '@sanity/asset-utils'
import { DashboardIcon } from '@sanity/icons'

import { SANITY_CONSTANTS } from '../../../assets/constants'
import { Disclaimer } from '../../../components/Disclaimer'
import ReferencingPinsInput from '../../../components/ReversePinReference'
import type { PreviewPrepareProps } from '../../../types/customTypes'
import { headerSchemaFields } from '../../common/headerFields'
import { flattenRichText } from '../../util-functions/utilFunctions'
import { validationRules } from '../../validation'

const { DESKTOP_SIZE, MOBILE_SIZE } = SANITY_CONSTANTS

const walkthroughGrid = {
  // Schema for the Walkthrough Grid which will be displayed in Walkthrough Sections and referenced by Walkthrough Pins
  name: 'walkthroughGrid',
  title: 'Walkthrough Grid',
  icon: DashboardIcon,
  type: 'document',
  preview: {
    select: {
      title: 'localizedTitle.en',
      media: 'icon',
      imageRef: 'walkthroughGridImage',
    },

    prepare: ({ title, imageRef }: PreviewPrepareProps) => {
      const formattedTitle = flattenRichText(title)
      const { width, height } = imageRef
        ? getImageDimensions(imageRef)
        : { width: 0, height: 0 }

      let subtitle = 'No image chosen'
      if (width === DESKTOP_SIZE.width && height === DESKTOP_SIZE.height) {
        subtitle = 'Desktop Grid'
      } else if (width === MOBILE_SIZE.width && height === MOBILE_SIZE.height) {
        subtitle = 'Mobile Grid'
      }

      return {
        title: formattedTitle,
        subtitle,
        media: imageRef ?? undefined,
      }
    },
  },

  fields: [
    ...headerSchemaFields({
      title: 'Grid Title',
      description:
        'Title for internal use only; does not affect the website display.',
    }),
    {
      type: 'string',
      name: 'disclaimer',
      title: 'Usage Disclaimer',
      readOnly: true,
      components: {
        input: () =>
          Disclaimer({
            disclaimerText:
              'This grid can be referenced by multiple "Walkthrough Pin" elements.',
          }),
      },
    },
    {
      title: 'Grid Image',
      name: 'walkthroughGridImage',
      type: 'image',
      description: 'The image that will be displayed in the walkthrough grid.',
      validation: validationRules.walkthroughImageValidation,
      options: {
        hotspot: true,
      },
    },
    {
      name: 'referencingPins',
      title: 'Referencing Walkthrough Pins',
      type: 'array',
      readOnly: true,
      of: [{ type: 'reference', to: [{ type: 'walkthroughPin' }] }],
      components: { input: ReferencingPinsInput },
      description:
        'List of Walkthrough Pins that reference this Walkthrough Grid.',
      options: {
        disableNew: true,
      },
    },
  ],
}

export default walkthroughGrid
