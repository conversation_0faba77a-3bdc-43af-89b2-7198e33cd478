import { CommentIcon } from '@sanity/icons'

import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const AboutUsSection = {
  // Schema for "About Us" section
  name: 'aboutUsSection',
  title: 'About us section',
  icon: CommentIcon,
  type: 'document',
  groups: [
    // Groups are defined, fields have been separated based on the contents.
    {
      name: 'sectionContent',
      title: 'Section content',
    },
    {
      name: 'contactDetails',
      title: 'Contact details',
    },
  ],
  preview: createSectionPreview(),
  fields: [
    // Title of the about us section
    ...headerSchemaFields({
      title: 'About us section title',
      description: 'The title representing the about us section.',
      type: 'richTextMidTitleLite',
      group: 'sectionContent',
      subtitle: {
        showSubtitle: true,
        title: 'About us section subtitle',
        description: 'The subtitle for the about us section.',
        isRequired: true,
        type: 'richTextContentLite',
      },
    }),
    slugSchemaField({
      title: 'About us section path',
      description:
        'The path for the about us section that will act as anchor link. Do not add "#" symbol. (e.g. "about-team")',
    }),
    // Subtitle caption of the about us section
    {
      name: 'aboutUsSectionSubtitleCaption',
      title: 'Subtitle caption',
      type: 'string',
      group: 'contactDetails',
      description: 'The caption for the subtitle in the about us section.',
    },
    // Address image of the about us section
    {
      name: 'aboutUsSectionAddressIcon',
      title: 'Address icon',
      type: 'image',
      group: 'contactDetails',
      description:
        'An icon that represents the address in the about us section.',
      validation: validationRules.imageValidation,
      options: {
        hotspot: true,
      },
    },
    // Address of the about us section
    {
      name: 'aboutUsSectionAddress',
      title: 'Address field',
      type: 'string',
      group: 'contactDetails',
      description: 'A physical address displayed in the about us section.',
      validation: validationRules.isRequired,
    },
    // Email image of the about us section
    {
      name: 'aboutUsSectionEmailIcon',
      title: 'Email icon',
      type: 'image',
      group: 'contactDetails',
      description: 'An icon that represents the email in the about us section.',
      validation: validationRules.imageValidation,
      options: {
        hotspot: true,
      },
    },
    // Email of the about us section
    {
      name: 'aboutUsSectionEmail',
      title: 'Email field',
      type: 'string',
      group: 'contactDetails',
      description: 'An email address provided in the about us section.',
      validation: validationRules.isRequired,
    },
    // Phone image of the about us section
    {
      name: 'aboutUsSectionPhoneIcon',
      title: 'Phone icon',
      type: 'image',
      group: 'contactDetails',
      description: 'An icon for the phone number in the about us section.',
    },
    // Phone number of the about us section
    {
      name: 'aboutUsSectionPhoneNumber',
      title: 'Phone number field',
      type: 'string',
      group: 'contactDetails',
      description: 'A phone number displayed in the about us section.',
    },
  ],
}

export default AboutUsSection
