import { headerSchema<PERSON>ields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const PartnersSection = {
  // Schema for the "Partners" section
  name: 'partnersSection',
  title: 'Partners section',
  type: 'document',
  preview: createSectionPreview(),
  fields: [
    ...headerSchemaFields({
      title: 'Partners section title',
      description: 'The title representing the partners section.',
      icon: {
        showIcon: true,
        title: 'Partners section icon',
        description: 'Icon that represents the partners section.',
      },
    }),
    slugSchemaField({
      title: 'Partners section path',
      description:
        'The path for the partners section that will act as anchor link. Do not add "#" symbol. (e.g. "partner-view")',
    }),
    {
      // Field that has reference to partner
      title: 'Partners List',
      name: 'partnersCompanies',
      type: 'array',
      description: 'Select or create partners to display within this section.',
      of: [
        {
          type: 'reference',
          to: [
            {
              type: 'partner',
            },
          ],
        },
      ],
      validation: validationRules.isRequired,
    },
  ],
}

export default PartnersSection
