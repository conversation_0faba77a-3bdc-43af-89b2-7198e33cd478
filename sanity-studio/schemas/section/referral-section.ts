import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'

const ReferralSection = {
  // Schema for "Referral" section
  name: 'referralSection',
  title: 'Referral section',
  type: 'document',
  preview: createSectionPreview(),
  fields: [
    ...headerSchemaFields({
      title: 'Referral section title',
      description: 'The title representing the referral section.',
      subtitle: {
        showSubtitle: true,
        title: 'Referral section subtitle',
        description:
          'A field for adding a descriptive subtitle that gives more insight on the section.',
      },
    }),
    {
      // Field for the section disclaimer
      title: 'Disclaimer',
      name: 'localeDisclaimer',
      type: 'richTextContent',
      description:
        'Field for adding a fine print disclaimer about the section.',
    },
    slugSchemaField({
      title: 'Referral section path',
      description:
        'The path for the Referral section that will act as anchor link. Do not add "#" symbol. (e.g. "referral")',
    }),
  ],
}

export default ReferralSection
