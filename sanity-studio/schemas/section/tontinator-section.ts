import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'

const TontinatorSection = {
  // Schema for "Tontinator" section
  name: 'tontinatorSection',
  title: 'Tontinator section',
  type: 'document',
  preview: createSectionPreview(),
  fields: [
    ...headerSchemaFields({
      title: 'Tontinator section title',
      description: 'The title representing the tontinator section.',
      subtitle: {
        showSubtitle: true,
        title: 'Tontinator section subtitle',
        description:
          'A field for adding a descriptive subtitle that gives more insight on the section.',
      },
    }),
    {
      // Field for the section disclaimer
      title: 'Disclaimer',
      name: 'localeDisclaimer',
      type: 'richTextContent',
      description:
        'Field for adding a fine print disclaimer about the section.',
    },
    slugSchemaField({
      title: 'Tontinator section path',
      description:
        'The path for the Tontinator section that will act as anchor link. Do not add "#" symbol. (e.g. "tontinator")',
    }),
  ],
}

export default TontinatorSection
