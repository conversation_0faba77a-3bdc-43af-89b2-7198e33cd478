import { internalUseTitle } from '../common/headerFields'
import { filterExistingReferences } from '../util-functions/schemaFilters'
import { validationRules } from '../validation'

const NavigationMenu = {
  // Schema for "Navigation Menu".
  title: 'Navigation Menus',
  name: 'navigationMenu',
  type: 'document',
  preview: {
    select: {
      title: 'title',
      media: 'websiteLogo',
    },
  },
  fields: [
    internalUseTitle,
    {
      // Field for website logo.
      title: 'Logo',
      name: 'websiteLogo',
      type: 'image',
      description:
        'The main navigation logo displayed on the left side of the navigation bar.',
      options: {
        hotspot: true,
      },
      validation: validationRules.imageValidation,
    },

    {
      // Navigation bar items
      title: 'Navigation bar items',
      name: 'navigationItems',
      type: 'array',
      description: 'List of items displayed in the navigation bar.',
      of: [
        {
          type: 'reference',
          options: {
            filter: filterExistingReferences,
          },
          to: [
            {
              type: 'navigationItem', // reference to "navigationItem"
            },
          ],
        },
      ],
    },
    {
      // This field controls the visibility of the sign in/up buttons.
      title: 'Hide sign in/up buttons from navigation bar and hamburger menu',
      name: 'authButtonsVisibility',
      type: 'boolean',
      description:
        'Controls the visibility of the authentication buttons. When the toggle is set to "ON", the buttons will be hidden from the website.',
      initialValue: false,
    },
    {
      // This field controls the visibility of the language picker.
      title: 'Login button link',
      description:
        'Where the login button should lead to. (defaults to /rewards-club/#referral)',
      name: 'loginLink',
      type: 'slug',
    },

    {
      // This field controls the visibility of the language picker.
      title: 'Register button link',
      description:
        'Where the register button should lead to. (defaults to /lifetime-income-calculator/#tontinator)',
      name: 'registerLink',
      type: 'slug',
    },
    {
      // This field controls the visibility of the language picker.
      title: 'Hide language picker from navigation bar and hamburger menu',
      name: 'languagePickerVisibility',
      type: 'boolean',
      description:
        'Controls the visibility of the language picker. When the toggle is set to "ON", the language picker will be hidden from the website. (only visible on staging for now)',
      initialValue: false,
    },
  ],
}

export default NavigationMenu
