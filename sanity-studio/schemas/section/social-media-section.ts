import { HeartIcon } from '@sanity/icons'

import { internalUseTitle } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'

const SocialMediaSection = {
  // Schema for "Social Media" section
  name: 'socialMediaSection',
  title: 'Social Media Section',
  icon: HeartIcon,
  type: 'document',
  preview: createSectionPreview({ title: 'title' }),
  fields: [
    internalUseTitle,
    slugSchemaField({
      title: 'Social media section path',
      description:
        'The path for the social media section that will act as anchor link. Do not add "#" symbol. (e.g. "social-media")',
    }),
    {
      // List of social media icons and links
      title: 'Social media section items',
      name: 'socialMediaSectionItems',
      type: 'array',
      description: 'Add social media icons with their corresponding links.',
      of: [
        {
          type: 'reference',
          to: [
            {
              type: 'socialLink', // reference to socialLink schema
            },
          ],
        },
      ],
    },
  ],
}

export default SocialMediaSection
