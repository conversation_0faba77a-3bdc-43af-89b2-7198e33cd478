import { HelpCircleIcon } from '@sanity/icons'

import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'

const FaqSection = {
  // Schema for "FAQ" section
  name: 'faqSection',
  title: 'Faq Section',
  icon: HelpCircleIcon,
  type: 'document',
  preview: createSectionPreview({ subtitle: 'localizedSubtitle.en' }),
  groups: [
    {
      name: 'sectionContent',
      title: 'Section Content',
    },
    {
      name: 'faqsWebschema',
      title: 'FAQs Webschema',
    },
  ],
  fields: [
    ...headerSchemaFields({
      description: 'A title for the section.',
      type: 'richTextMidTitleLite',
      subtitle: {
        showSubtitle: true,
        description:
          'A brief summary or subtitle that appears beneath the section title.',
        type: 'richTextMidSubtitleLite',
      },
    }),
    {
      title: 'Search bar placeholder text',
      name: 'placeholderText',
      type: 'stringTitle',
    },
    {
      title: 'FAQ Categories',
      name: 'faqCategories',
      type: 'array',
      group: 'sectionContent',
      of: [
        {
          type: 'questionCategory',
        },
      ],
      description:
        'This field allows you to create different categories for FAQs. Each category can contain multiple questions and their corresponding answers.',
    },
    slugSchemaField({
      title: 'FAQ section path',
      description:
        'The path for the FAQ section that will act as anchor link. Do not add "#" symbol. (e.g. "questions-and-answers")',
    }),
    {
      title: 'Additional info for button',
      name: 'faqAdditionalInfo',
      type: 'stringTitle',
    },
    {
      // Field for button
      name: 'faqButton',
      title: 'Button',
      type: 'reference',
      description: 'Button used to direct the user to the contact us form',
      validation: null,
      to: [
        {
          type: 'buttonCustomType',
        },
      ],
    },
    {
      // Field for webschema context
      title: 'Webschema context',
      name: 'webSchemaFaqsContext',
      type: 'url',
      group: 'faqsWebschema',
      description:
        'The URL used to define the type of webschema. Google and search engines use this to enhance SEO and navigate users to our website. Ideally should be: "http://schema.org/"',
    },
    {
      // Field for webschema type
      title: 'Webschema type',
      name: 'webSchemaFaqsContextType',
      type: 'string',
      group: 'faqsWebschema',
      description: 'The type of the webschema. it should ideally be "FAQPage".',
    },
  ],
}

export default FaqSection
