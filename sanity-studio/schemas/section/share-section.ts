import { HeartIcon } from '@sanity/icons'

import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'

const ShareSection = {
  // Schema for "Social Media" section
  name: 'shareSection',
  title: 'Share Section',
  icon: HeartIcon,
  type: 'document',
  preview: createSectionPreview({ subtitle: 'localizedSubtitle.en' }),
  fields: [
    ...headerSchemaFields({
      title: 'Share section title',
      description: 'The title representing the share section.',
      subtitle: {
        showSubtitle: true,
        title: 'Share section subtitle',
        description: 'The subtitle for the social share section.',
      },
    }),
    slugSchemaField({
      title: 'Share section path',
      description:
        'The path for the share section that will act as anchor link. Do not add "#" symbol. (e.g. "share-content")',
    }),
  ],
}

export default ShareSection
