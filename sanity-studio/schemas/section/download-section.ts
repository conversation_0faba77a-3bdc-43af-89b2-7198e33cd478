import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { filterExistingReferences } from '../util-functions/schemaFilters'
import { createSectionPreview } from '../util-functions/utilFunctions'

const DownloadSection = {
  name: 'downloadSection',
  title: 'Download Section',
  type: 'document',
  preview: createSectionPreview(),
  fields: [
    ...headerSchemaFields({
      title: 'Download section title',
      description: 'The title representing the download section.',
      subtitle: {
        showSubtitle: true,
        title: 'Download section content',
        type: 'richTextLongSubtitle',
      },
    }),
    {
      name: 'sectionImage',
      title: 'Download section image',
      type: 'image',
      description: 'The image for download section',
      options: {
        hotspot: true,
      },
    },
    slugSchemaField({
      title: 'Download section path',
      description:
        'The path for the featured section that will act as anchor link. Do not add "#" symbol. (e.g. "download-mytontine")',
    }),
    {
      name: 'appLinks',
      title: 'App Links',
      type: 'array',
      of: [
        {
          type: 'reference',
          options: {
            filter: filterExistingReferences,
          },
          to: [{ type: 'linkWithImage' }],
        },
      ],
      description: 'Links to app stores or downloads with associated images.',
    },
  ],
}

export default DownloadSection
