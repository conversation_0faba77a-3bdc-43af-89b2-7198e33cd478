import type { PreviewPrepareProps } from '../../types/customTypes'
import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import linkSelector from '../navigation/linkSelector'
import { generateLinkSelectorPreview } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const InfoBannerSection = {
  // Schema for "Info Banner" section
  name: 'infoBannerSection',
  title: 'Info Banner section',
  type: 'document',
  preview: {
    select: {
      title: 'localizedTitle.en',
      pageSlug: 'allPages.slug.current',
      sectionSlug: 'allPageSections.slug.current',
      customLink: 'customLink',
      media: 'icon',
    },
    prepare(selection: PreviewPrepareProps) {
      return generateLinkSelectorPreview(selection)
    },
  },
  fields: [
    ...headerSchemaFields({
      title: 'Info banner text',
      type: 'richTextLongSubtitleLite',
      description: 'Text displayed in the info banner section.',

      icon: {
        showIcon: true,
        description: 'Info banner close icon',
        title: 'Icon used on the button to close the info banner.',
        validation: validationRules.imageValidation,
      },
    }),
    slugSchemaField({
      title: 'Info banner slug',
      description:
        'Unique identifier that is used to differentiate the section. (e.g. "coming-soon-banner")',
      source: 'infoBannerText',
    }),

    ...linkSelector.fields,
  ],
}

export default InfoBannerSection
