import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const TeamSection = {
  // Schema for "Team" section
  name: 'teamSection',
  title: 'Team Section',
  type: 'document',
  preview: createSectionPreview(),
  fields: [
    ...headerSchemaFields({
      title: `Team's title`,
      description: `The title representing the team's role within the company. (e.g., "Tontine's Team" or "Advisors")`,
      subtitle: {
        showSubtitle: true,
        title: `Team's subtitle`,
        description: `A field for adding a descriptive subtitle that explains the team's role or purpose within the company.`,
      },
      icon: {
        showIcon: true,
        title: `Team's icon`,
        description: 'An icon that represents the team.',
      },
    }),
    slugSchemaField({
      title: 'Team Slug',
      description:
        'The path for the team section that will act as anchor link. Do not add "#" symbol. (e.g. "team-players")',
    }),
    {
      // Schema that represents an array of people who are in that particular team
      name: 'teamMembers',
      title: 'Members',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [
            {
              type: 'person',
            },
          ],
        },
      ],
      description: 'Select or create team members who belong to this team.',
      validation: validationRules.isRequired,
    },
    {
      name: 'hoverEffect',
      title: 'Hover effect',
      type: 'boolean',
      description:
        'Toggle hover effect for team members for the whole section.',
      initialValue: false,
    },
  ],
}

export default TeamSection
