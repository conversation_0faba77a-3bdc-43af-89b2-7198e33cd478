import { ImageIcon } from '@sanity/icons'

import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { filterExistingReferences } from '../util-functions/schemaFilters'
import { createSectionPreview } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const CompaniesLogosSection = {
  // Schema for "Companies Logo's Section"
  name: 'companiesLogosSection',
  title: 'Companies Logos Section',
  type: 'document',
  icon: ImageIcon,
  preview: createSectionPreview(),
  fields: [
    ...headerSchemaFields({
      title: 'Companies logos section title',
      description: 'The title representing the companies logos section.',
      validation: validationRules.isRequired,
    }),
    // Field for section slug
    slugSchemaField({
      title: 'Companies logos section path',
      description:
        'The path for the companies logos section that will act as anchor link. Do not add "#" symbol. (e.g. "org-logos")',
    }),
    {
      // Field for company logo's
      name: 'companyLogos',
      title: 'Companies logo',
      type: 'array',
      description:
        'Images representing logos of media organizations that have covered us for social proof.',
      of: [
        {
          type: 'reference',
          options: {
            filter: filterExistingReferences,
          },
          to: [
            {
              type: 'customImage', // reference to imageCustomType
            },
          ],
        },
      ],
      validation: validationRules.isRequired,
    },
  ],
}

export default CompaniesLogosSection
