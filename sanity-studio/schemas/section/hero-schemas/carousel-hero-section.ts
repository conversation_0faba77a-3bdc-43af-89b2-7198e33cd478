import { headerSchemaFields } from '../../common/headerFields'
import { slugSchemaField } from '../../common/slug'
import { createSectionPreview } from '../../util-functions/utilFunctions'
import { validationRules } from '../../validation'

const CarouselHeroSection = {
  // Schema for "Carousel Hero" section, which includes hero sections and allows switching between them
  name: 'carouselHeroSection',
  title: 'Carousel hero section',
  type: 'document',
  preview: createSectionPreview({
    media: 'carouseHeroSectionPosts.0.heroSectionBackgroundImage',
  }),
  fields: [
    ...headerSchemaFields({
      title: 'Carousel hero section title',
      description: 'The title representing the carousel hero section.',
      validation: validationRules.isRequired,
    }),
    slugSchemaField({
      title: 'Carousel hero section path',
      description:
        'The path for the hero section that will act as anchor link. Do not add "#" symbol. (e.g. "hero-section")',
    }),
    // Array of hero section cards present in this carousel section
    {
      title: 'Carousel section with hero section cards',
      name: 'carouseHeroSectionPosts',
      type: 'array',
      description:
        'Hero section cards that will be present in this carousel section',
      of: [
        {
          type: 'reference',
          to: [
            {
              type: 'heroSection', // reference to heroSection
            },
          ],
        },
      ],
      validation: validationRules.isRequired,
    },
  ],
}

export default CarouselHeroSection
