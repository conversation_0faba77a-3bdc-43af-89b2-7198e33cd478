import { internalUseTitle } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'

const FooterSection = {
  // Schema for website's footer
  name: 'footer',
  title: 'Footer',
  type: 'document',
  preview: {
    select: {
      title: 'title',
      media: 'footerWebsiteLogo',
    },
  },
  groups: [
    // Groups are defined, fields have been separated based on the contents.
    {
      name: 'social',
      title: 'Social',
    },
    {
      name: 'navigation',
      title: 'Navigation',
    },
    {
      name: 'WebsiteInfo',
      title: 'Website Info',
    },
  ],
  fields: [
    internalUseTitle,
    slugSchemaField({
      title: 'Footer section anchor link',
      description:
        'The path for the footer section that will act as anchor link. Do not add "#" symbol. (e.g. "footer-section")',
    }),
    {
      // Field for Tontine's logo
      title: `Website's logo`,
      name: 'footerWebsiteLogo',
      type: 'image',
      description: `Used for rendering the website's logo on the left side of the footer.`,
      group: 'social',
      options: {
        hotspot: true,
      },
    },
    {
      // Field that references the "socialMedia" schema.
      title: 'Social media section',
      name: 'footerSocialMediaSection',
      type: 'array',
      group: 'social',
      description: `Field for adding Tontine's social media links.`,
      of: [
        {
          type: 'reference',
          to: [
            {
              type: 'socialMediaSection', // reference to "socialMediaSection"
            },
          ],
        },
      ],
    },
    {
      // Field for footer navigation bar that references to "navigationItem"
      title: 'Footer navigation menu',
      name: 'footerNavigationMenu',
      type: 'array',
      description: 'Field for adding items to the footer navigation menu.',
      group: 'navigation',
      of: [
        {
          type: 'reference',
          to: [
            {
              type: 'navigationItem', // reference to "navigationItem"
            },
          ],
        },
      ],
    },
    // Field for adding links in footer
    {
      title: 'Footer links',
      name: 'footerLinks',
      type: 'array',
      description:
        'Field for adding footer links to internal or external resources, (e.g. "Privacy Policy", "Terms of Use").',
      group: 'WebsiteInfo',
      of: [
        {
          type: 'reference',
          to: [
            {
              type: 'linkCustomType', // reference to "LinkCustomType"
            },
          ],
        },
      ],
    },
    {
      // Field for Tontine copyright and disclaimer content
      title: 'Tontine copyright and disclaimer content',
      name: 'localizedTontineCopyrightContent',
      type: 'richTextUnlimited',
      description:
        'Field for adding the copyright and disclaimer content for Tontine.',
      group: 'WebsiteInfo',
    },
  ],
}

export default FooterSection
