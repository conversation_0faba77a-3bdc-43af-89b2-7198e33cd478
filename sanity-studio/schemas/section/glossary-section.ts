import { BookIcon } from '@sanity/icons'
import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { filterExistingReferences } from '../util-functions/schemaFilters'
import { createSectionPreview } from '../util-functions/utilFunctions'

const GlossarySection = {
  name: 'glossarySection',
  title: 'Glossary section',
  type: 'document',
  icon: BookIcon,
  preview: createSectionPreview(),
  fields: [
    ...headerSchemaFields({
      title: 'Title',
      description: 'A title for the glossary section.',
      subtitle: {
        showSubtitle: true,
        title: 'Subtitle',
        description: 'A subtitle for the glossary section.',
      },
    }),
    slugSchemaField({
      title: 'Glossary Section slug',
      description:
        'Unique identifier that is used to differentiate the section. (e.g. "Everything you need to know")',
    }),
    {
      name: 'glossaries',
      title: 'Glossaries',
      type: 'array',
      of: [
        {
          type: 'reference',
          options: {
            filter: filterExistingReferences,
          },
          to: [{ type: 'glossary' }],
        },
      ],
      description: 'List of glossaries for this section.',
    },
  ],
}

export default GlossarySection
