import { CommentIcon } from '@sanity/icons'

import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const TestimonialSection = {
  // Schema for "Testimonials" section
  name: 'testimonialSection',
  title: 'Testimonial section',
  icon: CommentIcon,
  type: 'document',
  preview: createSectionPreview(),
  fields: [
    ...headerSchemaFields({
      title: 'Testimonial title',
      description: 'The title representing the testimonials section.',
    }),
    slugSchemaField({
      title: 'Testimonial section path',
      description:
        'The path for the featured section that will act as anchor link. Do not add "#" symbol. (e.g. "testimonials-info")',
    }),
    {
      title: 'Testimonial posts',
      name: 'testimonialPosts',
      type: 'array',
      of: [
        {
          type: 'testimonialCustomType',
        },
      ],
      description: 'List of testimonial posts within the section.',
      validation: validationRules.isRequired,
    },
  ],
}

export default TestimonialSection
