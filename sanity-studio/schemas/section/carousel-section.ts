import { InlineElementIcon } from '@sanity/icons'

import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const CarouselSection = {
  // Schema for "Carousel" section
  name: 'carouselSection',
  title: 'Carousel section',
  icon: InlineElementIcon,
  type: 'document',
  preview: createSectionPreview(),
  fields: [
    ...headerSchemaFields({
      description: 'The title representing the carousel section.',
    }),
    slugSchemaField({
      title: 'Carousel section path',
      description:
        'The path for the carousel section that will act as anchor link. Do not add "#" symbol. (e.g. "carousel-spotlight")',
    }),
    // Carousel posts
    {
      title: 'Carousel posts',
      name: 'carouselPosts',
      type: 'array',
      of: [
        {
          type: 'carouselCustomType',
        },
      ],
      description: 'Collection of posts displayed in the carousel section.',
      validation: validationRules.isRequired,
    },
    // Text under carousel section for complaints and feedback from user
    {
      title: 'Carousel content feedback',
      name: 'localizedCarouselSectionFeedback',
      type: 'richTextLite',
      description:
        'Field for users to provide feedback or complains about the content displayed in the carousel section, including a link to the contact us page.',
    },
  ],
}

export default CarouselSection
