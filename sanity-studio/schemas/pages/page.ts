import PageDisclaimer from '../../components/PageDisclaimer'
import { allSectionTypes } from '../../structure-tool/sections-structure'
import { slugSchemaField } from '../common/slug'
import SEOPageData from '../sanity-custom-types/SEOCustomType'
import { filterExistingReferences } from '../util-functions/schemaFilters'
import { createSectionPreview } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const page = {
  // Schema for "Pages"
  name: 'page',
  title: 'Page',
  type: 'document',
  preview: createSectionPreview({
    sectionImage: 'seoImage',
    subtitle: 'pageSlug.current',
  }),

  groups: [
    // Groups are defined, fields have been separated based on the contents.
    {
      name: 'seoData',
      title: 'SEO Data',
    },
    {
      name: 'slug',
      title: 'Page Slug',
    },
    {
      name: 'sections',
      title: 'Page Sections',
    },
  ],
  fieldsets: [
    {
      name: 'seoData',
      title: 'Page SEO',
      description: 'SEO data for the page',
      options: { collapsible: false, collapsed: false },
    },
  ],
  fields: [
    {
      type: 'string',
      name: 'disclaimer',
      readOnly: true,
      components: {
        input: PageDisclaimer,
      },
    },
    ...SEOPageData,

    slugSchemaField({
      name: 'pageSlug',
      title: 'Page slug',
      description: 'A URL-friendly version of the SEO title for the page.',
      group: 'slug',
      isRequired: false,
    }),

    {
      // Field for sections that appear on page
      type: 'array',
      name: 'pageSections',
      title: 'Sections',
      description: 'The different sections that appear on the page.',
      group: 'sections',
      validation: validationRules.isRequired,
      of: [
        {
          type: 'reference',
          title: 'Select a section from the list',
          options: {
            filter: filterExistingReferences,
          },
          to: allSectionTypes,
        },
      ],
    },
  ],
}

export { page }
