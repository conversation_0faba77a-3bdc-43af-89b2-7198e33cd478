import { LinkIcon } from '@sanity/icons'

import type { PreviewPrepareProps } from '../../types/customTypes'
import { headerSchemaFields } from '../common/headerFields'
import { filterExistingReferences } from '../util-functions/schemaFilters'
import { generateLinkSelectorPreview } from '../util-functions/utilFunctions'
import linkSelector from './linkSelector'

const NavigationItem = {
  // Schema for "Navigation Item"
  name: 'navigationItem',
  title: 'Navigation Item',
  type: 'document',
  icon: LinkIcon,
  preview: {
    select: {
      title: 'stringTitle.en',
      pageSlug: 'allPages.pageSlug.current',
      pageRef: 'allPages._ref',
      sectionSlug: 'allPageSections.slug.current',
      customLink: 'customLink',
      media: 'allPages.pageSeo.seoImage',
    },
    prepare(selection: PreviewPrepareProps) {
      return generateLinkSelectorPreview(selection)
    },
  },
  fields: [
    ...headerSchemaFields({
      title: 'Navigation item title',
      type: 'stringTitle',
      description: 'The title of the navigation item.',
    }),

    ...linkSelector.fields,
    {
      // Field for secondary navigation items
      title: 'Secondary navigation',
      name: 'navigationSubMenuItems',
      type: 'array',
      description:
        'An array of sub-menu items associated with a specific navigation item.',
      of: [
        {
          type: 'reference',
          options: {
            filter: filterExistingReferences,
          },
          to: [
            {
              type: 'subMenuItem',
            },
          ],
        },
      ],
    },
  ],
}

export default NavigationItem
