import { validationRules } from '../validation'

const corporationSchema = {
  // Website webSchema for SEO
  name: 'corporationSchema',
  title: 'Corporation Schema',
  type: 'document',
  fields: [
    {
      title: 'Property: Corporation name  || Enter: text',
      name: 'corporationSchemaName',
      type: 'string',
      description: 'Official name of the corporation (e.g., "Tontine Trust").',
      validation: validationRules.isRequired,
    },
    {
      title: 'Property: Corporation description  || Enter: text',
      name: 'corporationSchemaDescription',
      type: 'string',
      description:
        'Description of the corporation (e.g., "Tontine Trust is a ...").',
      validation: validationRules.isRequired,
    },
    {
      title: 'Property: Corporation working hours  || Enter: text',
      name: 'corporationSchemaWorkingHours',
      type: 'string',
      description:
        'Working hours of the corporation schema (e.g., "Mo-Fr 07:00-23:00").',
    },
    {
      title: 'Property: Link to feedback webpage  || Enter: Url',
      name: 'corporationSchemaActionableFeedbackPolicy',
      type: 'url',
      description:
        'URL that points to the webpage where corporation feedback policy is described (e.g., "https://tontine.com/contact-us/").',
    },
    {
      title: 'Property: Corporation street address  || Enter: text',
      name: 'corporationSchemaStreetAddress',
      type: 'string',
      description:
        'Specifies the street address of the corporation (e.g., "123 Main Street").',
    },
    {
      title: 'Property: City where the corporation is located  || Enter: text',
      name: 'corporationSchemaAddressLocality',
      type: 'string',
      description:
        'Specifies the locality or city where the corporation is located (e.g., "New York").',
    },
    {
      title: 'Property: Region where the corporation is located || Enter: text',
      name: 'corporationSchemaAddressRegion',
      type: 'string',
      description:
        'Specifies the region where the corporation is located (e.g., "NY").',
    },
    {
      title: 'Property: Corporation postal code  || Enter: text',
      name: 'corporationSchemaPostalCode',
      type: 'string',
      description:
        'Specifies the postal code for the corporation (e.g., "10001").',
    },
    {
      title:
        'Property: Country where the corporation is located || Enter: text',
      name: 'corporationSchemaAddressCountry',
      type: 'string',
      description:
        'Specifies the country where the corporation is located (e.g., "US").',
    },
    {
      title: 'Property: Corporation privacy policy webpage link  || Enter: Url',
      name: 'corporationSchemaCorrectionsPolicy',
      type: 'url',
      description:
        'URL that points to the webpage where corporation corrections policy is described.',
    },
    {
      title:
        'Property: Corporation diversity policy webpage link   || Enter: Url',
      name: 'corporationSchemaDiversityPolicy',
      type: 'url',
      description:
        'URL that points to the webpage where corporation diversity policy is described.',
    },
    {
      title:
        'Property: Corporation diversity staffing webpage link  || Enter: Url',
      name: 'corporationSchemaDiversityStaffingReport',
      type: 'url',
      description:
        'URL that points to the webpage where corporation diversity staffing report is described.',
    },
    {
      title: 'Property: Company duns  || Enter: text',
      name: 'corporationSchemaDuns',
      type: 'string',
      description:
        'The Dun & Bradstreet DUNS number for identifying an organization or business person.',
    },
    {
      title: 'Property: Corporation email  || Enter: text',
      name: 'corporationSchemaEmail',
      type: 'string',
      description: 'Corporation email address.',
    },
    {
      title: 'Property: Corporation ethics policy webpage link  || Enter: Url',
      name: 'corporationSchemaEthicsPolicy',
      type: 'url',
      description:
        'URL that points to the webpage where corporation ethics policy is described.',
    },
    {
      title: 'Property: Corporation fax number  || Enter: text',
      name: 'corporationSchemaFaxNumber',
      type: 'string',
      description: 'Corporation fax number.',
    },
    {
      title: 'Property: Corporation founder  || Enter: text',
      name: 'corporationSchemaFounder',
      type: 'string',
      description: 'A person who founded this corporation.',
    },
    {
      title: 'Property: Corporation founding date  || Enter: date',
      name: 'corporationSchemaFoundingDate',
      type: 'date',
      description: 'The date that this corporation was founded.',
    },
    {
      title: 'Property: Corporation address locality  || Enter: text',
      name: 'corporationSchemaFoundingAddressLocality',
      type: 'string',
      description:
        'The locality where the corporation was founded (e.g., "New York").',
    },
    {
      title: 'Property: Corporation address region  || Enter: text',
      name: 'corporationSchemaFoundingAddressRegion',
      type: 'string',
      description: 'The region where the corporation was founded (e.g., "NY").',
    },
    {
      title: 'Property: Corporation postal code  || Enter: text',
      name: 'corporationSchemaFoundingPostalCode',
      type: 'string',
      description:
        'The postal code where the corporation was founded (e.g., "10001").',
    },
    {
      title: 'Property: Corporation address country  || Enter: text',
      name: 'corporationSchemaFoundingAddressCountry',
      type: 'string',
      description:
        'The country where the corporation was founded (e.g., "US").',
    },
    {
      title: 'Property: Corporation global location number  || Enter: text',
      name: 'corporationSchemaGlobalLocationNumber',
      type: 'string',
      description:
        'The Global Location Number (GLN, sometimes also referred to as International Location Number or ILN) of the respective corporation, person, or place. The GLN is a 13-digit number used to identify parties and physical locations.',
    },
    {
      title: 'Property: Keywords associated with Tontine || Enter: text',
      name: 'corporationSchemaKeywords',
      type: 'array',
      of: [{ type: 'string' }],
      description:
        'Keywords associated with the corporation.Multiple textual entries in a keywords list are typically delimited by commas.',
    },
    {
      title: 'Property: Experts for Tontine  || Enter: text or Url',
      name: 'corporationSchemaKnowsAbout',
      type: 'string',
      description:
        'Name of a Person, and less typically of an Organization, to indicate a topic that is known about - suggesting possible expertise but not implying it. We do not distinguish skill levels here, or relate this to educational content, events, objectives or JobPosting descriptions.',
    },
    {
      title: 'Property: Corporation known languages  || Enter: text',
      name: 'corporationSchemaKnowsLanguage',
      type: 'string',
      description:
        'Of a Person, and less typically of an Organization, to indicate a known language. We do not distinguish skill levels or reading/writing/speaking/signing here. Use language codes from the IETF BCP 47 standard.',
    },
    {
      title: 'Property: Official corporation name  || Enter: text',
      name: 'corporationSchemaLegalName',
      type: 'string',
      description:
        'The official name of the corporation (e.g., "The registered company name).',
    },
    {
      title: 'Property: Corporation lei code  || Enter: text',
      name: 'corporationSchemaLeiCode',
      type: 'string',
      description:
        'An organization identifier that uniquely identifies a legal entity as defined in ISO 17442.',
    },
    {
      title: 'Property: Corporation logo  || Enter: URL',
      name: 'corporationSchemaLogo',
      type: 'url',
      description: 'URL of an image for the logo of the corporation.',
    },
    {
      title: 'Property: Corporation number of employees  || Enter: number',
      name: 'corporationSchemaNumberOfEmployees',
      type: 'number',
      description: 'The number of employees in the corporation.',
    },
    {
      title: 'Property: Corporation product  || Enter: text',
      name: 'corporationSchemaOwns',
      type: 'string',
      description: 'Product owned by the corporation (e.g., "Tontine").',
    },
    {
      title: 'Property: Corporation publishing principles  || Enter: Url',
      name: 'corporationSchemaPublishingPrinciples',
      type: 'url',
      description:
        'The publishingPrinciples property indicates (typically via URL) a document describing the editorial principles of an Organization (or individual (e.g., "A Person writing a blog")) that relate to their activities as a publisher (e.g., "ethics or diversity policies)',
    },
    {
      title: 'Property: Slogan  || Enter: text',
      name: 'corporationSchemaSlogan',
      type: 'string',
      description: 'A slogan or motto associated with the corporation.',
    },
    {
      title: 'Property: Corporation tax ID  || Enter: text',
      name: 'corporationSchemaTaxID',
      type: 'string',
      description:
        'The Tax / Fiscal ID of the organization or person (e.g., "the TIN in the US or the CIF/NIF in Spain.").',
    },
    {
      title: 'Property: Corporation telephone number  || Enter: text',
      name: 'corporationSchemaTelephone',
      type: 'string',
      description: 'Corporation telephone number.',
    },
    {
      title: 'Property: Corporation vat ID  || Enter: text',
      name: 'corporationSchemaVatID',
      type: 'string',
      description: 'The Value-added Tax ID of the organization or person.',
    },
    {
      title: 'Property: Corporation alternate name  || Enter: text',
      name: 'corporationSchemaAlternateName',
      type: 'string',
      description: 'An alias for the corporation (e.g., "TontineIRA").',
    },
    {
      title: 'Property: Corporation website link  || Enter: URL',
      name: 'corporationSchemaUrl',
      type: 'url',
      description: 'URL of the corporation website.',
    },
  ],
}

export { corporationSchema }
