import { validationRules } from '../validation'

const investmentOrDepositSchema = {
  // Website webSchema for SEO
  name: 'investmentOrDepositSchema',
  title: 'Investment Or Deposit Schema',
  type: 'document',
  fields: [
    {
      title: 'Property: Name  || Enter: Text',
      name: 'investmentOrDepositSchemaName',
      type: 'string',
      validation: validationRules.isRequired,
      description: 'Name of the investment or deposit schema.',
    },
    {
      title: 'Property: Description  || Enter: Text',
      name: 'investmentOrDepositSchemaDescription',
      type: 'string',
      description: 'Description of the investment or deposit.',
      validation: validationRules.isRequired,
    },
    {
      title: 'Property: Min value of the investment || Enter: Number',
      name: 'investmentOrDepositSchemaMinValue',
      type: 'number',
      description: 'Minimum amount value of the investment or deposit.',
    },
    {
      title: 'Property: Max value of the investment || Enter: Number',
      name: 'investmentOrDepositSchemaMaxValue',
      type: 'number',
      description:
        'Maximum amount value of the investment or deposit ( In dollars ).',
    },
    {
      title: 'Property: Annual Percentage Rate || Enter: Number',
      name: 'investmentOrDepositSchemaAnnualPercentageRate',
      type: 'number',
      description:
        'The annual rate that is charged for borrowing (or made by investing), expressed as a single percentage number that represents the actual yearly cost of funds over the term of a loan. This includes any fees or additional costs associated with the transaction.',
    },
    {
      title:
        'Property: Fees and commissions specification || Enter: Text or URL',
      name: 'investmentOrDepositSchemaFeesAndCommissionsSpecification',
      type: 'string',
      description:
        'Description of fees, commissions, and other terms applied either to a class of financial product, or by a financial service organization.',
    },
    {
      title: 'Property: Interest Rate || Enter: Number',
      name: 'investmentOrDepositSchemaInterestRate',
      type: 'number',
      description:
        'The interest rate, charged or paid, applicable to the financial product. Note: This is different from the calculated annualPercentageRate.',
    },
    {
      title: 'Property: Geographic area served || Enter: Text',
      name: 'investmentOrDepositSchemaAreaServed',
      type: 'string',
      description:
        'The geographic area where a service or offered item is provided (e.g., "United States").',
    },
    {
      title: 'Property: Audience type || Enter: Text',
      name: 'investmentOrDepositSchemaAudienceType',
      type: 'string',
      description:
        'The target group associated with a given audience (e.g., "Individuals").',
    },
    {
      title: 'Property: Company awards || Enter: Text',
      name: 'investmentOrDepositSchemaAward',
      type: 'string',
      description: 'An award won by or for this item.',
    },
    {
      title: 'Property: Brand name || Enter: Text',
      name: 'investmentOrDepositSchemaBrandName',
      type: 'string',
      description:
        'Enter the brand(s) name associated with a product or service, or the brand(s) maintained by an organization or business person.',
    },
    {
      title: 'Property: Brand url || Enter: Text',
      name: 'investmentOrDepositSchemaBrandUrl',
      type: 'string',
      description: 'Website url of the brand',
    },
    {
      title: 'Property: Investment or deposit category || Enter: Text',
      name: 'investmentOrDepositSchemaCategory',
      type: 'string',
      description:
        'A category for the item. Greater signs or slashes can be used to informally indicate a category hierarchy (e.g., "Stocks", "Bonds", "Mutual Funds", "Real Estate", "Commodities", etc.).',
    },
    {
      title: 'Property: Related product || Enter: Text',
      name: 'investmentOrDepositSchemaIsRelatedToName',
      type: 'string',
      description:
        'The name of a pointer to another, somehow related product (or multiple products) or concept (e.g., "Pension").',
    },
    {
      title: 'Property: Related product url || Enter: Text or URL',
      name: 'investmentOrDepositSchemaIsRelatedToUrl',
      type: 'string',
      description:
        'The url of a pointer to another, somehow related product (or multiple products) or concept towards the Investment or Deposit.',
    },
    {
      title:
        'Property: Associated investment or deposit logo url || Enter: URL',
      name: 'investmentOrDepositSchemaLogoUrl',
      type: 'url',
      description:
        'URL of the associated logo for the investment or deposit, or just companies logo.',
    },
    {
      title: 'Property: Provider Mobility || Enter: Text',
      name: 'investmentOrDepositSchemaProviderMobility',
      type: 'string',
      description:
        'Indicates the mobility of a provided service (e.g., "static", "dynamic", etc.).',
    },
    {
      title: 'Property: Tontine slogan or motto. || Enter: Text',
      name: 'investmentOrDepositSchemaSlogan',
      type: 'string',
      description:
        'A slogan or motto associated with the company (e.g, "Live longer", "Earn more", etc.)',
    },
    {
      title: 'Property: URL of terms of service webpage || Enter: Text || URL',
      name: 'investmentOrDepositSchemaTermsOfService',
      type: 'string',
      description:
        'Terms of service documentation of the investment or deposit (e.g., "https://tontine.com/terms-conditions/", etc.).',
    },
    {
      title: 'Property: Alternate Name || Enter: Text',
      name: 'investmentOrDepositSchemaAlternateName',
      type: 'string',
      description: 'An alias for the item. (e.g., "TontineIRA")',
    },
    {
      title: 'Property: Investment url || Enter: URL',
      name: 'investmentOrDepositSchemaUrl',
      type: 'url',
      description:
        'URL of the item or URL which leads to more information regarding the investment or deposit.',
    },
    {
      // Field for company or organization links ("Same as" title is required)
      title: 'Property: Same As || Enter: URL',
      name: 'investmentOrDepositSchemaSameAs',
      type: 'array',
      of: [{ type: 'string' }],
      description: `URL of a reference Web page that unambiguously indicates the item's identity (e.g., "URL of the item's Wikipedia page", "Wikidata entry", "official website" or "social networks")`,
    },
  ],
}

export { investmentOrDepositSchema }
