import './components/layout.css'

import { visionTool } from '@sanity/vision'
import type { ComponentType } from 'react'
import type { FieldProps, SchemaTypeDefinition } from 'sanity'
import { defineConfig } from 'sanity'
import { unsplashImageAsset } from 'sanity-plugin-asset-source-unsplash'
import { media } from 'sanity-plugin-media'
import { muxInput } from 'sanity-plugin-mux-input'
import { netlifyTool } from 'sanity-plugin-netlify'
import { structureTool } from 'sanity/structure'

import { table } from '@sanity/table'
import { CompanyLogo } from './components/CompanyLogo'
import { CustomFieldInput } from './components/CustomFieldInput'
import { CustomToolMenu } from './components/CustomToolMenu'
import { defaultDocumentNode } from './defaultDocumentNode'
import { schemaTypes } from './schemas'
import { customStructureTool } from './structure-tool/structure-tool'

export default defineConfig({
  name: 'production-workspace',
  title: 'Tontine Trust',
  icon: CompanyLogo,

  projectId: 'hl9czw39',
  dataset: 'production',

  form: {
    components: {
      field: CustomFieldInput as unknown as ComponentType<FieldProps>,
    },
  },

  beta: {
    create: {
      startInCreateEnabled: false,
    },
  },

  plugins: [
    structureTool({
      structure: customStructureTool,
      defaultDocumentNode,
    }),
    media(),
    muxInput(),
    visionTool(),
    netlifyTool(),
    unsplashImageAsset(),
    table(),
  ],
  document: {
    comments: {
      enabled: false,
    },
  },
  releases: {
    enabled: false,
  },
  tasks: {
    enabled: false,
  },
  scheduledPublishing: {
    enabled: false,
  },
  schema: {
    types: schemaTypes as Array<SchemaTypeDefinition>,
  },
  studio: {
    components: {
      toolMenu: CustomToolMenu,
    },
  },
})
