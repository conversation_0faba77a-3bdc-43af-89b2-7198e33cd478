{"name": "tontine-trust", "private": true, "version": "1.0.0", "main": "package.json", "license": "UNLICENSED", "scripts": {"dev-sanity": "sanity dev", "dev-sanity:ninja": "console-ninja sanity dev", "build-sanity": "sanity build", "preview": "npm run build && sanity preview", "deploy": "sanity deploy", "deploy-graphql": "sanity graphql deploy"}, "keywords": ["sanity"], "dependencies": {"@sanity/asset-utils": "^2.2.1", "@sanity/client": "^6.29.0", "@sanity/icons": "^3.7.0", "@sanity/table": "^1.1.3", "@sanity/ui": "^2.15.13", "@sanity/vision": "^3.86.0", "react": "^18.3.1", "react-dom": "^18.3.1", "sanity": "^3.86.0", "sanity-plugin-asset-source-unsplash": "^3.0.3", "sanity-plugin-iframe-pane": "^3.2.1", "sanity-plugin-media": "^2.4.2", "sanity-plugin-mux-input": "^2.8.0", "sanity-plugin-netlify": "^1.1.2", "styled-components": "6.1.17"}, "devDependencies": {"@sanity/cli": "^3.86.0", "typescript": "^5.8.2"}}