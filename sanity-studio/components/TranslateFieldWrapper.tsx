import { TranslateIcon } from '@sanity/icons'
import { isProd, set, useFormValue } from 'sanity'

import type {
  ComponentProps,
  SupportedLanguagesType,
} from '../types/customTypes'
import { WrappedField } from './WrappedField'

/** TranslateFieldWrapper is a component that renders a field with a translate button.
 * It wraps the `WrappedField` component and adds a button with a translate icon.
 * When the button is clicked, it call translates the english value to the sent
 * language and sets the new translated value to the current field.
 */
export const TranslateFieldWrapper = ({
  customClass = 'field-wrapper',
  buttonClass = 'field-button',
  customPortableWrapperClass = 'field-portable-wrapper',
  //   currentLanguage,
  props,
}: ComponentProps & {
  buttonClass: string
  customPortableWrapperClass?: string
  currentLanguage: SupportedLanguagesType
  props: ComponentProps
}) => {
  const document = useFormValue([]) as { en: string }
  const currentPath = props?.path || []
  const parentPath = currentPath?.slice?.(0, -1)

  const getNestedValue = <T,>(obj: T, path: Array<string>) => {
    // @ts-expect-error don't know how to type properly
    return path.reduce((acc, key) => acc?.[key], obj)
  }

  const handleTranslate = () => {
    if (Array.isArray(parentPath)) {
      const parentObject = getNestedValue<{ en: string }>(document, parentPath)
      const enValue = parentObject?.en
      // Would make call here using the currentLanguage and the enValue
      if (enValue && props.onChange) {
        props.onChange(set(enValue))
      }
    }
  }

  return (
    <div className={customClass}>
      {customPortableWrapperClass ? (
        <WrappedField props={props} customClass={customPortableWrapperClass} />
      ) : (
        props?.renderDefault?.(props)
      )}
      {!isProd && (
        <button className={buttonClass} onClick={handleTranslate} type='button'>
          <TranslateIcon />
        </button>
      )}
    </div>
  )
}

/** USAGE: 
components: lang.isDefault
  ? {
      input: (props: ComponentProps) =>
        WrappedField({
          customClass: isLite ? 'rich-text-lite' : '',
          props,
        }),
    }
  : {
      input: (props: ComponentProps) =>
        TranslateFieldWrapper({
          customClass: fieldWrapperClass ?? 'locale-string',
          buttonClass: 'translate-button',
          customPortableWrapperClass: isLite ? 'rich-text-lite' : '',
          currentLanguage: lang,
          props,
        }),
    }, 
*/
