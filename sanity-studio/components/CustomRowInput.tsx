import { Dialog, DialogProvider, TextInput } from '@sanity/ui'
import type React from 'react'
import type { ObjectInputProps } from 'sanity'

import { SANITY_CONSTANTS } from '../assets/constants'
import {
  useFetchSectionImage,
  usePlacementInputs,
} from '../sanity-hooks/walkthroughHooks'
import type { WalkthroughPinPlacementType } from '../types/customTypes'

const {
  DESKTOP_COLUMNS,
  DESKTOP_ROWS,
  DESKTOP_SIZE,
  MOBILE_COLUMNS,
  MOBILE_ROWS,
  MOBILE_SIZE,
} = SANITY_CONSTANTS

type ColumnRowInputProps = ObjectInputProps<WalkthroughPinPlacementType> & {
  document: { walkthroughGridImage: { asset: { url: string } } }
  mobileSection?: boolean
}

const styles = {
  button: {
    padding: '0.5rem 1rem',
    backgroundColor: '#007BFF',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
  },
  gridImage: {
    position: 'absolute',
    top: '0',
    left: '0',
    height: '100%',
    zIndex: 0,
  } as const,
  gridCell: {
    border: '.05px solid rgba(0, 0, 0, 0.15)',
    borderRadius: '.5rem',
    cursor: 'pointer',
    transition: 'background-color 0.2s',
    zIndex: 1,
  },
  container: {
    display: 'grid',
    gap: '.5rem',
    border: '1px solid rgb(206, 112, 49)',
    padding: '.5rem',
    borderRadius: '.5rem',
  },
  errorMessage: {
    color: 'rgb(206, 60, 49)',
    fontSize: '.85rem',
    margin: 0,
    fontWeight: 400,
  },
  messageText: {
    fontSize: '.8rem',
    margin: 0,
  },
  goodMessage: {
    color: '#2975BF',
    fontWeight: 600,
  },
}

/**
 * ColumnRowInput component renders a two inputs and button that can be clicked
 * to select a specific cell where a pin should be located.
 * It uses the `useFetchSectionImage` hook to fetch section image details and the `usePlacementInputs` hook
 * to manage the state of the inputs and handle cell clicks.
 */
export const ColumnRowInput: React.FC<ColumnRowInputProps> = ({
  value,
  onChange,
  mobileSection,
}) => {
  const { sectionImageUrl, sectionTitle, isMobile, imageSizes } =
    useFetchSectionImage(mobileSection)

  const { inputs, isModalOpen, setIsModalOpen, handleChange, handleCellClick } =
    usePlacementInputs({ value, onChange })

  const initialColumns = isMobile ? MOBILE_COLUMNS : DESKTOP_COLUMNS
  const initialRows = isMobile ? MOBILE_ROWS : DESKTOP_ROWS

  const renderGrid = () =>
    Array.from({ length: initialRows }).map((_r, row) =>
      Array.from({ length: initialColumns }).map((_c, col) => (
        <button
          type='button'
          className='grid-cell'
          key={`${row + 1}-${col + 1}`}
          style={styles.gridCell}
          onClick={() => handleCellClick(row + 1, col + 1)}
        />
      ))
    )

  const gridContainerStyle = {
    display: 'grid',
    gridTemplateColumns: `repeat(${isMobile ? MOBILE_COLUMNS : DESKTOP_COLUMNS}, 1rem)`,
    gridTemplateRows: `repeat(${initialRows}, 1rem)`,
    position: 'relative',
  } as const

  if (!sectionImageUrl) {
    return (
      <div style={styles.container}>
        <h3 style={styles.errorMessage}>
          <span style={styles.goodMessage}>{sectionTitle}</span> is missing an
          image
        </h3>
        <p style={styles.messageText}>
          Required size: Height:{' '}
          {mobileSection ? MOBILE_SIZE.height : DESKTOP_SIZE.height}px; Width:{' '}
          {mobileSection ? MOBILE_SIZE.width : DESKTOP_SIZE.width}px
        </p>
      </div>
    )
  }

  if ((mobileSection && !isMobile) || (!mobileSection && isMobile)) {
    return (
      <div style={styles.container}>
        <h3 style={styles.errorMessage}>
          The image in <span style={styles.goodMessage}>{sectionTitle}</span> is
          not sized properly
        </h3>
        <p style={styles.messageText}>
          Required size: Height:{' '}
          <span style={styles.goodMessage}>{MOBILE_SIZE.height}px</span>; Width:{' '}
          <span style={styles.goodMessage}>{MOBILE_SIZE.width}px</span>
        </p>
        <p style={styles.messageText}>
          Current size: Height:{' '}
          <span
            style={{
              color:
                imageSizes?.height === MOBILE_SIZE.height
                  ? styles.goodMessage.color
                  : styles.errorMessage.color,
              fontWeight: 500,
            }}
          >
            {imageSizes?.height}px
          </span>
          ; Width:{' '}
          <span
            style={{
              color:
                imageSizes?.width === MOBILE_SIZE.width
                  ? styles.goodMessage.color
                  : styles.errorMessage.color,
              fontWeight: 500,
            }}
          >
            {imageSizes?.width}px
          </span>
        </p>
      </div>
    )
  }

  return (
    <>
      <div style={{ display: 'flex', gap: '.5rem' }}>
        <TextInput
          type='number'
          value={inputs.column}
          onChange={(e) =>
            handleChange('column', Number((e.target as HTMLInputElement).value))
          }
          placeholder='Column'
          max={isMobile ? MOBILE_COLUMNS : DESKTOP_COLUMNS}
          min='1'
        />
        <TextInput
          type='number'
          value={inputs.row}
          onChange={(e) =>
            handleChange('row', Number((e.target as HTMLInputElement).value))
          }
          placeholder='Row'
          min='1'
          max={isMobile ? MOBILE_ROWS : DESKTOP_ROWS}
          style={{ flex: 1 }}
        />
        {sectionImageUrl && (
          <button
            onClick={() => setIsModalOpen(true)}
            style={styles.button}
            type='button'
          >
            Open Grid
          </button>
        )}
      </div>
      {isModalOpen && (
        <DialogProvider>
          <Dialog
            id='grid-dialog'
            header='Select Grid Position'
            onClose={() => setIsModalOpen(false)}
            open={isModalOpen}
            width={1}
          >
            <div style={gridContainerStyle}>
              {sectionImageUrl && (
                <img src={sectionImageUrl} alt='Pin' style={styles.gridImage} />
              )}
              {renderGrid()}
            </div>
          </Dialog>
        </DialogProvider>
      )}
    </>
  )
}
