import { Box, Button, Checkbox, Dialog, Flex, Stack, Text } from '@sanity/ui'
import { type ReactElement, useState } from 'react'
import { type FormSetPatch, set } from 'sanity'
import { convertImageIdToUrl } from '../schemas/util-functions/utilFunctions'

/** A custom input component for Sanity Studio that allows users to add and adjust elliptical corners on images.
 * Provides a visual interface with live preview and controls for corner radius adjustment. */
export default function SectionImageRoundedCorners(props: {
  value?: {
    asset?: { _ref?: string }
    cornerRadius?: number
    rounded?: boolean
  }
  onChange: (patch: FormSetPatch) => void
  renderDefault: (props: unknown) => ReactElement
  path: Array<string>
}) {
  const { value, onChange, renderDefault } = props
  const [open, setOpen] = useState(false)
  const [radius, setRadius] = useState(value?.cornerRadius ?? 30)
  const [enabled, setEnabled] = useState(value?.rounded ?? true)

  const imageUrl = convertImageIdToUrl(value?.asset?._ref)

  const handleSliderChange = (val: number) => {
    setRadius(val)
    onChange(set({ ...value, cornerRadius: val }))
  }

  const handleCheckboxChange = () => {
    const newValue = !enabled
    setEnabled(newValue)
    onChange(set({ ...value, rounded: newValue }))
  }

  return (
    <Stack space={3}>
      <>
        {renderDefault(props)}
        <Text weight='semibold'>Image rounded: {enabled ? 'Yes' : 'No'}</Text>
      </>

      {value?.asset && (
        <>
          <Button
            text='Elliptical corners'
            tone='primary'
            onClick={() => setOpen(true)}
          />
          {open && (
            <Dialog
              id='elliptical-corners-modal'
              header='Adjust Elliptical Corners'
              onClose={() => setOpen(false)}
              width={2}
            >
              <Box padding={4}>
                <Flex gap={4} direction='column'>
                  {imageUrl && (
                    <img
                      src={imageUrl}
                      alt='Preview'
                      style={{
                        maxWidth: '100%',
                        height: 'auto',
                        clipPath: enabled ? `ellipse(${radius}% 120%)` : 'none',
                      }}
                    />
                  )}
                  <Flex align='center' gap={2}>
                    <Checkbox
                      checked={enabled}
                      onChange={handleCheckboxChange}
                    />
                    <Text>Use elliptical corners</Text>
                  </Flex>

                  <Box disabled={!enabled}>
                    <Text>Corner Radius percentage: {radius}%</Text>
                    <input
                      type='range'
                      name='radius'
                      min='25'
                      max='50'
                      value={radius}
                      onChange={(e) =>
                        handleSliderChange(Number(e.target.value))
                      }
                      disabled={!enabled}
                      style={{ width: '100%' }}
                    />
                  </Box>
                </Flex>
              </Box>
            </Dialog>
          )}
        </>
      )}
    </Stack>
  )
}
