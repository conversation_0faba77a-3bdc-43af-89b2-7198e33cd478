import { useEffect, useState } from 'react'
import { ArrayOfPrimitivesFunctions, unset, useFormValue } from 'sanity'

import type { Props } from '../types/customTypes'

/** Custom component that limits amount of array items to validation.max,
 * removes the add more button once validation is met.
 */
const LimitArrayItems: React.FC<Props> = (props) => {
  const valRules = props?.schemaType?.validation?.[0]?._rules ?? []
  const max = valRules.find((r) => r.flag === 'max')?.constraint
  const total = props?.value?.length ?? 0
  if (max && !Number.isNaN(max) && total >= max) return null
  // @ts-expect-error
  return <ArrayOfPrimitivesFunctions {...props} />
}
/** Custom component that unsets component values when 'dropdown' value changes.
 */
export function ArrayTypeFilter(props: Props) {
  const { renderDefault, onChange } = props
  const typeToInclude = useFormValue(['dropdown']) as string
  const [currentType, setCurrentType] = useState(typeToInclude)

  useEffect(() => {
    if (currentType !== typeToInclude) {
      const patch = unset()
      onChange(patch)
      setCurrentType(typeToInclude)
    }
  }, [typeToInclude, currentType, onChange])

  return renderDefault({ ...props, onChange, arrayFunctions: LimitArrayItems })
}
