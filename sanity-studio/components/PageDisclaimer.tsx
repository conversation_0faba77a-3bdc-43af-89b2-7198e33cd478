import { useFormValue } from 'sanity'

import { useSanityFetch } from '../sanity-hooks/useSanityFetch'
import { cleanDomain } from '../schemas/util-functions/utilFunctions'
import type { Doc } from '../types/customTypes'
import { Disclaimer } from './Disclaimer'

const baseWrapperStyling = {
  textAlign: 'center',
  cursor: 'default',
  userSelect: 'none',
  padding: '.25rem',
  borderRadius: '5px',
  boxShadow: '0px 0px 5px rgba(0, 0, 0, 0.199)',
} as const

function PageDisclaimer() {
  // TODO: Use built in disclaimer levels
  const document = useFormValue([]) as Doc
  const pageId = document?._id

  const cleanPageId = pageId.replace(/drafts\./g, '')

  const { data: websiteTitles, loading } = useSanityFetch<Array<string>>(
    `*[_type == "website" && references($pageId)].pageDomain`,
    { pageId: cleanPageId }
  )

  let colorScheme = 'var(--error-red)'
  if (websiteTitles?.length === 1) {
    colorScheme = 'var(--cerulean-blue)'
  } else if (websiteTitles && websiteTitles.length > 1) {
    colorScheme = 'var(--jade-green)'
  }

  const wrapperStyling = {
    ...baseWrapperStyling,
    border: `1px solid ${colorScheme}`,
  }

  const disclaimerText =
    websiteTitles && websiteTitles.length > 0 ? (
      <>
        <span style={{ color: colorScheme }}>
          This page is added in {websiteTitles.length} website
          {websiteTitles.length > 1 ? 's: ' : ': '}
        </span>
        {websiteTitles.map((title, index) => (
          <span
            key={index}
            style={{
              color: 'var(--card-fg-color)',
              fontWeight: 'bold',
            }}
          >
            {cleanDomain(title)}
            {index < websiteTitles.length - 1 ? ', ' : '.'}
          </span>
        ))}
      </>
    ) : (
      <span
        style={{
          color: colorScheme,
        }}
      >
        This page is not assigned to any website.
      </span>
    )

  return (
    <Disclaimer stylingProps={loading ? baseWrapperStyling : wrapperStyling}>
      <p
        style={{ fontSize: '0.8rem', color: colorScheme, textAlign: 'center' }}
      >
        {loading ? '' : disclaimerText}
      </p>
    </Disclaimer>
  )
}

export default PageDisclaimer
