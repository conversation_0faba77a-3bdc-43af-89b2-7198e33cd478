import { HelpCircleIcon } from '@sanity/icons'
import { Box, Flex, Text, TextInput, Tooltip } from '@sanity/ui'
import type { SchemaType } from 'sanity'

type TooltipStringInputProps = {
  value: string
  onChange: (value: string) => void
  schemaType: SchemaType
}

/** * A custom input component for Sanity Studio that displays a text input field
 * with a tooltip containing information about available custom parameters.
 * It is used for entering string values with specific formatting requirements.
 */
export default function TooltipStringInput(props: TooltipStringInputProps) {
  const { value, onChange, schemaType } = props
  return (
    <Flex direction='column' gap={2}>
      <Flex align='center' gap={1}>
        <Text size={1} weight='semibold'>
          {schemaType?.title}
        </Text>

        <Tooltip
          method='click'
          content={
            <Box padding={2}>
              <Text size={1}>
                All available custom params for the Tontinator:
                <br />
                <code>country</code>
                <br />
                <code>sex</code>
                <br />
                <code>invStrat (BOL, BTC, Bank dep. are default)</code>
                <br />
                <code>currAge</code>
                <br />
                Example usage: <code>country=USA&invStrat=BOL&currAge=30</code>
              </Text>
            </Box>
          }
          placement='top'
        >
          <Box>
            <HelpCircleIcon style={{ cursor: 'help' }} />
          </Box>
        </Tooltip>
      </Flex>
      <Text size={1}>{schemaType?.description}</Text>
      <TextInput
        value={value}
        onChange={(e) => onChange(e.currentTarget.value)}
      />
    </Flex>
  )
}
