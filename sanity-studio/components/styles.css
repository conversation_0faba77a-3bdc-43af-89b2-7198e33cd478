:root {
  --cerulean-blue: #2975bf;
  --light-cerulean-blue: #7ebaf7;
  --warn-orange: #e29404;
  --warn-light-orange: #e29404b0;
  --error-red: #e53e3e;
  --jade-green: #22b573;
}

.custom-button {
  background-color: transparent !important;
  box-shadow: none !important;
}

.custom-button span {
  color: #bbc0ca;
}

.custom-button :hover {
  background-color: #35383d !important;
}

.custom-button_active {
  background-color: #22344d !important;
}

.custom-button_active span {
  color: #acccfd;
}

.CodeMirror .cm-spell-error {
  background-color: transparent !important;
  text-decoration: wavy underline #ff0000;
}

/* Tontine <PERSON> in the nav bar */
.company-logo {
  position: absolute;
  scale: 1.5;
}

.rate-return,
.color-picker,
.george-yield {
  span {
    background: transparent;
    color: inherit;
  }
}

.link-render {
  .link-render-icon {
    svg {
      padding-left: 8px !important;
      font-size: 20px !important;
      margin-right: 0px !important;
    }
  }
  &.external {
    border-bottom: 1px dashed var(--cerulean-blue);
    svg {
      color: var(--cerulean-blue) !important;
    }
  }
  &.page {
    border-bottom: 1px dashed var(--warn-orange);
    svg {
      color: var(--warn-orange) !important;
    }
    div {
      gap: 0 !important;
    }
  }
  &.section {
    border-bottom: 1px dashed var(--jade-green);
    svg {
      color: var(--jade-green) !important;
    }
  }
  span {
    border: none !important;
  }
}
