import { PinFilledIcon } from '@sanity/icons'
import { Card } from '@sanity/ui'
import type { SchemaType } from 'sanity'
import { Preview, useFormValue } from 'sanity'
import { IntentLink } from 'sanity/router'

import { useSanityFetch } from '../sanity-hooks/useSanityFetch'
import type { Doc } from '../types/customTypes'

type Pin = {
  id: string
  title: string
  subtitle: string
  walkthroughGridImage: {
    asset: {
      id: string
      url: string
    }
  }
  isDraft: boolean
  desktopPlacement?: {
    column: number
    row: number
  }
  mobilePlacement?: {
    column: number
    row: number
  }
}

const ReferencingPinsInput = () => {
  const document = useFormValue([]) as Doc
  const { data: pins } = useSanityFetch<Array<Pin>>(
    `*[_type == "walkthroughPin" && references($gridId)]{
      "id": _id, 
      title, 
      subtitle, 
      walkthroughGridImage, 
      "isDraft": _id in path("drafts.**"), 
      "desktopPlacement": placement.desktop, 
      "mobilePlacement": placement.mobile
    }`,
    { gridId: document._id }
  )

  return (
    <>
      {pins?.length ? (
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, 2fr)',
            flexWrap: 'wrap',
            gap: '.5rem',
          }}
        >
          {pins?.map((pin) => (
            <Card style={{ width: 'auto' }} key={pin?.id} tone='neutral'>
              <IntentLink
                style={{
                  textDecoration: 'none',
                  color: pin.isDraft
                    ? 'var(--warn-orange)'
                    : 'var(--light-cerulean-blue)',
                }}
                intent='edit'
                params={{ id: pin?.id }}
              >
                <Preview
                  value={{
                    title: pin?.title,
                    subtitle: (
                      <>
                        <span
                          style={{
                            color: pin?.isDraft
                              ? 'var(--warn-light-orange)'
                              : 'var(--cerulean-blue)',
                          }}
                        >
                          {pin?.isDraft ? 'Draft ' : 'Published '}
                        </span>
                        - {pin?.subtitle}
                      </>
                    ),
                    icon: PinFilledIcon,
                  }}
                  layout='default'
                  schemaType={'walkthroughPin' as unknown as SchemaType}
                >
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 0,
                      color: 'gray',
                      fontSize: '0.75rem',
                    }}
                  >
                    {pin?.desktopPlacement && (
                      <p style={{ padding: 0, margin: 0 }}>
                        {` Desktop: Col ${pin?.desktopPlacement?.column}, Row ${pin?.desktopPlacement?.row}`}
                      </p>
                    )}
                    {pin?.mobilePlacement && (
                      <p style={{ padding: 0, margin: 0 }}>
                        {`Mobile: Col ${pin?.mobilePlacement?.column}, Row ${pin?.mobilePlacement.row}`}
                      </p>
                    )}
                  </div>
                </Preview>
              </IntentLink>
            </Card>
          ))}
        </div>
      ) : (
        <p>Any pins that reference this document will appear here</p>
      )}
    </>
  )
}

export default ReferencingPinsInput
