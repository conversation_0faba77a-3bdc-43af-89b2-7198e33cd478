import { Box, Card, Flex, Radio, Stack, Text } from '@sanity/ui'
import { useState } from 'react'
import { PatchEvent, set } from 'sanity'

import type { Props } from '../types/customTypes'

const IMAGE_MAP = {
  legacy:
    'https://cdn.sanity.io/images/hl9czw39/production/d45212df5ec0c4e9679135746fa484a510660654-1902x560.png',
  simple:
    'https://cdn.sanity.io/images/hl9czw39/production/46259ddc318b69dfa1cafc1ae8c0d1192e27f975-1904x552.png',
  modern:
    'https://cdn.sanity.io/images/hl9czw39/production/985058bdf8378b375e0c1f369edc1dd8512501e2-1904x561.png',
}

export function HeroVariantRadio(props: Props) {
  const [hoveredOption, setHoveredOption] = useState<
    keyof typeof IMAGE_MAP | null
  >(null)
  const { value, schemaType, onChange } = props

  const imageSource = hoveredOption
    ? IMAGE_MAP[hoveredOption]
    : // biome-ignore lint/nursery/noNestedTernary: <TODO: Fix>
      value
      ? IMAGE_MAP[value as unknown as keyof typeof IMAGE_MAP]
      : null

  return (
    <Flex
      align='flex-start'
      style={{
        border: '1px solid #2a2d3f',
        borderRadius: '0.1875rem',
        padding: '1rem',
      }}
      gap={4}
    >
      <Stack space={3}>
        {schemaType?.options?.list?.map((option) => (
          <Box
            key={option?.value}
            onMouseEnter={() =>
              setHoveredOption(option?.value as keyof typeof IMAGE_MAP)
            }
            onMouseLeave={() => setHoveredOption(null)}
          >
            <Flex align='center' gap={2}>
              <Radio
                checked={typeof value === 'string' && value === option.value}
                onChange={() => onChange(PatchEvent.from(set(option.value)))}
              />
              <Text
                size={1}
                style={{ cursor: 'default' }}
                onClick={() => onChange(PatchEvent.from(set(option.value)))}
              >
                {option.title}
              </Text>
            </Flex>
          </Box>
        ))}
      </Stack>

      {imageSource && (
        <Card padding={0} shadow={2} radius={2}>
          <img
            src={imageSource}
            alt={`${hoveredOption} preview`}
            style={{
              maxWidth: '300px',
              height: 'auto',
              borderRadius: '5px',
              display: 'block',
            }}
          />
        </Card>
      )}
    </Flex>
  )
}
