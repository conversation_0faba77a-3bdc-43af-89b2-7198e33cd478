import type { IconSymbol } from '@sanity/icons'
import { Icon } from '@sanity/icons'
import { Box, Text } from '@sanity/ui'

import { SANITY_CONSTANTS } from '../assets/constants'

const wrapperStyling = {
  display: 'flex',
  gap: '.5rem',
  alignItems: 'center',
  position: 'relative',
  padding: '.75rem',
  borderRadius: '5px',
  boxShadow: '0px 0px 5px rgba(0, 0, 0, 0.199)',
} as const

/** Renders a disclaimer text inside a box container. Accepts a level (info, warning, error, success).
 */
export function Disclaimer({
  children,
  disclaimerText,
  level = 'info',
  stylingProps,
}: {
  children?: React.ReactNode
  disclaimerText?: string
  level?: 'info' | 'warning' | 'error' | 'success'
  stylingProps?: React.CSSProperties
}) {
  const color = SANITY_CONSTANTS.COLOR_CODES[level]

  const wrapperStyle = {
    color,

    ...wrapperStyling,
    border: `1px solid ${color}`,
  }

  const symbol =
    level === 'success'
      ? 'checkmark-circle'
      : (`${level}-outline` as IconSymbol)

  return (
    <Box className={'disclaimer-field'} style={stylingProps ?? wrapperStyle}>
      {!children ? (
        <>
          <Icon symbol={symbol} width={'2rem'} height={'2rem'} />

          <Text style={{ margin: 0, color: 'inherit', fontSize: '0.8rem' }}>
            {disclaimerText}
          </Text>
        </>
      ) : (
        children
      )}
    </Box>
  )
}

/** Usage
 * 
 * 
 * If you need values from the parent

function PinDisclaimer() {
  const document = useFormValue([]) as Doc

  const pinType = document?.pinType
  const disclaimerText =
    pinTypes?.find((e) => e?.value === pinType)?.disclaimer ?? 'empty'

  return pinType ? Disclaimer({ disclaimerText }) : Fragment
}

{
  type: 'string',
  name: 'disclaimer',
  readOnly: true,
  components: {
    input: PinDisclaimer,
  },
},

-- Or --

{
  type: 'string',
  name: 'disclaimer',
  readOnly: true,
  components: {
    input: () =>
      Disclaimer({
        disclaimerText:
          'You can only add this pin to singular Desktop and Mobile walkthrough grids, but neither is required.',
      }),
  },
},
 */
