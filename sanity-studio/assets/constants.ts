const supportedLanguages = [
  { id: 'en', title: 'English', isDefault: true },
  { id: 'es', title: 'Spanish', isDefault: false },
  { id: 'pt', title: 'Portuguese', isDefault: false },
] as const

const SANITY_CONSTANTS = {
  DESKTOP_SIZE: { width: 960, height: 560 },
  MOBILE_SIZE: { width: 320, height: 640 },
  DESKTOP_COLUMNS: 60,
  MOBILE_COLUMNS: 20,
  DESKTOP_ROWS: 35,
  MOBILE_ROWS: 40,
  API_VERSION: '2024-11-11',
  DYNAMIC_METRICS: {
    RETURNS_USD: ['BOL', 'BTC', 'FII', 'VBI'],
    RETURNS_EUR: ['BOL', 'BTC', 'FII'],
  },
  SUPPORTED_LANGUAGES: supportedLanguages,
  BASE_LANGUAGE:
    supportedLanguages.find((l) => l.isDefault) ?? supportedLanguages[0],
  COLOR_CODES: {
    warning: 'var(--warn-orange)',
    error: 'var(--error-red)',
    info: 'var(--cerulean-blue)',
    success: 'var(--jade-green)',
  },
} as const

export { SANITY_CONSTANTS }
