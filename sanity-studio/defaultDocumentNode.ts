import type { DefaultDocumentNodeResolver } from 'sanity/structure'

import { iframeBuilder } from './schemas/util-functions/utilFunctions'

// We use the array below to limit the the appearance of previewing button to only pages
const documentsWithPreviewing = [
  'page',
  'newsPost',
  'blogPost',
  'researchPost',
  'videoPost',
]

/**
 * This enables the iFrame plugin in Sanity Studio. It gives us the Tontine website preview on an iframe
 */
export const defaultDocumentNode: DefaultDocumentNodeResolver = (
  S,
  { schemaType }
) => {
  if (documentsWithPreviewing.includes(schemaType)) {
    return S.document().views([S.view.form(), iframeBuilder(S, 'Preview')])
  }
  return S.document().views([S.view.form()])
}
