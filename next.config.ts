import bundleAnalyzer from '@next/bundle-analyzer'
import { withSentryConfig } from '@sentry/nextjs'
import type { NextConfig } from 'next'

import { tontineRedirects } from './tontine/redirects.config.ts'

// Environment variables validation
const sentryEnabled = <PERSON><PERSON>an(process.env.NEXT_PUBLIC_SENTRY_ENABLED)
const sentryDebug = Boolean(process.env.NEXT_PUBLIC_SENTRY_DEBUG)
const isDevContext =
  process.env.NEXT_PUBLIC_ENVIRONMENT_TYPE === 'development' ||
  !process.env.NEXT_PUBLIC_ENVIRONMENT_TYPE

// Initialize bundle analyzer with type safety
const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
})

// Define base Next.js configuration with TypeScript types
const nextConfig: NextConfig = {
  eslint: {
    dirs: ['.'],
    ignoreDuringBuilds: true,
  },
  async redirects() {
    return tontineRedirects
  },
  poweredByHeader: false,
  trailingSlash: true,
  reactStrictMode: true,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.sanity.io',
        port: '',
      },
    ],
    formats: ['image/avif', 'image/webp'],
    deviceSizes: [480, 768, 810, 1024, 1200, 1680],
    imageSizes: [16, 32, 48, 64, 96, 128],
  },
  experimental: {
    turbo: {
      moduleIdStrategy: isDevContext ? 'named' : 'deterministic',
    },
    optimizePackageImports: [
      'tailwind-merge',
      'clsx',
      '@tailwindcss/postcss',
      'axios',
      'axios-retry',
      '@sanity/icons',
      '@sanity/asset-utils',
      '@sentry/nextjs',
      '@mux/mux-video-react',
      'embla-carousel-autoplay',
      'embla-carousel-react',
      'next-share',
      'semantic-ui-react',
    ],
  },
}

const sentryWebpackPluginOptions = {
  // For all available options, see:
  // https://github.com/getsentry/sentry-webpack-plugin#options
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Organization name and project name for Sentry
  org: 'tontinetrust',
  project: 'javascript-nextjs',

  // Auth token for Sentry. If not included it disables source map upload and speeds up build time.
  authToken: process.env.SENTRY_AUTH_TOKEN,

  // Silent mode to reduce build log spam related to source map uploading.
  silent: !sentryDebug,

  // Enable additional debug information related to source map operations if `sentryDebug` is true.
  debug: sentryDebug,

  // Enable uploading a larger set of source maps for more detailed stack traces. This increases build time.
  widenClientFileUpload: sentryEnabled,

  sourcemaps: {
    // Disable source map generation if not needed. Enable during development or if `sentryEnabled` is true.
    disable: isDevContext ? false : !sentryEnabled,

    // Delete JS source maps after uploading them to Sentry. If in development context, don't delete.

    deleteSourcemapsAfterUpload: !isDevContext,
  },

  reactComponentAnnotation: {
    enabled: true,
  },

  // This overrides the debug mode from the initializers so disable if you need to use debug mode
  disableLogger: !sentryDebug,

  // Disable Sentry telemetry.
  telemetry: false,

  // Optional: Route error reporting through Next.js Middleware to avoid being blocked by ad blockers.
  // Currently disabled as it increases invocations.
  // tunnelRoute: '/monitoring',
}

const bundleAnalyzerConfig = withBundleAnalyzer(nextConfig)

const sentryConfig = withSentryConfig(
  bundleAnalyzerConfig,
  sentryWebpackPluginOptions
)

// biome-ignore lint/style/noDefaultExport: <>
export default sentryEnabled ? sentryConfig : nextConfig
