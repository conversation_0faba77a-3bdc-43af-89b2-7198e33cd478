// This file configures the initialization of Sentry on the client.
// The config you add here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import { init } from '@sentry/nextjs'

import { SECURITY_CONSTANTS } from './data-resource/security-constants'
import {
  buildContext,
  sentryDSN,
  sentryDebug,
  sentryEnabled,
  sentrySampleRate,
} from './serverless/keys'

if (sentryEnabled) {
  init({
    environment: buildContext,
    dsn: sentryDSN,
    denyUrls: [...SECURITY_CONSTANTS.IGNORED_EXCEPTION_URLS],

    // Adjust this value in production, or use tracesSampler for greater control
    tracesSampleRate: sentrySampleRate,
    debug: sentryDebug,
  })
}
