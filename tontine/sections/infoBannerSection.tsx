'use client'

import { useEffect } from 'react'

import { BoxContainer } from '../components/common/BoxContainer'
import { GenericButton } from '../components/common/GenericButton'
import { HideContent } from '../components/common/HideContent'
import { NextLink } from '../components/common/NextLink'
import { SanityImage } from '../components/common/SanityImage'
import { LocalizedContentParser } from '../components/typography/LocalizedContentParser'
import { WrappedText } from '../components/typography/WrappedText'
import { CONSTANTS } from '../data-resource/constants'
import { cn, generateSlug } from '../helper-functions/UtilFunctions'
import { useHideOnClick } from '../hooks/useHideOnClick'
import { STYLE } from '../styles/style'
import type { InfoBannerSectionType } from '../types/sections/info-section.types'
import type { SharedSectionDataType } from '../types/sections/section.types'

type Timer = ReturnType<typeof setTimeout>

/**
 * Implementation on info banner section
 * - `infoBannerText` Text in the info banner section.
 * - `infoBannerCloseIcon` Icon that represents the close button.
 * - `infoBannerSubMenuItem`  value which is used as id in this section, which can then be referenced by a sub menu item.
 */
export function InfoBannerSection({
  sectionData,
}: {
  sectionData: InfoBannerSectionType & SharedSectionDataType
}) {
  const [isVisible, toggleHidden] = useHideOnClick(true)

  // Unmount the info banner section after 30 seconds timeout
  useEffect(() => {
    let timer: Timer
    if (isVisible) {
      timer = setTimeout(() => {
        toggleHidden()
      }, CONSTANTS.INFO_BANNER_TIMEOUT)
    }
    return () => clearTimeout(timer)
  }, [isVisible, toggleHidden])

  if (!isVisible) {
    return <></>
  }

  const slug = generateSlug({
    ...sectionData,
    pageSlug: sectionData.infoBannerPageSlug,
  })

  return (
    <HideContent>
      <BoxContainer
        className={cn(
          'fadeInElement fixed bottom-0 left-0 flex h-14 w-full flex-row items-center justify-center gap-4 border-grey-350 border-t-1 bg-background-100 font-semibold text-[12px] text-brand opacity-0 lg:text-base',
          STYLE.Z_INDEX.INFO_BANNER
        )}
        id='info-banner-id'
      >
        <BoxContainer className='flex w-[80%] text-center lg:w-auto'>
          {sectionData?.title && slug ? (
            <NextLink
              className='w-full'
              href={slug}
              objectId={`${sectionData.sectionSlug?.current}_info_banner_section`}
            >
              <LocalizedContentParser>
                {sectionData.title}
              </LocalizedContentParser>
            </NextLink>
          ) : (
            <WrappedText>
              <LocalizedContentParser>
                {sectionData.title}
              </LocalizedContentParser>
            </WrappedText>
          )}
        </BoxContainer>

        {sectionData?.icon && (
          <GenericButton
            onClick={toggleHidden}
            className='w-fit p-0'
            objectId='info_banner_close'
          >
            <SanityImage
              skeletonProps={{
                className: 'p-1',
              }}
              height={24}
              width={24}
              {...sectionData?.icon}
            />
          </GenericButton>
        )}
      </BoxContainer>
    </HideContent>
  )
}
