import { BoxContainer } from '../../components/common/BoxContainer'
import { NextLink } from '../../components/common/NextLink'
import { SanityImage } from '../../components/common/SanityImage'
import { WrappedText } from '../../components/typography/WrappedText'
import { cn } from '../../helper-functions/UtilFunctions'

type ContactInfoProps = {
  address?: string
  addressIconAlt?: string
  addressIconSrc?: string
  email?: string
  emailIconAlt?: string
  emailIconSrc?: string
  phoneNumber?: string
  phoneIconAlt?: string
  phoneIconSrc?: string
}

const iconHeight = {
  width: 25,
  height: 25,
}

const fieldBoxStyling =
  'flex items-center justify-start p-4 shadow-sm rounded-xl bg-background-100'

const iconStyling =
  'items-center mr-2 flex justify-center p-4 text-center rounded-full w-auto h-[52px] min-w-[52px] min-h-[52px] max-w-[52px] bg-brand-100'

const fieldBoxHover =
  'duration-100 ease-in hover:ring hover:ring-brand-200 hover:shadow-md hover:text-brand'

/** ContactInfo component displays the contact information of the company */
export function ContactInfo({
  address,
  addressIconAlt,
  addressIconSrc,
  email,
  emailIconAlt,
  emailIconSrc,
  phoneIconAlt,
  phoneIconSrc,
  phoneNumber,
}: ContactInfoProps) {
  const numberOfElements = [address, email, phoneNumber].filter(
    (elements) => elements !== undefined
  ).length

  return (
    <BoxContainer
      className={`grid w-full grow grid-cols-1 gap-4 xl:w-unset xl:grid-cols-${numberOfElements}`}
    >
      {address && (
        <BoxContainer className={fieldBoxStyling}>
          <SanityImage
            className='h-6.5 w-6.5'
            skeletonProps={{ className: iconStyling }}
            alt={addressIconAlt}
            src={addressIconSrc}
            {...iconHeight}
          />
          <WrappedText>{address}</WrappedText>
        </BoxContainer>
      )}
      {email && (
        <NextLink
          className={cn(fieldBoxStyling, fieldBoxHover)}
          href={`mailto:${email}`}
        >
          <SanityImage
            {...iconHeight}
            skeletonProps={{ className: iconStyling }}
            alt={emailIconAlt}
            src={emailIconSrc}
          />
          <WrappedText>{email}</WrappedText>
        </NextLink>
      )}
      {phoneNumber && (
        <NextLink
          className={cn(fieldBoxStyling, fieldBoxHover)}
          href={`tel:${phoneNumber}`}
        >
          {phoneIconSrc && (
            <SanityImage
              {...iconHeight}
              skeletonProps={{ className: iconStyling }}
              alt={phoneIconAlt}
              src={phoneIconSrc}
            />
          )}
          <WrappedText>{phoneNumber}</WrappedText>
        </NextLink>
      )}
    </BoxContainer>
  )
}
