import { BoxContainer } from '../../components/common/BoxContainer'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { Title } from '../../components/typography/Title'
import type {
  AboutUsSectionType,
  SharedSectionDataType,
} from '../../types/sections/section.types'
import { ContactInfo } from './ContactInfo'

type AboutUsSectionProps = {
  sectionData: AboutUsSectionType & SharedSectionDataType
}

/** `AboutUsSection` renders an About Us section with data from Sanity CMS */
export function AboutUsSection({ sectionData }: AboutUsSectionProps) {
  const { title, subtitle } = sectionData
  return (
    <BoxContainer
      as={'section'}
      aria-label={sectionData?.slug?.current || 'About tontine trust'}
      id={sectionData?.slug?.current}
      className='w-full max-w-[125rem] scroll-mt-(--nav-height) items-center justify-center bg-brand-100 py-20'
    >
      <BoxContainer className='mx-auto flex flex-col items-center justify-center gap-4 px-8 lg:w-[70%]'>
        <BoxContainer className='rounded-lg bg-background-100 px-10 py-8 shadow-sm lg:mx-0'>
          {title && (
            <Title
              as={'h3'}
              className='mb-5 font-semibold text-[1.8rem] text-brand'
            >
              {title}
            </Title>
          )}
          <LocalizedContentParser
            renderWrapper
            className='text-lg'
            parentSectionId={`${sectionData?.slug?.current}_about_us_section`}
          >
            {subtitle}
          </LocalizedContentParser>
        </BoxContainer>
        <ContactInfo
          address={sectionData?.aboutUsSectionAddress}
          addressIconSrc={sectionData?.aboutUsSectionAddressIcon?.src}
          addressIconAlt={sectionData?.aboutUsSectionAddressIcon?.alt}
          email={sectionData?.aboutUsSectionEmail}
          emailIconSrc={sectionData?.aboutUsSectionEmailIcon?.src}
          emailIconAlt={sectionData?.aboutUsSectionEmailIcon?.alt}
          phoneNumber={sectionData?.aboutUsSectionPhoneNumber}
          phoneIconAlt={sectionData?.aboutUsSectionPhoneIcon?.alt}
          phoneIconSrc={sectionData?.aboutUsSectionPhoneIcon?.src}
        />
      </BoxContainer>
    </BoxContainer>
  )
}
