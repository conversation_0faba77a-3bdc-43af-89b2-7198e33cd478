'use client'

import { useEffect, useRef } from 'react'

import type { IframeProps } from '../../components/common/Iframe'
import { Iframe } from '../../components/common/Iframe'
import { cn } from '../../helper-functions/UtilFunctions'
import { useAuth } from '../../providers/AuthContext'
import { isCypress } from '../../serverless/keys'

/** MyTontine iframeWrapper with an webapp post event listener utilizing useAuth()
 *
 * @note Should not be wrapped in skeleton loading,
 * because messes with the iframe's state
 */
export function MyTontineIframe({ src, className, id, ...rest }: IframeProps) {
  const iframeRef = useRef(null)
  const { listenForWebappEvent } = useAuth()

  useEffect(() => {
    if (!isCypress) {
      listenForWebappEvent()
    }
  }, [listenForWebappEvent])

  return (
    <>
      {!isCypress && (
        <Iframe
          ref={iframeRef}
          src={isCypress ? '' : src}
          // Allows clipboard API to be used in an iframe
          allow='clipboard-read; clipboard-write'
          {...rest}
          className={cn('border-0', className)}
          id={`iframe-${id}`}
        />
      )}
    </>
  )
}
