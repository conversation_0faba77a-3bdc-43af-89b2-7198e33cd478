'use client'

import dynamic from 'next/dynamic'

import { LoadingSpinner } from '../../components/ui/LoadingSpinner'
import { Skeleton } from '../../components/ui/skeleton/Skeleton'

export const TontinatorHandlerLazy = dynamic(
  () => import('./UrlTokenHandler').then((mod) => mod.TontinatorHandler),
  {
    ssr: false,
    loading: () => (
      <Skeleton className='flex min-h-[1520px] scroll-mt-[80px] items-center justify-center lg:min-h-[1240px]'>
        <LoadingSpinner className='bg-transparent' />
      </Skeleton>
    ),
  }
)

export const ReferralHandlerLazy = dynamic(
  () => import('./UrlTokenHandler').then((mod) => mod.ReferralHandler),
  {
    ssr: true,
    loading: () => (
      <Skeleton className='min-h-[750px] scroll-mt-[200px] lg:min-h-[650px]'>
        <LoadingSpinner className='bg-transparent' />
      </Skeleton>
    ),
  }
)
