import { BoxContainer } from '../../components/common/BoxContainer'
import { HeaderInfo } from '../../components/layouts/HeaderInfo'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import type {
  LocalizedContentType,
  SharedSectionDataType,
} from '../../types/sections/section.types'
import { ReferralHandlerLazy } from './MyTontineImportHandler'

type ReferralSectionProps = {
  sectionData: {
    localeDisclaimer: LocalizedContentType
  } & SharedSectionDataType
}

/**
 * Referral section that renders an iframe from https://mytontinelite.netlify.app/rewards/
 *
 * Users verify their email by clicking a link on the email they receive when
 * they join the rewards club. The webapp takes in the following URL scheme /rewards/:verify_token
 */
export function ReferralSection({ sectionData }: ReferralSectionProps) {
  return (
    <BoxContainer
      as={'section'}
      id={sectionData?.slug?.current}
      className='mt-4 px-8'
    >
      <HeaderInfo title={sectionData.title} subtitle={sectionData.subtitle} />

      <ReferralHandlerLazy sectionId={sectionData?.slug?.current} />

      <LocalizedContentParser
        renderWrapper
        className='mx-auto mb-4 flex w-[90%] flex-col items-center justify-center md:w-[70%]'
        overrideElements={{
          p: {
            className: '!leading-5 text-center text-fg/50 ',
          },
        }}
        parentSectionId={`${sectionData?.slug?.current}_tontinator_section`}
      >
        {sectionData?.localeDisclaimer}
      </LocalizedContentParser>
    </BoxContainer>
  )
}
