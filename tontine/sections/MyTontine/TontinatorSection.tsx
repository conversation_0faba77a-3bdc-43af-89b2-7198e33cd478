import { BoxContainer } from '../../components/common/BoxContainer'
import { HeaderInfo } from '../../components/layouts/HeaderInfo'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import type {
  LocalizedContentType,
  SharedSectionDataType,
} from '../../types/sections/section.types'
import { TontinatorHandlerLazy } from './MyTontineImportHandler'

type TontinatorSectionProps = {
  sectionData: {
    localeDisclaimer: LocalizedContentType
  } & SharedSectionDataType
}

/**
 * Tontinator section that renders an iframe from https://mytontinelite.netlify.app/
 *
 * This is where the user redeems a code, the /u/:referral_code url scheme
 * applies the referral code in the sign up form
 */
export function TontinatorSection({ sectionData }: TontinatorSectionProps) {
  return (
    <BoxContainer
      as={'section'}
      id={sectionData?.slug?.current}
      className='my-7 px-2'
    >
      <HeaderInfo title={sectionData?.title} subtitle={sectionData?.subtitle} />

      <TontinatorHandlerLazy sectionId={sectionData?.slug?.current} />

      <LocalizedContentParser
        renderWrapper
        className='mx-auto mb-4 flex w-[90%] flex-col items-center justify-center md:w-[70%]'
        overrideElements={{
          p: {
            className: '!leading-5 text-center text-fg/50 ',
          },
        }}
        parentSectionId={`${sectionData?.slug?.current}_tontinator_section`}
      >
        {sectionData?.localeDisclaimer}
      </LocalizedContentParser>
    </BoxContainer>
  )
}
