'use client'

import type { ReadonlyURLSearchParams } from 'next/navigation'
import { useSearchParams } from 'next/navigation'

import { cn, isLoadedWindow } from '../../helper-functions/UtilFunctions'
import { useAuth } from '../../providers/AuthContext'
import { myTontineLiteOrigin } from '../../providers/MTLOrigins'
import { MyTontineIframe } from './MyTontineIframe'

/**
 * Maps a website domain to a country in order for the tontinator to render
 * country specific UI
 */
const domainToCountry: { [key: string]: string } = {
  'https://ira.tontine.com': 'USA',
}

/**
 * Builds search params string from website for the iframe to be passed in via the
 * src prop for MyTontine Lite.
 * Example of expected return string `&monthlyDep=20&country=USA`
 */
const buildSearchParamsForIframe = (
  searchParams: ReadonlyURLSearchParams | URLSearchParams
) => {
  if (searchParams?.size > 0) {
    let parsedSearchParams = ''

    searchParams.forEach((value, key) => {
      parsedSearchParams += `&${key}=${value}`
    })

    return parsedSearchParams
  }

  return ''
}

/**
 * Referral section that renders an iframe from https://mytontinelite.netlify.app/rewards/
 */
function ReferralHandler({ sectionId }: { sectionId?: string }) {
  const rewardsPageFromWebapp = 'rewards/'
  // URL from the MyTontine Lite webapp

  const rewards = isLoadedWindow()
    ? `${myTontineLiteOrigin}${rewardsPageFromWebapp}?websiteOrigin=${window.origin}`
    : ''

  // DO NOT change the width and height of the iframe
  // To control W and H go to <ReferralSection /> box styling
  return (
    <MyTontineIframe
      title='MyTontine Referral'
      width={'100%'}
      height={'100%'}
      className='h-[750px] scroll-mt-[200px] lg:h-[650px]'
      src={rewards}
      id={sectionId}
    />
  )
}

/**
 * Check `netlify.toml` to control where website urls should be redirected to
 * in order for this component to read the allowed params
 */
function TontinatorHandler({ sectionId }: { sectionId?: string }) {
  const { isAuth } = useAuth()
  const searchParams = useSearchParams()
  const isLoaded = isLoadedWindow()
  const overrideCountry = searchParams.get('country')
  const verifyToken = searchParams.get('ver')
  const mtlVerifyComponent = 'verify/'

  // Handles hash URLs
  // Unlike /tontinator/ paths that routing and useSearchParams catch, hashes are client-side.
  const hash = isLoadedWindow() ? window.location.hash : ''
  let tontinatorParams = ''

  // Use regex to extract query string from hash (e.g., #tontinator/?country=USA)
  const hashQueryMatch = hash.match(/#.*?(\?.*)/)
  if (hashQueryMatch?.[1]) {
    // Extracts the query string, the part after '?'
    const hashQuery = hashQueryMatch[1].substring(1)

    const hashParams = new URLSearchParams(hashQuery)
    tontinatorParams = buildSearchParamsForIframe(hashParams)
  } else {
    // Fallback to regular searchParams if there's no hash-based query params
    tontinatorParams = buildSearchParamsForIframe(searchParams)
  }

  // Constructions for MTL params starts here
  const websiteOriginParam = `?websiteOrigin=${window?.origin}`

  // Default src to home URL
  let src = `${myTontineLiteOrigin}` as string
  if (verifyToken) {
    src = `${src}${mtlVerifyComponent}`
  }
  // Website origin is always included
  src = `${src}${websiteOriginParam}`

  if (isLoaded) {
    const country = domainToCountry[window?.origin]
    // If no override is provided, use the country from the domain
    if (domainToCountry[window?.origin] && !overrideCountry) {
      src = `${src}&country=${country}`
    }
  }

  if (tontinatorParams) {
    src = `${src}${tontinatorParams}`
  }
  return (
    <MyTontineIframe
      title='MyTontine Tontinator'
      width={'100%'}
      height={'100%'}
      className={cn(
        'h-[1520px] scroll-mt-[80px] lg:h-[1240px]',
        isAuth && 'h-[1650px] lg:h-[1350px]'
      )}
      src={src}
      id={sectionId}
    />
  )
}

export { ReferralHandler, TontinatorHandler }
