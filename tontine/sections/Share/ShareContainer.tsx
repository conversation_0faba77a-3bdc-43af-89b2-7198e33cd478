'use client'

import { LinkIcon } from '@sanity/icons'
import {
  FacebookIcon,
  FacebookShareButton,
  LinkedinIcon,
  LinkedinShareButton,
  TwitterIcon,
  TwitterShareButton,
} from 'next-share'
import { usePathname } from 'next/navigation'

import track from '../../app/api/analytics'
import { BoxContainer } from '../../components/common/BoxContainer'
import { GenericButton } from '../../components/common/GenericButton'
import strings from '../../data-resource/strings.json'
import { copyToClipboard } from '../../helper-functions/UtilFunctions'
import { useToastHook } from '../../hooks/useToast'
import type { AnalyticsEvents } from '../../types/Analytics/Analytics.types'
import { SectionEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { ShareIdChunk } from '../../types/Analytics/AnalyticsObjectIds.types'
import type { PageDomainType } from '../../types/common.types'

type ShareButtonProps = {
  url: string
  name: ShareIdChunk
  Component: typeof FacebookShareButton
  Icon: typeof FacebookIcon
}

const buttonStyling =
  'button transparent h-12.5 w-12.5 md:h-14 md:w-14 rounded-full p-1 text-xl text-background-100 outline outline-background-100/50 hover:text-brand hover:outline-transparent'

/** Renders a container for sharing a URL on various social media platforms. */
export function ShareContainer({
  pageDomain,
  shareEvent,
}: PageDomainType & { shareEvent?: AnalyticsEvents }) {
  const { showToast } = useToastHook()
  // Used to get pathname for share links
  const pathname = usePathname()
  const url = `${pageDomain}${pathname}`

  const handleSuccessful = () => {
    showToast({
      title: strings.SUCCESSFUL_LINK_COPY,
      type: 'success',
    })
  }

  const handleError = () => {
    showToast({
      title: strings.FAILED_LINK_COPY,
      type: 'error',
    })
  }

  const triggerEvent = (name: ShareIdChunk) => {
    if (name === 'clipboard') {
      copyToClipboard(url, handleSuccessful, handleError)
    }
    track({
      event: shareEvent ?? SectionEvent.link_clicked,
      properties: {
        object_value: pathname,
        object_id: name,
      },
    })
  }

  const platforms: Array<ShareButtonProps> = [
    {
      url,
      name: 'facebook',
      Component: FacebookShareButton,
      Icon: FacebookIcon,
    },
    {
      url,
      name: 'x',
      Component: TwitterShareButton,
      Icon: TwitterIcon,
    },
    {
      url,
      name: 'linkedin',
      Component: LinkedinShareButton,
      Icon: LinkedinIcon,
    },
  ]

  return (
    <BoxContainer className='flex gap-4'>
      {platforms.map((platform, index) => (
        <platform.Component
          key={`modal-share-links-${platform.url}-${index}`}
          url={platform.url}
        >
          <BoxContainer
            onClick={() => triggerEvent(platform.name)}
            className={buttonStyling}
          >
            <platform.Icon
              iconFillColor={'currentColor'}
              bgStyle={{ fill: 'transparent' }}
            />
          </BoxContainer>
        </platform.Component>
      ))}
      <GenericButton
        onClick={() => triggerEvent('clipboard')}
        className={buttonStyling}
      >
        <LinkIcon height={'100%'} width={'100%'} />
      </GenericButton>
    </BoxContainer>
  )
}
