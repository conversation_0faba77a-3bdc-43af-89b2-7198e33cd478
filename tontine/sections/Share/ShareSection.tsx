import { BoxContainer } from '../../components/common/BoxContainer'
import { HideContent } from '../../components/common/HideContent'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { Title } from '../../components/typography/Title'
import type { AnalyticsEvents } from '../../types/Analytics/Analytics.types'
import type { PageDomainType } from '../../types/common.types'
import type { SharedSectionDataType } from '../../types/sections/section.types'
import { ShareContainer } from './ShareContainer'

/**
 * Renders a Share section, including a title, subtitle and social media icons with share functionality.
 */
export function ShareSection({
  title,
  subtitle,
  slug,
  pageDomain,
  shareEvent,
}: { shareEvent?: AnalyticsEvents } & SharedSectionDataType & PageDomainType) {
  return (
    <HideContent>
      <BoxContainer
        aria-label={slug?.current}
        className='section flex flex-col items-center justify-center gap-4 bg-brand py-10 lg:flex-row'
      >
        <BoxContainer className='flex items-end gap-2'>
          <Title className='font-bold text-4xl text-background-100 md:text-6xl'>
            {title}
          </Title>

          <LocalizedContentParser
            renderWrapper
            as='div'
            className='max-w-35 text-background-100 leading-5 md:text-lg'
            renderDefaultBlock={false}
          >
            {subtitle}
          </LocalizedContentParser>
        </BoxContainer>
        <ShareContainer pageDomain={pageDomain} shareEvent={shareEvent} />
      </BoxContainer>
    </HideContent>
  )
}
