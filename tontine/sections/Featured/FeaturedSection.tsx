import { BoxContainer } from '../../components/common/BoxContainer'
import {
  cn,
  convertToPlainText,
  replaceWhitespace,
} from '../../helper-functions/UtilFunctions'
import type { AnalyticsObjectIds } from '../../types/Analytics/AnalyticsObjectIds.types'
import type { PageDomainType, SanityButton } from '../../types/common.types'
import type {
  SectionMediaType,
  SharedSectionDataType,
} from '../../types/sections/section.types'
import { FeaturedMediaContent } from './FeaturedMediaContent'
import { MarkdownFeaturedSection } from './MarkdownFeaturedSection'

type FeaturedSectionProps = {
  sectionData: {
    isHero?: boolean
    featuredSectionMediaDirectionToggle?: boolean
    featuredSectionElementCollection?: string
    featuredSectionButtons?: Array<SanityButton>
    featuredSectionSubscribeToggle?: boolean
  } & SharedSectionDataType &
    SectionMediaType
} & PageDomainType

/**
 * Represents the properties of a featured section.

 * - `featuredS<PERSON>tionTitle`  The title of the featured section.
 * - `featuredSectionDescription`  The description of the featured section.
 * - `isHero`  Indicates whether the featured section is a hero section.
 * - `featuredSectionMediaDirectionToggle`  Indicates whether to toggle the media direction of the featured section.
 * - `featuredSectionElementCollection`  The element collection of the featured section.
 * - `featuredSectionEllipticalImageToggle` Used for rounding the image corners into an elliptical shape
 * - `featuredSectionImage`  The image of the featured section.
 * - `featuredSectionSubMenuItemReference`  The references to sub-menu items of the featured section.
 * - `featuredSectionButtons` The buttons of the featured section.
 * - `sectionSlug` value that gives this section an id
 * - `featuredSectionSubscribeToggle` toggle that if it's set on true featured section should have subscribe field
 */
export function FeaturedSection({
  sectionData,
  pageDomain,
}: FeaturedSectionProps) {
  const hasMediaContent = sectionData?.sectionImage ?? sectionData.sectionVideo

  const justifyContentValue = hasMediaContent
    ? 'justify-between'
    : 'justify-center'

  const convertedTitle = convertToPlainText({ value: sectionData?.title }) ?? ''

  const slug =
    sectionData?.slug?.current ??
    replaceWhitespace({
      str: convertedTitle,
    }).toLowerCase()
  const headId: string = `head-${slug}`

  const objectId: AnalyticsObjectIds = `${sectionData.slug?.current}_featured_section`

  return (
    <BoxContainer
      aria-label={convertedTitle}
      aria-labelledby={headId}
      as={'section'}
      className={cn(
        'flex w-full items-center bg-gray-200 py-8 xmd:py-10 md:px-[2rem] md:py-15 xl:py-20',
        sectionData?.isHero ? 'bg-brand-100' : 'bg-background',
        justifyContentValue
      )}
      id={slug}
    >
      <BoxContainer
        className={cn(
          'section-width mx-auto flex flex-col-reverse items-center gap-5 xmd:gap-6',
          !sectionData?.isHero && 'justify-between',
          sectionData?.isHero && 'xmd:gap-15',
          hasMediaContent ? 'xlg:flex-row' : 'xlg:flex-col',
          sectionData.featuredSectionMediaDirectionToggle &&
            'xl:flex-row-reverse'
        )}
      >
        <FeaturedMediaContent
          title={sectionData?.title}
          subtitle={sectionData?.subtitle}
          image={sectionData?.sectionImage}
          videoThumbnail={sectionData?.videoThumbnail}
          video={sectionData?.sectionVideo}
          hideControls={sectionData?.hideControls}
          pageDomain={pageDomain}
        />
        <MarkdownFeaturedSection
          sectionSlug={slug}
          title={sectionData?.title}
          subtitle={sectionData?.subtitle}
          buttons={sectionData?.featuredSectionButtons}
          isHero={sectionData?.isHero}
          id={headId}
          parentSectionId={objectId}
        />
      </BoxContainer>
    </BoxContainer>
  )
}
