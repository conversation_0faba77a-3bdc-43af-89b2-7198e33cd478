'use client'

import React from 'react'

import { BoxContainer } from '../../components/common/BoxContainer'
import { NextLink } from '../../components/common/NextLink'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { Title } from '../../components/typography/Title'
import {
  cn,
  convertToPlainText,
  generateSlug,
  getAttribute,
} from '../../helper-functions/UtilFunctions'
import { useLanguage } from '../../providers/LanguageContext'
import { SectionEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { SanityButton } from '../../types/common.types'
import type { AdditionalComponentAttributes } from '../../types/component.types'
import type {
  LocalizedContentHeader,
  ParentSectionID,
} from '../../types/sections/section.types'

type MarkdownType = {
  sectionSlug: string
  buttons?: Array<SanityButton>
  isHero?: boolean
} & ParentSectionID &
  Omit<AdditionalComponentAttributes, 'title'> &
  LocalizedContentHeader

/** Renders a featured section with markdown content. */
export function MarkdownFeaturedSection({
  sectionSlug,
  title,
  subtitle,
  buttons,
  isHero,
  parentSectionId,
  id,
}: MarkdownType) {
  const { language } = useLanguage()
  return (
    <BoxContainer
      {...{
        ...getAttribute(true, 'tabIndex', 0),
      }}
      className='flex w-full flex-col gap-5 lg:w-[50%]'
    >
      {title && (
        <Title
          as={'h2'}
          id={id}
          className={cn(
            'text-left font-bold text-3xl sm:text-4xl smd:text-4xl md:text-4xl lg:text-5xl xl:text-5xl',
            isHero ? 'text-brand' : 'text-fg'
          )}
        >
          {title}
        </Title>
      )}

      {subtitle && (
        <LocalizedContentParser
          renderWrapper
          className={cn(
            'w-fit text-lg leading-7.5 xl:max-w-[90%] xl:text-xl',
            isHero ? 'text-brand' : 'text-fg'
          )}
          parentSectionId={parentSectionId}
        >
          {subtitle}
        </LocalizedContentParser>
      )}
      {buttons && buttons?.length > 0 && (
        <BoxContainer className='mt-3 mb-3 flex w-full flex-wrap items-center justify-center md:mt-0 lg:justify-start'>
          {buttons?.length > 0 &&
            buttons?.map((button, index) => {
              const slug = generateSlug(button)

              if (button?.buttonLabel && slug) {
                return (
                  <NextLink
                    key={`button-${index}-${slug}-${button?.buttonLabel}`}
                    objectId={`${sectionSlug}_featured_section`}
                    linkLabel={convertToPlainText({
                      value: button?.buttonLabel,
                      language,
                    })}
                    customEvent={SectionEvent.btn_clicked}
                    className={cn(
                      'button px-10 font-medium hover:scale-101',
                      isHero ? 'solid' : 'transparent'
                    )}
                    href={slug}
                  >
                    <LocalizedContentParser>
                      {button?.buttonLabel}
                    </LocalizedContentParser>
                  </NextLink>
                )
              }
              return <React.Fragment key={`emptyMarkDownButton-${index}`} />
            })}
        </BoxContainer>
      )}
    </BoxContainer>
  )
}
