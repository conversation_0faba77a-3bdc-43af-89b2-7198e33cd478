import { BoxContainer } from '../../components/common/BoxContainer'
import { SanityImage } from '../../components/common/SanityImage'
import { ReactMuxVideo } from '../../components/mux-video/ReactMuxVideo'
import { VideoSchema } from '../../components/mux-video/VideoSchema'
import { cn } from '../../helper-functions/UtilFunctions'
import type { PageDomainType, SanityImageType } from '../../types/common.types'
import type { LocalizedContentHeader } from '../../types/sections/section.types'

type FeaturedSectionMedia = {
  image?: SanityImageType
  hideControls?: boolean
  videoThumbnail?: SanityImageType
  video?: {
    playbackId: string
    duration: number
    quality: string
    updatedAt: string
    createdAt: string
    height: number
    width: number
  }
} & PageDomainType &
  LocalizedContentHeader

/** Renders the media content of a featured section.
 * - `featuredSectionImage`  The image of the featured section.
 */
export function FeaturedMediaContent({
  title,
  subtitle,
  image,
  video,
  videoThumbnail,
  pageDomain,
  hideControls,
}: FeaturedSectionMedia) {
  const ellipticalImage = {
    clipPath: 'none',
  }

  if (image?.src)
    return (
      <SanityImage
        {...image}
        fillProp
        skeletonProps={{
          className: cn(
            'aspect-video h-full w-full xmd:max-w-[500px] xxl:max-w-[600px] sm:max-w-[350px] md:max-w-[450px] xl:max-w-[500px]',
            ellipticalImage
          ),
        }}
      />
    )
  if (video?.playbackId)
    return (
      <BoxContainer className='h-full w-[100%] overflow-hidden rounded-[1rem] shadow-smx sm:w-[400px] md:w-[500px]'>
        <ReactMuxVideo
          hideControls={hideControls}
          playbackId={video?.playbackId}
          videoThumbnail={videoThumbnail?.src || ''}
        >
          <VideoSchema
            title={title}
            subtitle={subtitle}
            video={video}
            videoThumbnail={videoThumbnail}
            pageDomain={pageDomain}
          />
        </ReactMuxVideo>
      </BoxContainer>
    )

  return <></>
}
