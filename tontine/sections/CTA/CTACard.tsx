'use client'

import { useState } from 'react'

import { BoxContainer } from '../../components/common/BoxContainer'
import { GenericButton } from '../../components/common/GenericButton'
import { HideContent } from '../../components/common/HideContent'
import { NextLink } from '../../components/common/NextLink'
import { SanityImage } from '../../components/common/SanityImage'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { Title } from '../../components/typography/Title'
import { WrappedText } from '../../components/typography/WrappedText'
import { CloseButton } from '../../components/ui/CloseButton'
import {
  cn,
  generateSlug,
  getCardPosition,
} from '../../helper-functions/UtilFunctions'
import { useIntersectObserver } from '../../hooks/useIntersectObserver'
import { STYLE } from '../../styles/style'
import type {
  CTACardPosition,
  CTACardSectionType as cardData,
} from '../../types/sections/cta.types'

type CTACardProps = {
  sectionData: cardData
  interSectionObserverId?: string
}
/**
 *CTACard is a fixed card in one of the devices screen corners with a CTA link button that leads you to a certain route
 */
export function CTACard({ sectionData }: CTACardProps) {
  const [openCta, setOpenCta] = useState(false)

  const { targetIntersecting: cardIntersecting } = useIntersectObserver({
    ids: sectionData?.intersectSections,
    root: null,
    rootMargin: '0px',
    threshold: 0.1,
  })

  const { targetIntersecting: infoBannerIsVisible } = useIntersectObserver({
    ids: 'info-banner-id',
    root: null,
    rootMargin: '0px',
    threshold: 0.1,
  })

  const isCTACardFullPosition = (
    position: string
  ): position is CTACardPosition => {
    return ['full-bottom-right', 'full-bottom-left'].includes(position)
  }
  const determinePosition = (
    cardPosition: CTACardPosition,
    infoBannerVisible: boolean
  ): CTACardPosition => {
    if (cardPosition === 'bottom-right' || cardPosition === 'bottom-left') {
      if (!infoBannerVisible) {
        const newPosition = `full-${cardPosition}`
        return isCTACardFullPosition(newPosition) ? newPosition : cardPosition
      }
      return cardPosition
    }
    return cardPosition
  }
  const toggleCTA = () => {
    setOpenCta(!openCta)
  }
  const cardToSlideToBottom = infoBannerIsVisible
    ? 'translateY(0)'
    : 'translateY(3.5rem)'

  const cardPosition = getCardPosition(
    determinePosition(sectionData?.position, infoBannerIsVisible ?? true)
  )

  const slug = sectionData?.ctaCardButton
    ? generateSlug(sectionData?.ctaCardButton)
    : ''
  return (
    <HideContent>
      <BoxContainer
        className={cn(
          'cta-card fixed ml-4 flex h-min select-none items-center justify-center gap-1.5 rounded-full bg-brand px-2 py-2 pl-2 opacity-start shadow-sm hover:shadow-lg sm:ml-0 sm:px-3 sm:py-3',
          cardIntersecting && 'hidden',
          cardPosition,
          cardToSlideToBottom,
          STYLE.Z_INDEX.CTA_CARD
        )}
        onClick={toggleCTA}
      >
        <SanityImage
          fillProp
          skeletonProps={{ className: 'h-12 w-12 rounded-full' }}
          objectFitProp='fill'
          {...sectionData?.icon}
        />
        <NextLink
          className={cn(
            'flex items-center justify-between gap-1.5 overflow-hidden',
            !openCta && 'hidden md:flex'
          )}
          href={slug}
          objectId={`${sectionData?.slug?.current}_cta_card_section`}
          tabIndex={cardIntersecting ? -1 : 0}
        >
          <BoxContainer>
            <Title className='font-semibold text-background-100 leading-4 sm:text-base sm:leading-5'>
              {sectionData?.stringTitle}
            </Title>
            <WrappedText className='text-wrap text-grey-200 text-sm leading-3.5 sm:text-base sm:leading-5'>
              <LocalizedContentParser>
                {sectionData?.stringSubtitle}
              </LocalizedContentParser>
            </WrappedText>
          </BoxContainer>

          <GenericButton
            disabled={cardIntersecting}
            className='button solid inverted ml-1.5 text-nowrap px-3 py-2 font-semibold text-sm shadow-sm sm:ml-3 sm:px-3 sm:py-2.5 sm:text-base'
          >
            <LocalizedContentParser>
              {sectionData?.ctaCardButton?.buttonLabel}
            </LocalizedContentParser>
          </GenericButton>
        </NextLink>
        <CloseButton
          className={cn(
            'button px-1 text-2xl text-background-100 sm:hidden',
            !openCta && 'hidden'
          )}
          onClick={toggleCTA}
        />
      </BoxContainer>
    </HideContent>
  )
}
