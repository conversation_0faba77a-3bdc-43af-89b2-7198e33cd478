import { BoxContainer } from '../../components/common/BoxContainer'
import { NextLink } from '../../components/common/NextLink'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { Title } from '../../components/typography/Title'
import { WrappedText } from '../../components/typography/WrappedText'
import {
  convertToPlainText,
  generateSlug,
} from '../../helper-functions/UtilFunctions'
import type { SanityButton } from '../../types/common.types'
import type { SharedSectionDataType } from '../../types/sections/section.types'

type CtaSectionProps = {
  sectionData: {
    ctaSectionButtons?: Array<SanityButton>
    ctaAdditionalText?: string
  } & SharedSectionDataType
}

/**
 * Generic CTA component that can be used for any CTA and calls the user to action
 */
export function CtaSection({ sectionData }: CtaSectionProps) {
  const slug = sectionData?.slug?.current
  const headId: string = `head-${slug}`
  return (
    <BoxContainer
      as='section'
      className='mx-auto max-w-[125rem] bg-brand-100 py-8 pt-10 text-center'
      id={slug}
      aria-label={convertToPlainText({ value: sectionData?.title })}
      aria-labelledby={headId}
    >
      <BoxContainer className='mx-auto flex w-[100%] flex-col items-center justify-center gap-4 px-10 md:w-[70%] lg:px-0 xl:w-[50%]'>
        <Title className='font-bold text-3xl text-brand'>
          {sectionData?.title}
        </Title>
        <LocalizedContentParser
          overrideElements={{
            p: {
              className: 'text-fg/80 text-lg',
            },
          }}
          parentSectionId={`${sectionData?.slug?.current}_cta_section`}
        >
          {sectionData?.subtitle}
        </LocalizedContentParser>
        <BoxContainer className='flex flex-col justify-center gap-4 lg:flex-row'>
          {sectionData?.ctaSectionButtons?.map((button) => {
            const slug = generateSlug(button)
            return (
              <NextLink
                key={`cta-button-${slug}`}
                className='button solid px-10'
                href={slug}
              >
                <LocalizedContentParser>
                  {button.buttonLabel}
                </LocalizedContentParser>
              </NextLink>
            )
          })}
        </BoxContainer>
        <WrappedText className='text-gray-500 text-sm'>
          {sectionData?.ctaAdditionalText}
        </WrappedText>
      </BoxContainer>
    </BoxContainer>
  )
}
