import { BoxContainer } from '../../components/common/BoxContainer'
import { SanityImage } from '../../components/common/SanityImage'
import { HeaderInfo } from '../../components/layouts/HeaderInfo'
import { ReactMuxVideo } from '../../components/mux-video/ReactMuxVideo'
import { VideoSchema } from '../../components/mux-video/VideoSchema'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import type { PageDomainType } from '../../types/common.types'
import type { InfoBlockSectionType } from '../../types/sections/info-section.types'
import { InfoBlockList } from './InfoBlockList'

/** InfoHubSection is a section that contains group of smaller info blocks components and title. */
export function InfoHubSection({
  sectionData,
  pageDomain,
}: {
  sectionData: InfoBlockSectionType
} & PageDomainType) {
  return (
    <BoxContainer
      as={'section'}
      className='bg-background px-8 py-12 lg:px-0'
      id={sectionData?.slug?.current}
    >
      <HeaderInfo
        className='flex flex-col items-center justify-center gap-2'
        titleProps={{
          className:
            'lg:text-5xl text-3xl sm:text-3xl md:text-4xl font-bold text-fg',
        }}
        subtitleProps={{
          className: 'sm:text-xl font-semibold text-fg',
        }}
        subtitle={sectionData?.subtitle}
        title={sectionData?.title}
      />
      <InfoBlockList
        infoBlockList={sectionData?.infoHubCardList}
        variant={sectionData?.layout}
        parentSectionId={`${sectionData?.slug?.current}_info_hub_section`}
      />
      {sectionData?.infoHubAdditionalInfo && (
        <LocalizedContentParser
          renderWrapper
          className='py-5 text-center font-semibold text-fg text-xl'
        >
          {sectionData?.infoHubAdditionalInfo}
        </LocalizedContentParser>
      )}
      {sectionData?.sectionVideo?.playbackId && (
        <ReactMuxVideo
          hideControls={sectionData?.hideControls}
          className='mx-auto overflow-hidden rounded-xl shadow-mdx md:rounded-3xl lg:w-[60%]'
          playbackId={sectionData?.sectionVideo?.playbackId || ''}
          videoThumbnail={sectionData?.videoThumbnail?.src || ''}
        >
          <VideoSchema
            title={sectionData?.title}
            subtitle={sectionData?.subtitle}
            videoThumbnail={sectionData?.videoThumbnail}
            pageDomain={pageDomain}
            video={sectionData?.sectionVideo}
          />
        </ReactMuxVideo>
      )}
      {sectionData?.sectionImage && (
        <SanityImage
          {...sectionData?.sectionImage}
          skeletonProps={{
            className:
              'rounded-xl shadow-mdx md:rounded-3xl aspect-[16/9] mx-auto lg:max-w-[60%]',
          }}
          fillProp
        />
      )}
    </BoxContainer>
  )
}
