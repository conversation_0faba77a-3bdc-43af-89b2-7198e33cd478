import { BoxContainer } from '../../components/common/BoxContainer'
import { HeaderInfo } from '../../components/layouts/HeaderInfo'
import type { InfoBlock } from '../../types/sections/info-section.types'
import type { SharedSectionDataType } from '../../types/sections/section.types'
import { InfoBlockList } from './InfoBlockList'

type InfoBlockSectionProps = {
  sectionData: {
    infoBlockSectionList?: Array<InfoBlock>
  } & SharedSectionDataType
}

/** InfoBlockSection is a section that contains group of smaller info blocks components and title.
 * - `infoBlockSectionTitle` Title of the section.
 * - `infoBlockSectionList` array of each info block
 * - `sectionSlug` value that gives this section an id
 */
export function InfoBlockSection({ sectionData }: InfoBlockSectionProps) {
  return (
    <BoxContainer
      as={'section'}
      className='section-width pt-10 pb-15'
      id={sectionData?.slug?.current}
    >
      <HeaderInfo className='mb-0' title={sectionData?.title} />

      <InfoBlockList
        infoBlockList={sectionData?.infoBlockSectionList}
        parentSectionId={`${sectionData?.slug?.current}_info_block_section`}
      />
    </BoxContainer>
  )
}
