import type { BoxContainerProps } from '../../components/common/BoxContainer'
import { BoxContainer } from '../../components/common/BoxContainer'
import { cn } from '../../helper-functions/UtilFunctions'
import type {
  InfoBlock,
  InfoVariants,
} from '../../types/sections/info-section.types'
import type { ParentSectionID } from '../../types/sections/section.types'
import { InfoCard } from './InfoCard'

type InfoBlockListProps = {
  infoBlockList?: Array<InfoBlock>
  variant?: InfoVariants
} & ParentSectionID &
  BoxContainerProps

/** `InfoBlockList` renders a list of info blocks by mapping the elements from the passed array to the corresponding info block container.
 */
export function InfoBlockList({
  infoBlockList,
  variant,
  parentSectionId,
  ...rest
}: InfoBlockListProps) {
  const isVertical = variant === 'vertical'
  const isHorizontal = variant === 'horizontal'
  return (
    <BoxContainer
      {...rest}
      className={cn(
        'grid grid-cols-(--single) gap-x-8 gap-y-6 py-6 md:grid-cols-(--single) lg:grid-cols-(--repeat-3)',
        isVertical &&
          'mx-auto grid sm:grid-cols-(--single) lg:w-[60%] lg:grid-cols-(--single)',
        isHorizontal && 'mx-auto gap-x-0 lg:w-[70%]'
      )}
    >
      {infoBlockList &&
        infoBlockList?.length > 0 &&
        infoBlockList.map((infoBlock, index) => (
          <InfoCard
            infoBlock={infoBlock}
            variant={variant}
            key={`infoBlock-${index}`}
            parentSectionId={parentSectionId}
          />
        ))}
    </BoxContainer>
  )
}
