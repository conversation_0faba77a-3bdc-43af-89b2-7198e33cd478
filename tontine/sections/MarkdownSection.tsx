import { BoxContainer } from '../components/common/BoxContainer'
import { LocalizedContentParser } from '../components/typography/LocalizedContentParser'
import { Title } from '../components/typography/Title'
import { cn } from '../helper-functions/UtilFunctions'
import type {
  LocalizedContentType,
  SharedSectionDataType,
} from '../types/sections/section.types'

type MarkdownSectionProps = {
  sectionData: {
    textStyling?: 'normal' | 'disclaimer'
    textPosition?: 'left' | 'center' | 'right'
    markdown?: LocalizedContentType
  } & SharedSectionDataType
}

/**
 * Renders a markdown section with a styling and position options.
 */
export function MarkdownSection({ sectionData }: MarkdownSectionProps) {
  const markdownTextStyling =
    sectionData.textStyling === 'normal'
      ? 'text-dark-article-grey'
      : 'text-dark-article-grey/80 text-sm'

  return (
    <BoxContainer className='section flex flex-col items-center justify-center gap-8 bg-background py-15'>
      <Title className='font-bold text-3xl text-fg sm:text-4xl smd:text-5xl md:text-6xl lg:text-5xl xl:text-5xl'>
        {sectionData?.stringTitle}
      </Title>
      <LocalizedContentParser
        renderWrapper
        className={cn(
          'w-[70%] text-lg',
          markdownTextStyling,
          `text-${sectionData?.textPosition}`
        )}
        parentSectionId={`${sectionData?.slug?.current}_markdown_section`}
      >
        {sectionData?.markdown}
      </LocalizedContentParser>
    </BoxContainer>
  )
}
