'use client'

import { useState } from 'react'
import { BoxContainer } from '../components/common/BoxContainer'
import { LocalizedContentParser } from '../components/typography/LocalizedContentParser'
import { Title } from '../components/typography/Title'
import { Divider } from '../components/ui/Divider'
import { VideoCard } from '../components/ui/VideoCard'
import type { ContentPost } from '../types/sections/content-section.types'
import type { SharedSectionDataType } from '../types/sections/section.types'

type VideoSectionProps = {
  sectionData: {
    videos?: Array<ContentPost>
  } & SharedSectionDataType
}

/**
 * The `VideosSection` component renders a section for displaying multiple videos.
 * It includes a title, subtitle, and a grid of video cards.
 * Each video card displays a video thumbnail, title, and publish date.
 */
export function VideosSection({
  sectionData: { videos, title, subtitle },
}: VideoSectionProps) {
  const [playingIndex, setPlayingIndex] = useState<number | null>(null)

  return (
    <>
      <BoxContainer
        aria-label='Recommended videos.'
        as={'section'}
        className='max-w-[125rem] bg-grey-200 py-10'
      >
        <BoxContainer className='mx-auto flex w-[90%] flex-col gap-6 sm:w-[80%] md:w-[80%] xl:w-[75%]'>
          <BoxContainer className='flex flex-col pt-5'>
            <Title className='font-semibold text-3xl text-fg'>{title}</Title>
            <LocalizedContentParser
              overrideElements={{
                p: { className: 'font-semibold text-fg-400 text-xl' },
              }}
            >
              {subtitle}
            </LocalizedContentParser>
          </BoxContainer>
          <Divider className='mb-3' />
          <BoxContainer className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2 lg:gap-8 xl:grid-cols-3'>
            {videos &&
              videos.length > 0 &&
              videos.map((video, index) => {
                const isPlaying = playingIndex === index
                return (
                  <VideoCard
                    video={video}
                    key={`video-${video.slug.current}-${index}`}
                    index={index}
                    isPlaying={isPlaying}
                    setPlayingIndex={setPlayingIndex}
                  />
                )
              })}
          </BoxContainer>
          <Divider />
        </BoxContainer>
      </BoxContainer>
    </>
  )
}
