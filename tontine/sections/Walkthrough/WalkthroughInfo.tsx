import { BoxContainer } from '../../components/common/BoxContainer'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { Title } from '../../components/typography/Title'
import type { DialogProps } from '../../components/ui/Dialog'
import { Dialog } from '../../components/ui/Dialog'
import type { ParentSectionID } from '../../types/sections/section.types'
import type { WalkthroughPinType } from '../../types/sections/walkthrough-section.types'

export const WalkthroughInfo = ({
  currentPin,
  showBackdrop,
  isDesktop,
  setDialog,
  parentSectionId,
}: {
  currentPin?: WalkthroughPinType
  infoModalOpen?: boolean
  isDesktop?: boolean
  showBackdrop?: boolean
  setDialog: DialogProps['setRef']
  setCurrentPin: (pin: WalkthroughPinType) => void
} & ParentSectionID) => {
  return isDesktop ? (
    <BoxContainer className='top-0 bottom-0 my-[.1rem] w-full xml:w-[calc(100%-90px)] xxl:w-[calc(100%-100px)] max-w-[25rem] grow bg-background p-4 text-left shadow-xs xl:w-[calc(100%-80px)]'>
      <Title as='h4' className='font-bold text-xl'>
        {currentPin?.title}
      </Title>
      <LocalizedContentParser
        className='walkthrough-info-content text-left'
        parentSectionId={parentSectionId}
      >
        {currentPin?.subtitle}
      </LocalizedContentParser>
    </BoxContainer>
  ) : (
    <Dialog
      setRef={setDialog}
      showBackdropExternal={showBackdrop}
      title={currentPin?.title}
      dialogProps={{
        className: 'p-4 rounded-md bg-brand-50 shadow-md',
      }}
      headerProps={{
        className:
          'flex items-center justify-between text-xl text-fg-700 gap-6',
      }}
      closeButtonProps={{
        className: 'button transparent px-2 rounded-sm w-[32px] h-[32px]',
        objectId: 'walkthrough_modal_info_close',
        objectValue: currentPin,
      }}
    >
      <LocalizedContentParser
        renderWrapper
        className='text-left text-lg'
        parentSectionId={parentSectionId}
      >
        {currentPin?.subtitle}
      </LocalizedContentParser>
    </Dialog>
  )
}
