import { BoxContainer } from '../../components/common/BoxContainer'
import { SanityImage } from '../../components/common/SanityImage'
import { EmblaSlide } from '../../components/embla-carousel/EmblaSlide'
import strings from '../../data-resource/strings.json'
import { cn, convertToPlainText } from '../../helper-functions/UtilFunctions'
import { STYLE, getGridStyles } from '../../styles/style'
import type { SanityImageType } from '../../types/common.types'
import type { WalkthroughPinType } from '../../types/sections/walkthrough-section.types'
import { WalkthroughIconButton } from './WalkthroughIcon'

export const WalkthroughGrid = ({
  isDesktop,
  image,
  pinArray,
  currentPin,
  handleInfoPinClick,
}: {
  isDesktop?: boolean
  image?: SanityImageType
  pinArray?: Array<WalkthroughPinType>
  currentPin?: WalkthroughPinType
  handleInfoPinClick: (pin: WalkthroughPinType) => void
}) => {
  const handlePinClick = (pin: WalkthroughPinType) => {
    handleInfoPinClick(pin)
  }
  const gridSizes = isDesktop
    ? STYLE.WALKTHROUGH_GRID_SIZE.DESKTOP
    : STYLE.WALKTHROUGH_GRID_SIZE.MOBILE

  return (
    <EmblaSlide className='flex min-w-[unset] flex-[100%] px-2 py-[.1rem] md:px-3 xl:px-2'>
      <BoxContainer
        className={cn(
          strings.EMBLA_SCALE_CLASS, // Mandatory for scale to work.
          'flex select-none flex-col rounded-xl'
        )}
      >
        <BoxContainer
          className={cn(
            getGridStyles({
              rows: gridSizes.ROWS,
              columns: gridSizes.COLUMNS,
              cellSize: gridSizes.CELL_SIZE,
            }),
            'relative max-w-fit rounded-2xl shadow-xs'
          )}
        >
          <SanityImage
            {...image}
            fillProp
            skeletonProps={{
              className:
                'w-full h-full absolute rounded-[inherit] overflow-hidden',
            }}
          />
          {pinArray?.map((pin, pinIndex) => (
            <WalkthroughIconButton
              key={convertToPlainText({ value: pin?.title })}
              currentPin={currentPin}
              pin={pin}
              index={pinIndex}
              isDesktop={isDesktop}
              handleInfoPinClick={handlePinClick}
            />
          ))}
        </BoxContainer>
      </BoxContainer>
    </EmblaSlide>
  )
}
