'use client'

import { useState } from 'react'

import { BoxContainer } from '../../components/common/BoxContainer'
import { EmblaCarousel } from '../../components/embla-carousel/EmblaCarousel'
import { HeaderInfo } from '../../components/layouts/HeaderInfo'
import {
  cn,
  convertToPlainText,
  getArrayBasedOnLength,
} from '../../helper-functions/UtilFunctions'
import { useBreakpointValue } from '../../hooks/useBreakpointValue'
import type { AnalyticsObjectIds } from '../../types/Analytics/AnalyticsObjectIds.types'
import type {
  ParentSectionID,
  SharedSectionDataType,
} from '../../types/sections/section.types'
import type {
  WalkthroughPinType,
  WalkthroughSectionType,
} from '../../types/sections/walkthrough-section.types'
import { WalkthroughGrid } from './WalkthroughGrid'
import { WalkthroughInfo } from './WalkthroughInfo'

/**
 * WalkthroughContainer component renders a carousel of walkthrough grids
 * and displays walkthrough information based on the selected pin.
 *
 * The component uses the `useDialog` hook to manage the dialog state and
 * `useBreakpointValue` to determine mobile or desktop view. It handles pin
 * clicks to update the current walkthrough pin and open the dialog for mobile view.
 *
 * The carousel displays walkthrough grids with optional grid highlighting
 * and navigation controls, and it renders dots for multiple items.
 */
const WalkthroughContainer = ({
  // TODO: Figure out how to handle this with new dialog
  desktopWalkthroughs,
  mobileWalkthroughs,
  parentSectionId,
}: WalkthroughSectionType & ParentSectionID) => {
  const [dialogRef, setDialogRef] = useState<HTMLDialogElement>()

  // const [showBackdrop, setShowBackdrop] = useState(false)

  const { isBreakpoint: isDesktop } = useBreakpointValue(['xl'])

  // const handleOpen = () => {
  //   dialogRef?.showModal()
  //   setShowBackdrop(true)
  // }

  // const handleClose = () => {
  //   dialogRef?.close()
  //   setShowBackdrop(false)
  // }

  // Uses separate walkthrough arrays based on dimensions
  const infoPinArray = isDesktop ? desktopWalkthroughs : mobileWalkthroughs

  // Check if there is more than one element in the array - used for styli
  const moreThanOneElementInArray = infoPinArray && infoPinArray?.length > 1

  // State to manage the current pin
  const [currentPin, setCurrentPin] = useState(
    infoPinArray?.[0]?.walkthroughPins?.[0]
  )

  const handleInfoPinClick = (pin: WalkthroughPinType) => {
    setCurrentPin(pin)
    if (!isDesktop) dialogRef?.close()
  }

  return (
    <BoxContainer className='relative flex w-full items-center justify-center gap-4 xl:w-[80%] xl:items-start'>
      <EmblaCarousel
        autoPlayOptions={{
          active: false,
        }}
        emblaWrapperProps={{
          className:
            'embla-wrapper px-2 xl:px-0 max-w-[min(400px,100%)] md:max-w-[min(650px,100%)] lg:max-w-[unset] flex flex-col gap-4 overflow-hidden justify-center items-center relative xl:gap-0',
        }}
        sliderWrapperProps={{
          className: cn(
            moreThanOneElementInArray && 'cursor-grab active:cursor-grabbing'
          ),
        }}
        gradientBreakpoints={
          isDesktop ? getArrayBasedOnLength(infoPinArray?.length ?? 1) : []
        }
        controlsWrapperProps={{
          className:
            'flex items-center justify-center p-2 bg-brand-300 z-10 shadow-lg gap-2 sm:gap-3 relative xl:absolute bottom-unset xl:bottom-2 left-unset xl:left-4 rounded-[2rem] xl:rounded-xs',
        }}
        showButtons={isDesktop ? false : moreThanOneElementInArray}
        showDots={moreThanOneElementInArray}
        dotsStyling={{
          currentDotStyle: 'bg-brand hover:cursor-default',
          dotProps: {
            className:
              'hover:bg-brand-400 min-w-6 min-h-6 rounded-[50%] xl:rounded-sm flex bg-brand',
          },
          dotsWrapperProps: {
            className:
              'flex wrap items-center justify-center gap-1.5 sm:gap-2 h-full',
          },
        }}
        buttonStyling={{
          className:
            'hover:bg-brand-400 min-w-8 min-h-8 sm:min-w-7 msm:min-h-7 rounded-[50%] flex bg-brand',
          arrowProps: {
            className: 'min-w-5 min-h-5 sm:min-w-6 sm:min-h-6 text-background',
          },
        }}
        options={{
          loop: isDesktop,
          align: isDesktop ? 'center' : 'start',
          watchDrag: moreThanOneElementInArray,
        }}
        sectionContext='walkthrough_section'
      >
        {infoPinArray?.map((grid, index) => {
          return (
            <WalkthroughGrid
              key={`${index}-walkthrough-grid`}
              isDesktop={isDesktop}
              image={grid?.walkthroughGridImage}
              pinArray={grid?.walkthroughPins}
              currentPin={currentPin}
              handleInfoPinClick={(pin: WalkthroughPinType) =>
                handleInfoPinClick(pin)
              }
            />
          )
        }) ?? []}
      </EmblaCarousel>
      <WalkthroughInfo
        setDialog={setDialogRef}
        setCurrentPin={() => setCurrentPin(undefined)}
        // showBackdrop={showBackdrop}
        currentPin={currentPin}
        isDesktop={isDesktop}
        parentSectionId={parentSectionId}
      />
    </BoxContainer>
  )
}

type WalkthroughSectionProps = {
  sectionData: WalkthroughSectionType & SharedSectionDataType
}

/** WalkthroughSection is a wrapper for walkthroughContainer, which also has a title and subtitle
 */
export const WalkthroughSection = ({
  sectionData,
}: WalkthroughSectionProps) => {
  const objectId: AnalyticsObjectIds = `${sectionData?.slug?.current}_walkthrough_section`
  const { title, subtitle, desktopWalkthroughs, mobileWalkthroughs } =
    sectionData
  return (
    <BoxContainer
      as={'section'}
      aria-label={convertToPlainText({ value: sectionData?.title })}
      className='m-[0,auto] flex flex-col items-center gap-6 bg-brand-100 px-4 py-12 lg:px-0 xl:gap-10'
      id={sectionData?.slug?.current}
    >
      <HeaderInfo
        title={title}
        subtitle={subtitle}
        parentSectionId={objectId}
        className='flex w-full flex-col items-center justify-center gap-1'
      />
      <WalkthroughContainer
        desktopWalkthroughs={desktopWalkthroughs}
        mobileWalkthroughs={mobileWalkthroughs}
        parentSectionId={objectId}
      />
    </BoxContainer>
  )
}
