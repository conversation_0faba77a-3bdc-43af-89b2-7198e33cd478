import { GenericButton } from '../../components/common/GenericButton'
import { cn } from '../../helper-functions/UtilFunctions'
import type { WalkthroughPinType } from '../../types/sections/walkthrough-section.types'

export function WalkthroughIconButton({
  pin,
  index,
  currentPin,
  isDesktop,
  handleInfoPinClick,
}: {
  pin: WalkthroughPinType
  index: number
  isDesktop?: boolean
  currentPin?: WalkthroughPinType
  handleInfoPinClick: (pin: WalkthroughPinType) => void
}) {
  const pinPlacement = [
    pin?.placement?.column && `col-[repeat(${pin.placement.column})]`,
    pin?.placement?.row && `row-[repeat(${pin.placement.row})]`,
  ].filter(Boolean)

  const pinButtonStyling = cn(
    // Base styles
    'appearance-none outline-none',
    'cursor-pointer',
    'transition-all duration-200 ease-linear',
    'absolute z-10',
    'h-9 w-9',
    '[--pinBorderColor:transparent]',

    // Grid positioning (dynamic values)
    ...pinPlacement,

    // Pseudo states
    'disabled:scale-125 disabled:[--pinBorderColor:rgb(var(--colors-brand-inverted))]',
    'hover:[--pinBorderColor:rgb(var(--colors-brand-inverted))]',

    // Before pseudo-element (border)
    "before:absolute before:inset-0 before:content-['']",
    'before:border-2 before:border-[var(--pinBorderColor)]',
    'before:scale-105 before:transition-colors before:duration-200',
    'before:rounded-full',

    // After pseudo-element (inner circle)
    "after:absolute after:inset-0 after:content-['']",
    'after:scale-80 after:rounded-full after:bg-brand-inverted'
  )

  return (
    <GenericButton
      disabled={isDesktop ? pin?.title === currentPin?.title : false}
      className={pinButtonStyling}
      onClick={() => handleInfoPinClick(pin)}
      objectId={'walkthrough_pin_choose'}
      objectValue={currentPin}
      key={index}
    >
      {pin?.title !== currentPin?.title && (
        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
          <circle cx='12' cy='12' r='0' fill='currentColor'>
            <animate
              attributeName='r'
              calcMode='spline'
              dur='1.5s'
              keySplines='.52,.6,.25,.99'
              repeatCount='indefinite'
              values='0;11'
            />
            <animate
              attributeName='opacity'
              calcMode='spline'
              dur='1.5s'
              keySplines='.52,.6,.25,.99'
              repeatCount='indefinite'
              values='1;0'
            />
          </circle>
        </svg>
      )}
    </GenericButton>
  )
}
