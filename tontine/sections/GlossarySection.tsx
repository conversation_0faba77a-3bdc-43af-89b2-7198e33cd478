import { BoxContainer } from '../components/common/BoxContainer'
import { HeaderInfo } from '../components/layouts/HeaderInfo'
import { LocalizedContentParser } from '../components/typography/LocalizedContentParser'
import { Title } from '../components/typography/Title'
import type {
  LocalizedContentType,
  LocalizedStringContentType,
  SharedSectionDataType,
} from '../types/sections/section.types'

type GlossarySectionProps = {
  sectionData: {
    glossaries?: Array<{
      stringTitle: LocalizedStringContentType
      markdown: LocalizedContentType
    }>
  } & SharedSectionDataType
}

export function GlossarySection({ sectionData }: GlossarySectionProps) {
  return (
    <BoxContainer
      as={'section'}
      className='bg-background'
      id={sectionData?.slug?.current}
    >
      <BoxContainer className='section-width pt-10 pb-15'>
        <HeaderInfo
          className='mb-0 [&>p]:leading-6'
          title={sectionData?.title}
          subtitle={sectionData?.subtitle}
        />
        <BoxContainer className='grid grid-cols-2 gap-2 md:gap-4'>
          {sectionData?.glossaries &&
            sectionData?.glossaries?.length > 0 &&
            sectionData?.glossaries?.map((glossary, index) => (
              <BoxContainer
                key={`glossary-box-${index}`}
                className='flex flex-col gap-2 rounded-md border border-gray-300 bg-white p-3 shadow-sm md:p-4 lg:gap-4 lg:p-6'
              >
                <Title
                  key={`glossary-${index}`}
                  className='font-bold text-2xl text-fg lg:text-3xl'
                >
                  {glossary?.stringTitle}
                </Title>
                <LocalizedContentParser
                  overrideElements={{
                    p: {
                      className: 'wrap-break-word text-fg text-sm lg:text-lg',
                    },
                  }}
                >
                  {glossary?.markdown}
                </LocalizedContentParser>
              </BoxContainer>
            ))}
        </BoxContainer>
      </BoxContainer>
    </BoxContainer>
  )
}
