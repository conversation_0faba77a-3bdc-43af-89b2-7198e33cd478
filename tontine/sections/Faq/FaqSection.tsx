'use client'

import { useState } from 'react'

import { BoxContainer } from '../../components/common/BoxContainer'
import { NextLink } from '../../components/common/NextLink'
import { HeaderInfo } from '../../components/layouts/HeaderInfo'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { Title } from '../../components/typography/Title'
import { FAQsWebschema } from '../../components/web-schema/FAQsWebschema'
import strings from '../../data-resource/strings.json'
import {
  generateSlug,
  splitArrayIntoEqualChunks,
} from '../../helper-functions/UtilFunctions'
import { useLanguage } from '../../providers/LanguageContext'
import { SectionEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { AnalyticsObjectIds } from '../../types/Analytics/AnalyticsObjectIds.types'
import type { SanityButton } from '../../types/common.types'
import type {
  FaqCategory,
  QuestionAndAnswer,
} from '../../types/sections/faq-section.types'
import type {
  LocalizedStringContentType,
  SharedSectionDataType,
} from '../../types/sections/section.types'
import { CategoryBar } from './CategoryBar'
import { FaqCollapsible } from './FaqCollapsible'
import { SearchBar } from './SearchBar'

type FaqSectionProps = {
  sectionData: {
    placeholderText: LocalizedStringContentType
    faqCategories: Array<FaqCategory>
    faqButton?: SanityButton
    faqAdditionalInfo?: LocalizedStringContentType
  } & SharedSectionDataType
}

/** `FaqSection` is component for FAQ section
 * - `faqTitle` Title and subtitle data of the section
 * - `faqCategories` Array of categories containing category title and array of questions, answers and tags
 * - `sectionSlug` value that gives this section an id
 */
export function FaqSection({ sectionData }: FaqSectionProps) {
  const [activeClass, setActiveClass] = useState(0)

  const categories = sectionData?.faqCategories

  // Automatically create Show All category so that the content writer does not have to do it manually in Sanity
  const showAllCategory = {
    categoryTitle: { en: 'Show All', es: 'Mostrar Todo', pt: 'Mostrar Tudo' },
    faqData: categories?.flatMap((category) => category?.faqData),
  }

  const allCategories = [...categories, showAllCategory]

  const handleClick = (index: number) => {
    setActiveClass(index)
  }
  const objectId: AnalyticsObjectIds = `${sectionData?.slug?.current}_faq_section`

  const dividedColumnsArray = splitArrayIntoEqualChunks<QuestionAndAnswer>(
    allCategories?.[activeClass]?.faqData ?? []
  )

  const slug = generateSlug(sectionData?.faqButton as SanityButton)

  const { language } = useLanguage()
  const placeholderText =
    sectionData?.placeholderText?.[language] ?? sectionData?.placeholderText?.en
  return (
    <BoxContainer
      as={'section'}
      aria-label={sectionData?.slug?.current ?? 'Frequently asked questions'}
      className='section wrapper section-width flex flex-col gap-5'
      id={sectionData?.slug?.current}
    >
      <HeaderInfo
        className='mb-0 flex flex-col items-center justify-center py-4'
        title={sectionData?.title}
        subtitle={sectionData?.subtitle}
      />

      <BoxContainer className='mb-2 flex w-full flex-col gap-8'>
        <SearchBar
          placeholderText={placeholderText}
          faqData={categories}
          parentSectionId={objectId}
        >
          <CategoryBar
            categories={allCategories}
            activeClass={activeClass}
            onClick={handleClick}
          />
          <FaqCollapsible
            faqData={dividedColumnsArray}
            parentSectionId={objectId}
          />
        </SearchBar>
      </BoxContainer>
      <BoxContainer className='my-4 flex flex-col items-center justify-center gap-4'>
        <Title className='text-grey-550 text-lg'>
          {sectionData?.faqAdditionalInfo}
        </Title>
        <NextLink
          href={slug}
          objectId={objectId}
          customEvent={SectionEvent.btn_clicked}
          linkLabel={strings.CONTACT_US}
          className='button solid mx-auto w-full smd:w-85'
        >
          <LocalizedContentParser>
            {sectionData?.faqButton?.buttonLabel}
          </LocalizedContentParser>
        </NextLink>
      </BoxContainer>

      <FAQsWebschema faqCategories={categories} />
    </BoxContainer>
  )
}
