import { BoxContainer } from '../../components/common/BoxContainer'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { WrappedText } from '../../components/typography/WrappedText'
import { Collapsible } from '../../components/ui/collapse/Collapse'
import strings from '../../data-resource/strings.json'
import { cn } from '../../helper-functions/UtilFunctions'
import { FAQEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { QuestionAndAnswer } from '../../types/sections/faq-section.types'
import type { ParentSectionID } from '../../types/sections/section.types'
import { ThumbsRatingButtons } from '../Feedback/ThumbsRatingButtons'

type FaqCollapsibleBoxProps = {
  faqData?: Array<Array<QuestionAndAnswer>>
  nothingFound?: boolean
} & ParentSectionID

/** A component that renders an accordion list of questions and answers.
 * It can also take a "nothingFound" boolean prop to render a message when no results are found.
 * The component is designed to be used in the FAQ section.
 */
export const FaqCollapsible = ({
  faqData,
  parentSectionId,
  nothingFound,
}: FaqCollapsibleBoxProps) => {
  return (
    <BoxContainer className='mx-auto flex flex-wrap xmd:flex-nowrap gap-6'>
      {faqData?.map((column, index) => (
        <BoxContainer
          key={`faq-column-${index}`}
          className='grid h-min w-full gap-4'
        >
          {column?.map((item, itemIndex) => (
            <Collapsible
              key={`collapsible-item-${itemIndex}`}
              title={item?.question}
              triggerProps={{
                className:
                  'button subtle rounded-sm p-3.5 flex text-gray-600 md:text-lg text-left items-start md:items-center shadow-none border-b-1 border-[rgba(0,0,0,0.05)] justify-between z-10 gap-2.5',
                collapseEvent: FAQEvent.item_collapsed,
                expandEvent: FAQEvent.item_expanded,
              }}
              triggerArrowProps={{
                className:
                  'group-aria-expanded/trigger:bg-brand-400 group-aria-expanded/trigger:text-background-100 rounded-full ring-1 ring-gray-200 min-w-6 min-h-6 md:min-w-6.5 md:min-h-6.5',
              }}
              rootProps={{
                className: 'h-min shadow-sm  rounded-md overflow-hidden',
              }}
              contentProps={{
                className: 'relative',
                contentInnerProps: {
                  className: cn(
                    'flex flex-col gap-3 rounded-b-md border-brand-400 border-l-3 pt-2 pr-7 pb-5 pl-4 text-grey-650'
                  ),
                },
              }}
            >
              <LocalizedContentParser parentSectionId={parentSectionId}>
                {item?.answer}
              </LocalizedContentParser>
              <ThumbsRatingButtons questionData={faqData} />
            </Collapsible>
          ))}
        </BoxContainer>
      ))}

      {nothingFound && (
        <BoxContainer>
          <WrappedText as={'p'}>
            {strings.FAQ_SEARCH_NO_RESULT_MESSAGE}
          </WrappedText>
        </BoxContainer>
      )}
    </BoxContainer>
  )
}
