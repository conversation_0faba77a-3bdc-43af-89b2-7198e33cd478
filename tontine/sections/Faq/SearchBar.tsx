import { SearchIcon } from '@sanity/icons'

import { BoxContainer } from '../../components/common/BoxContainer'
import { GenericButton } from '../../components/common/GenericButton'
import { FormContainer } from '../../components/typography/FormContainer'
import { InputWrapper } from '../../components/typography/InputWrapper'
import { Divider } from '../../components/ui/Divider'
import { cn, handleInputChange } from '../../helper-functions/UtilFunctions'
import { useSearchFunctionality } from '../../hooks/useFAQSearchFunctionality'
import { FAQEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { SearchBarProps } from '../../types/sections/faq-section.types'
import { FaqCollapsible } from './FaqCollapsible'

/**
 * Displays a search bar to search through a list of questions, providing search results into new container.
 */
export function SearchBar({
  children,
  placeholderText,
  faqData,
  parentSectionId,
}: SearchBarProps) {
  const {
    searchResults,
    searchQuery,
    setSearchQuery,
    nothingFound,
    handleSearch,
    handleClearInput,
  } = useSearchFunctionality(faqData ?? [])

  const emptySearchQuery = searchQuery?.length === 0

  return (
    <>
      <BoxContainer className='flex w-auto flex-row justify-center'>
        <BoxContainer className='grow-1 sm:grow-0'>
          <FormContainer onSubmitProp={handleSearch}>
            <InputWrapper
              inputProps={{
                className: cn(
                  'peer w-full rounded-l-full py-3 pr-8 pl-3 text-sm ring ring-gray-200 focus:shadow-brand-transparent focus:outline-none focus:ring-brand sm:w-[19rem] xl:w-96',
                  emptySearchQuery ? 'focus:ring-gray-500' : 'focus:ring-brand'
                ),
                type: 'text',
                placeholder: placeholderText,
                maxLength: 70,
              }}
              inputVal={searchQuery}
              inputOnChange={(e) => handleInputChange(e, setSearchQuery)}
              inputClearButton={searchQuery?.length > 0}
              inputOnClear={handleClearInput}
              objectId={'faq_search_bar'}
              clearEvent={FAQEvent.search_cleared}
              trackInput
            />
          </FormContainer>
        </BoxContainer>

        <GenericButton
          className={
            'button solid w-[46px] rounded-l-none p-0 pr-1.5 shadow-none ring ring-brand duration-500'
          }
          onClick={handleSearch}
          customEvent={FAQEvent.searched}
          objectValue={searchQuery}
          disabled={emptySearchQuery}
          type='submit'
        >
          <SearchIcon className='h-7 w-7 text-background-100' />
        </GenericButton>
      </BoxContainer>
      {(searchResults?.length > 0 || nothingFound) && (
        <FaqCollapsible
          faqData={searchResults}
          parentSectionId={parentSectionId}
          nothingFound={nothingFound}
        />
      )}

      {searchResults?.length > 0 && (
        <Divider variant='solid' orientation='horizontal' className='my-8' />
      )}
      {searchResults?.length === 0 && children}
    </>
  )
}
