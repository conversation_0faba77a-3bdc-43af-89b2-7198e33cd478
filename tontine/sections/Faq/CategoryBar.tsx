import { BoxContainer } from '../../components/common/BoxContainer'
import { GenericButton } from '../../components/common/GenericButton'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { cn } from '../../helper-functions/UtilFunctions'
import { FAQEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { FaqCategory } from '../../types/sections/faq-section.types'

type CategoryBarProps = {
  categories: Array<FaqCategory>
  activeClass: number
  onClick: (index: number) => void
}

/** CategoryBar component represents a bar displaying categories as buttons. */
export function CategoryBar({
  categories,
  activeClass,
  onClick,
}: CategoryBarProps) {
  return (
    <BoxContainer
      className={cn(
        'mb-5 flex w-min-none flex-wrap items-center justify-center gap-3 smd:flex-nowrap xl:w-min-[22.5rem]',
        categories?.length >= 3 && 'flex-wrap'
      )}
    >
      {categories.map((categoryItem, index) => (
        <GenericButton
          key={`category-button-${index}`}
          isActive={activeClass === index}
          onClick={() => onClick(index)}
          objectValue={categoryItem?.categoryTitle?.en}
          customEvent={FAQEvent.category_selected}
          className={cn(
            'button',
            'flex-grow data-active:pointer-events-none smd:flex-grow-0',
            activeClass === index ? 'solid' : 'subtle'
          )}
        >
          <LocalizedContentParser
            overrideElements={{
              p: {
                className: 'w-max',
              },
            }}
          >
            {categoryItem?.categoryTitle}
          </LocalizedContentParser>
        </GenericButton>
      ))}
    </BoxContainer>
  )
}
