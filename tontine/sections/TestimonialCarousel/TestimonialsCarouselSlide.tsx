import type { SVGProps } from 'react'

import { BoxContainer } from '../../components/common/BoxContainer'
import { SanityImage } from '../../components/common/SanityImage'
import { EmblaSlide } from '../../components/embla-carousel/EmblaSlide'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { Title } from '../../components/typography/Title'
import { WrappedText } from '../../components/typography/WrappedText'
import strings from '../../data-resource/strings.json'
import { cn } from '../../helper-functions/UtilFunctions'
import { STYLE } from '../../styles/style'
import type {
  ParentSectionID,
  TestimonialCarouselPost,
} from '../../types/sections/section.types'

type TestimonialCarouselSlideProps = {
  slideData: TestimonialCarouselPost
  padding?: number | string
  slideLength: number
} & ParentSectionID

function RiDoubleQuotesR(props: SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' {...props} viewBox='0 0 256 256'>
      <path
        fill='currentColor'
        d='M116 72v88a48.05 48.05 0 0 1-48 48a8 8 0 0 1 0-16a32 32 0 0 0 32-32v-8H40a16 16 0 0 1-16-16V72a16 16 0 0 1 16-16h60a16 16 0 0 1 16 16m100-16h-60a16 16 0 0 0-16 16v64a16 16 0 0 0 16 16h60v8a32 32 0 0 1-32 32a8 8 0 0 0 0 16a48.05 48.05 0 0 0 48-48V72a16 16 0 0 0-16-16'
      />
    </svg>
  )
}

export function TestimonialCarouselSlide({
  slideData,
  padding = 0,
  slideLength,
  parentSectionId,
}: TestimonialCarouselSlideProps) {
  /** Get the slide size based on the length. */
  const getSlideSize = (length: number) => {
    if (length) {
      if (length > 3) {
        return /*tw*/ 'lg:flex-(--embla-size-md) xl:flex-(--embla-size-lg)' /*tw*/
      }
      if (length === 3) {
        return /*tw*/ 'lg:flex-(--embla-size-md) xl:flex-(--embla-size-md)' /*tw*/
      }
    }

    return /*tw*/ 'lg:flex-(--embla-size-base) xl:flex-(--embla-size-base)' /*tw*/
  }

  return (
    <EmblaSlide
      className={cn(
        'flex sm:flex-(--embla-size-base)',
        getSlideSize(slideLength),
        padding
      )}
    >
      <BoxContainer
        className={cn(
          strings.EMBLA_SCALE_CLASS, // Mandatory for scale to work.
          'flex grow select-none flex-col rounded-md bg-grey-200 p-2 sm:p-5'
        )}
      >
        <BoxContainer className={'h-8 text-brand sm:h-12'}>
          <RiDoubleQuotesR height='inherit' />
        </BoxContainer>

        <LocalizedContentParser
          parentSectionId={parentSectionId}
          overrideElements={{
            p: { className: 'w-full' },
          }}
          renderWrapper
          className={
            'flex w-full grow items-center py-5 text-left font-semibold text-fg text-lg lg:py-10 lg:text-2xl'
          }
        >
          {slideData?.title}
        </LocalizedContentParser>

        <BoxContainer className='flex w-full items-center justify-between'>
          <BoxContainer className='grid gap-1 border-grey-450 border-l-2 pl-2'>
            <Title
              title={slideData?.carouselPostReviewerName}
              className='text-1 sm:text-2 lg:text-3'
            />

            <WrappedText className='text-1 text-grey-650 sm:text-2 lg:text-3'>
              {slideData?.carouselReviewerDescription}
            </WrappedText>
          </BoxContainer>

          <SanityImage
            fillProp
            skeletonProps={{
              className:
                'bg-grey-200 w-14 sm:w-18 lg:w-22 h-14 sm:h-18 lg:h-22 rounded-full overflow-hidden',
            }}
            {...slideData?.carouselPostReviewerImage}
            sizes={STYLE.TESTIMONIAL_IMAGE_OPTIMIZATION}
          />
        </BoxContainer>
      </BoxContainer>
    </EmblaSlide>
  )
}
