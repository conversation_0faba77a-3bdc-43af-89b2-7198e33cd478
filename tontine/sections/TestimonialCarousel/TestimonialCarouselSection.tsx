import { BoxContainer } from '../../components/common/BoxContainer'
import { EmblaCarousel } from '../../components/embla-carousel/EmblaCarousel'
import { HeaderInfo } from '../../components/layouts/HeaderInfo'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { CONSTANTS } from '../../data-resource/constants'
import strings from '../../data-resource/strings.json'
import { cn, getArrayBasedOnLength } from '../../helper-functions/UtilFunctions'
import type {
  SharedSectionDataType,
  TestimonialCarouselSectionType,
} from '../../types/sections/section.types'
import { TestimonialCarouselSlide } from './TestimonialsCarouselSlide'

type CarouselProps = {
  sectionData: TestimonialCarouselSectionType & SharedSectionDataType
}

/**
 * `Carousel Section` displays a chakra carousel with data from sanity
 * - `sectionData` Data for the section
 * - `sectionSlug` value that gives this section an id
 * - `carouselSectionFeedback` text under carousel section that serves as text for feedback or complaints about content on carousel section.
 */
export const CarouselSection = ({ sectionData }: CarouselProps) => {
  return (
    <BoxContainer
      as={'section'}
      aria-label={sectionData?.slug?.current}
      tabIndex={0}
      className={cn(
        'mx-auto grid scroll-mt-(--nav-height) gap-4 py-10',
        sectionData?.carouselPosts && sectionData?.carouselPosts?.length < 3
          ? 'max-w-[50rem]'
          : 'max-w-[100%]'
      )}
      id={sectionData?.slug?.current}
    >
      <HeaderInfo title={sectionData?.title} titleProps={{ as: 'h2' }} />
      <EmblaCarousel
        autoPlayOptions={{
          playOnInit: true,
          delay: CONSTANTS.CAROUSEL.DELAY.TESTIMONIALS,
          stopOnMouseEnter: true,
          stopOnInteraction: false,
        }}
        shouldScale
        scaleFactor={CONSTANTS.CAROUSEL.TWEEN_FACTOR.SCALE}
        scaleClass={strings.EMBLA_SCALE_CLASS}
        emblaWrapperProps={{
          className:
            'flex flex-col mx-auto overflow-hidden relative gap-6 md:gap-10 px-6 w-full max-w-[35rem] lg:max-w-full xl:max-w-[min(90rem,100%)]',
        }}
        sliderWrapperProps={{
          className: 'cursor-grab active:cursor-grabbing',
        }}
        controlsWrapperProps={{
          className: 'flex justify-center gap-2 items-center sm:gap-4',
        }}
        gradientClassName='embla-gradient flex flex-col items-center gap-6 md:gap-10'
        showButtons
        showDots
        gradientBreakpoints={getArrayBasedOnLength(
          sectionData?.carouselPosts?.length ?? 0
        )}
        dotsStyling={{
          dotsWrapperProps: {
            className:
              'flex wrap items-center justify-center h-full bg-brand-100 rounded-full p-2 gap-[.35rem] sm:gap-[.5rem]',
          },
          dotProps: {
            className:
              'p-0 min-w-5 h-5 rounded-full bg-background-100 border-1 border-brand-100 hover:bg-brand-400',
          },
          currentDotStyle: 'pointer-events-none bg-brand',
        }}
        buttonStyling={{
          className:
            'p-0 border-2 border-brand-100 hover:bg-brand-400 h-8 min-w-8 flex items-center justify-center rounded-full bg-brand text-background-100',
          arrowProps: {
            className: 'text-background-100 min-h-5 h-5',
          },
        }}
        options={{ loop: true, align: 'center' }}
        sectionContext='carousel_section'
      >
        {sectionData?.carouselPosts?.map((slide, index) => {
          return (
            <TestimonialCarouselSlide
              slideData={slide}
              key={`carousel-card-id-${index}`}
              slideLength={sectionData?.carouselPosts?.length ?? 0}
              parentSectionId={`${sectionData?.slug?.current}_carousel_section`}
            />
          )
        }) ?? []}
      </EmblaCarousel>
      {sectionData?.localizedCarouselSectionFeedback && (
        <BoxContainer className='mx-auto mt-3 max-w-[81.25rem]'>
          <LocalizedContentParser
            renderWrapper
            className='m-auto text-center text-grey-450 text-sm md:text-base xl:text-lg'
            parentSectionId={`${sectionData?.slug?.current}_carousel_section`}
          >
            {sectionData?.localizedCarouselSectionFeedback}
          </LocalizedContentParser>
        </BoxContainer>
      )}
    </BoxContainer>
  )
}
