import { BoxContainer } from '../../components/common/BoxContainer'
import { HeaderInfo } from '../../components/layouts/HeaderInfo'
import type { AnalyticsObjectIds } from '../../types/Analytics/AnalyticsObjectIds.types'
import type { PersonType } from '../../types/common.types'
import type { SharedSectionDataType } from '../../types/sections/section.types'
import { PersonsContainer } from './PersonsContainer'

type TeamSectionProps = {
  sectionData: {
    hoverEffect?: boolean
    teamMembers?: Array<PersonType>
  } & SharedSectionDataType
}

/**
 * `TeamSection` is used to displays information about a team, including the team name, logo and information about every team member
 * - `teamsIcon` icon that represents the team
 * - `sectionSlug` value that gives this section an id
 * - `teamsTitle` Name of the team ex. Backend Team..
 * - `teamsSubtitle` Description for the team
 * - `teamMembers` Array of each team member data
 */
export function TeamSection({ sectionData }: TeamSectionProps) {
  const objectId: AnalyticsObjectIds = `${sectionData.slug?.current}_team_section`
  return (
    <BoxContainer
      as={'section'}
      aria-label={sectionData?.slug?.current || 'The team'}
      className='mx-auto grid w-fit scroll-mt-(--nav-height) place-content-center px-2 py-8'
      id={sectionData?.slug?.current}
    >
      <HeaderInfo
        title={sectionData?.title}
        subtitle={sectionData?.subtitle}
        parentSectionId={objectId}
      />
      <PersonsContainer
        teamMembers={sectionData?.teamMembers}
        hoverEffect={sectionData?.hoverEffect}
        parentSectionId={objectId}
      />
    </BoxContainer>
  )
}
