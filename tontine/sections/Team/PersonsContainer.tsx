import {
  BoxContainer,
  type BoxContainerProps,
} from '../../components/common/BoxContainer'
import type { PersonType } from '../../types/common.types'
import type { ParentSectionID } from '../../types/sections/section.types'
import { Person } from './Person'

type PersonsContainerProps = {
  teamMembers?: Array<PersonType>
  hoverEffect?: boolean
} & ParentSectionID &
  BoxContainerProps

/**
 * `PersonsContainer` is used to display a list of all of the persons
 * - `teamMember` data for every teamMember that needs to be rendered
 */
export function PersonsContainer({
  teamMembers,
  hoverEffect,
  parentSectionId,
  ...rest
}: PersonsContainerProps) {
  return (
    <BoxContainer
      {...rest}
      className='grid grid-cols-1 xml:grid-cols-3 xxl:grid-cols-4 place-content-center gap-6 lg:grid-cols-2'
    >
      {teamMembers &&
        teamMembers?.length > 0 &&
        teamMembers?.map((member, index) => (
          <Person
            person={member}
            hoverEffect={hoverEffect}
            parentSectionId={parentSectionId}
            key={`memberId-${member?.personName}-${index}`}
          />
        ))}
    </BoxContainer>
  )
}
