'use client'

import { BoxContainer } from '../../components/common/BoxContainer'
import { SanityImage } from '../../components/common/SanityImage'
import {
  cn,
  detectDeviceType,
  replaceWhitespace,
} from '../../helper-functions/UtilFunctions'
import { useIntersectObserver } from '../../hooks/useIntersectObserver'
import { STYLE } from '../../styles/style'
import type { PersonType } from '../../types/common.types'
import type { ParentSectionID } from '../../types/sections/section.types'
import { PersonInformation } from './PersonInformation'

type PersonProps = {
  children?: React.ReactNode
  person: PersonType
  hoverEffect?: boolean
} & ParentSectionID

/**
 * `PersonWrapper` component, which displays information about a single person.
 * - `person` on what should be rendered
 */
export function Person({ person, hoverEffect, parentSectionId }: PersonProps) {
  const personId = `person-${replaceWhitespace({ str: person?.personName?.toLowerCase() })}`
  const { targetIntersecting: isIntersecting } = useIntersectObserver({
    ids: personId,
    root: null,
    rootMargin: '-40px',
    threshold: 1,
  })

  const isMobile = detectDeviceType() === 'mobile'

  return (
    <BoxContainer
      id={personId}
      className='person relative mx-auto flex min-h-[17.5rem] w-[16.5rem] flex-col items-center overflow-hidden rounded-md bg-grey-200 sm:min-h-[21.5rem] sm:w-[21rem]'
    >
      {hoverEffect && (
        <BoxContainer
          className={cn(
            'aiImage absolute inset-0 z-1 overflow-hidden md:duration-400',
            isMobile && isIntersecting && 'opacity-100'
          )}
        >
          <SanityImage
            {...person?.personImage}
            fillProp
            className='w-full'
            sizes={STYLE.PERSON_TEAM_SECTION_IMAGE_OPTIMIZATION}
            objectFitProp={'cover'}
          />
        </BoxContainer>
      )}
      <BoxContainer
        className={cn('absolute inset-0 z-0 overflow-hidden duration-400')}
      >
        <SanityImage
          {...person?.personImage}
          fillProp
          sizes={STYLE.PERSON_TEAM_SECTION_IMAGE_OPTIMIZATION}
          objectFitProp={'cover'}
        />
      </BoxContainer>
      <PersonInformation
        personInfo={person}
        parentSectionId={parentSectionId}
      />
    </BoxContainer>
  )
}
