import { BoxContainer } from '../../components/common/BoxContainer'
import { NextLink } from '../../components/common/NextLink'
import { SanityImage } from '../../components/common/SanityImage'
import { WrappedText } from '../../components/typography/WrappedText'
import type { PersonType } from '../../types/common.types'
import type { ParentSectionID } from '../../types/sections/section.types'

type PersonInformationProps = {
  personInfo: PersonType
} & ParentSectionID

/**
 * `PersonInformation` is component that displays a teamMember name description and his social link.
 * - `personInfo` is data that's provided for person
 */
export function PersonInformation({
  personInfo,
  parentSectionId,
}: PersonInformationProps) {
  return (
    <BoxContainer className='z-10 flex h-full w-full items-end justify-between bg-[linear-gradient(180deg,transparent_0%,transparent_50%,black_100%)] px-4 pb-3.5'>
      <BoxContainer tabIndex={0} className='z-200'>
        {personInfo?.personTitle && (
          <WrappedText className='text-[0.97rem] text-background-100'>
            {personInfo?.personTitle}
          </WrappedText>
        )}
        {personInfo?.personName && (
          <WrappedText className='font-semibold text-[1.2rem] text-background-100 leading-6'>
            {personInfo?.personName}
          </WrappedText>
        )}
      </BoxContainer>
      {personInfo?.personSocialLink?.url && (
        <NextLink
          href={personInfo?.personSocialLink?.url}
          objectId={parentSectionId}
          hideExternalIcon
        >
          <SanityImage
            {...personInfo?.personSocialLink?.icon}
            skeletonProps={{ className: 'h-[2rem] w-[2rem]' }}
            fillProp
          />
        </NextLink>
      )}
    </BoxContainer>
  )
}
