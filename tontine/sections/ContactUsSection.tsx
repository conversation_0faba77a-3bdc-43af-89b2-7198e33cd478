'use client'

import { captureException } from '@sentry/nextjs'
import { isCancel } from 'axios'
import type { FormEvent } from 'react'
import { useState } from 'react'

import track from '../app/api/analytics'
import { BoxContainer } from '../components/common/BoxContainer'
import { GenericButton } from '../components/common/GenericButton'
import { NextLink } from '../components/common/NextLink'
import { HeaderInfo } from '../components/layouts/HeaderInfo'
import { NextTurnstile } from '../components/turnstile/Turnstile'
import type { InputWrapperProps } from '../components/typography/InputWrapper'
import { InputWrapper } from '../components/typography/InputWrapper'
import { TextArea } from '../components/typography/TextArea'
import { WrappedText } from '../components/typography/WrappedText'
import { Field, type FieldProps } from '../components/ui/Field'
import { Select } from '../components/ui/select/Select'
import { CONSTANTS } from '../data-resource/constants'
import strings from '../data-resource/strings.json'
import {
  cn,
  createContactUsEmailBody,
  handleInputChange,
  isEmailValid,
} from '../helper-functions/UtilFunctions'
import { useDebouncedValidation } from '../hooks/useDebounce'
import { useContactUsForm } from '../hooks/useFormFields'
import { defaultRecipientMail } from '../serverless/keys'
import {
  ContactEvent,
  ErrorEvent,
} from '../types/Analytics/AnalyticsEvents.types'
import type {
  ContactUsSectionType,
  SharedSectionDataType,
} from '../types/sections/section.types'

/**
 *  Contact us form which consist of First name, Last name, Email,dropdown select and Message
 * - `submitButtonLabel` - Label for submit button
 * - `selectInputOptions` - Options for dropdown select
 * - `placeholderEmailInput` - Placeholder for email input
 * - `placeholderNameInput` - Placeholder for first name input
 * - `placeholderSelectInput` - Placeholder for dropdown select
 * - `placeholderSurnameInput` - Placeholder for last name input
 * - `contactUsTitle` - Title for the section
 * - `placeholderTextareaInput` - Placeholder for textarea input
 * - `sectionSlug` value that gives this section an id
 */
export function ContactUsSection({
  sectionData,
}: {
  sectionData: ContactUsSectionType & SharedSectionDataType
}) {
  const [renderMailTo, setRenderMailTo] = useState(false)
  const [loaded] = useState(true)

  let trackTimeout: NodeJS.Timeout | null = null

  const {
    email,
    isLoading,
    firstName,
    lastName,
    message,
    selectedOption,
    setEmail,
    setFirstName,
    setLastName,
    setMessage,
    setSelectedOption,
    formSubmit,
    setIsLoading,
  } = useContactUsForm()

  const emailValidationStatus = useDebouncedValidation(
    isEmailValid,
    email,
    CONSTANTS.DEBOUNCE_TIME
  )

  /**
   * Submits the contact us form details, in case of turnstile verification
   * fails or bot activity is detected, we render a mailTo component
   */
  async function onSubmit(e: FormEvent<HTMLFormElement>) {
    try {
      e.preventDefault()
      if (!emailValidationStatus.isValid || renderMailTo) return
      const formData = new FormData(e.currentTarget)
      const token = formData.get('cf-turnstile-response') ?? ''
      // Set render mailto is just an onFailed callback, for now if any error
      // occurs we will render an alternative way for the user to submit their email
      await formSubmit({ token })
    } catch (err) {
      // Error occurred provide alternative way for user to submit the form
      setRenderMailTo(true)
      if (!isCancel(err)) {
        captureException(err)
      }
      if (trackTimeout) {
        clearTimeout(trackTimeout)
      }
      // Delay the track function by 200ms
      trackTimeout = setTimeout(() => {
        track({
          event: ErrorEvent.encountered,
          properties: {
            object_id: 'contact_us_submit',
            object_value: strings.MESSAGE_NOT_SENT,
            description: CONSTANTS.ANALYTICS_DESCRIPTIONS.CONTACT_US_ERROR,
          },
        })
      }, CONSTANTS.ANALYTICS_THROTTLE_DURATION + 100)
    }
  }

  const selectCollection = sectionData?.selectInputOptions?.map((option) => ({
    value: option,
    label: option,
  }))

  const generalInputProps: InputWrapperProps['inputProps'] = {
    className:
      'bg-background-100 rounded-sm placeholder:text-gray-muted h-12 grow w-full pl-2 ring-1 ring-grey-400 group-data-invalid/field:ring-error-red group-data-valid/field:ring-brand-200',
    required: true,
  }

  const generalFieldProps: FieldProps = {
    className: 'grow w-full flex-col flex gap-2 overflow-visible',
    loading: !loaded,
    showSkeleton: true,
    required: true,
    labelProps: {
      className: 'flex gap-2',
    },
  }

  return (
    <BoxContainer
      className='mx-auto my-0 min-h-(--nav-height-offset) w-full max-w-[125rem] scroll-mt-[--nav-height] flex-col rounded-sm bg-background px-2 py-9 md:px-0 md:py-12'
      id={sectionData?.slug?.current}
    >
      <BoxContainer className='mb-7'>
        <HeaderInfo title={sectionData?.title} titleProps={{ as: 'h1' }} />
      </BoxContainer>
      <form
        onSubmit={onSubmit}
        className='mx-auto flex h-full w-[85%] max-w-full flex-col gap-5 md:w-[40rem] lg:w-[60rem]'
      >
        <BoxContainer className='flex flex-col justify-center gap-5 md:flex-row'>
          <InputWrapper
            fieldProps={{
              label: 'Name',
              valid: firstName?.length > 0,
              ...generalFieldProps,
            }}
            inputProps={{
              ...generalInputProps,
              autoComplete: 'name',
              type: 'text',
              name: 'first_name',
              id: 'first-name',
              placeholder: sectionData?.placeholderNameInput,
            }}
            inputClearButton
            inputOnClear={() => setFirstName('')}
            inputVal={firstName}
            inputOnChange={(event) => handleInputChange(event, setFirstName)}
            trackInput
            objectId='contact_us_first_name'
          />
          <InputWrapper
            fieldProps={{
              label: 'Surname',
              valid: lastName?.length > 0,
              ...generalFieldProps,
            }}
            inputClearButton
            inputOnClear={() => setLastName('')}
            inputProps={{
              ...generalInputProps,
              autoComplete: 'name',
              type: 'text',
              name: 'last_name',
              id: 'last-name',
              placeholder: sectionData?.placeholderSurnameInput,
            }}
            inputVal={lastName}
            inputOnChange={(event) => handleInputChange(event, setLastName)}
            trackInput
            objectId='contact_us_last_name'
          />
        </BoxContainer>

        <InputWrapper
          fieldProps={{
            valid: emailValidationStatus.isValid && email?.length > 0,
            invalid: !emailValidationStatus.isValid && email?.length > 0,
            errorText: emailValidationStatus.errorMessage,
            label: 'Email',
            ...generalFieldProps,
          }}
          inputProps={{
            ...generalInputProps,
            autoComplete: 'email',
            type: 'email',
            name: 'email',
            placeholder: sectionData?.placeholderEmailInput,
          }}
          inputClearButton
          inputOnClear={() => setEmail('')}
          inputVal={email}
          inputOnChange={(event) => handleInputChange(event, setEmail)}
          trackInput
          objectId='contact_us_email'
        />
        <Field
          label='Subject'
          {...generalFieldProps}
          valid={Boolean(selectedOption)}
        >
          <Select
            options={selectCollection}
            placeholder='Select an option'
            setSelectedValue={setSelectedOption}
            clearable
            className={cn(generalInputProps.className, 'rounded-sm p-0')}
          />
        </Field>

        <TextArea
          fieldProps={{
            label: 'Message',
            valid: message?.length > 0,
            ...generalFieldProps,
          }}
          placeholder={sectionData.placeholderTextareaInput}
          value={message}
          inputOnChange={(event) => handleInputChange(event, setMessage)}
          className={cn(
            generalInputProps.className,
            'min-h-30 resize-none pt-2 pr-10'
          )}
          trackInput
          objectId='contact_us_text_area'
        />

        <NextTurnstile
          onError={() => {
            setRenderMailTo(true)
            setIsLoading(false)
          }}
          onSuccess={() => {
            setIsLoading(false)
          }}
          onExpire={() => {
            setRenderMailTo(true)
          }}
          onTimeout={() => {
            setRenderMailTo(true)
          }}
        />

        <GenericButton
          disabled={renderMailTo}
          type='submit'
          isLoading={isLoading}
          className='button solid mx-auto mt-2 w-full rounded-full uppercase md:max-w-[40%]'
          customEvent={ContactEvent.submit}
          objectId='contact_us_submit'
          objectValue={selectedOption || sectionData.placeholderSelectInput}
          label={sectionData.submitButtonLabel}
        />

        {renderMailTo && (
          <BoxContainer className='mt-4 text-center text-xl'>
            <WrappedText className='w-[16rem] sm:w-full'>
              {strings.SUSPICIOUS_BOT_ACTIVITY_PART1}
            </WrappedText>
            <BoxContainer className='flex flex-wrap items-center justify-center'>
              <NextLink
                href={`mailto:${defaultRecipientMail}?subject=${encodeURIComponent(
                  strings.FORM_EMAIL_SUBJECT
                )}&body=${encodeURIComponent(
                  createContactUsEmailBody({
                    selectedOption,
                    firstName,
                    lastName,
                    email,
                    message,
                  }) ?? ''
                )}`}
                customEvent={ContactEvent.email}
                objectId={'contact_us_mailto'}
                className='mx-1 cursor-pointer font-semibold text-brand-550 underline'
              >
                {strings.CLICK_HERE}
              </NextLink>
              <WrappedText>{strings.SUSPICIOUS_BOT_ACTIVITY_PART2}</WrappedText>
            </BoxContainer>
          </BoxContainer>
        )}
      </form>
    </BoxContainer>
  )
}
