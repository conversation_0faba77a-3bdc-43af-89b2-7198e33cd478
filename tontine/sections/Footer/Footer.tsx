import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'

import { BoxContainer } from '../../components/common/BoxContainer'
import { HideContent } from '../../components/common/HideContent'
import { Divider } from '../../components/ui/Divider'
import { UI_TEST_ID } from '../../data-resource/data-test-ids'
import {
  generateSlug,
  getAttribute,
} from '../../helper-functions/UtilFunctions'
import type { SharedPageData } from '../../types/shared-page-data.types'
import { FooterHeaderNavItems } from './footer-components/FooterHeaderNavItems'
import { MediaAndLogo } from './footer-components/MediaAndLogo'
import { TermsAndPolicy } from './footer-components/TermsAndPolicy'

/**
 * Footer is used to render the footer on page
 */
export function Footer({
  sharedPageData,
}: {
  sharedPageData?: SharedPageData
}) {
  const footerData = sharedPageData?.footer

  const footerId = footerData?.slug ?? 'Footer'

  const homePath = '/'

  const footerDisclaimer = footerData?.localizedTontineCopyrightContent

  return (
    <HideContent>
      <BoxContainer
        as={'footer'}
        className='scroll-mt-(--nav-height) bg-grey-800 pt-15 pb-15'
        dataTestId={UI_TEST_ID?.footer}
        id={footerId}
      >
        <BoxContainer
          as={'section'}
          className='mx-auto flex w-[80%] max-w-[125rem] flex-col justify-around gap-6 lg:flex-row lg:gap-0'
          {...{
            ...getAttribute(true, 'aria-label', 'Socials and links'),
          }}
        >
          <MediaAndLogo
            goHome={homePath}
            img={footerData?.footerWebsiteLogo}
            socialLinks={
              footerData?.footerSocialMediaSection?.[0]?.socialMediaSectionItems
            }
          />
          {footerData?.footerNavigationMenu.map((item, index: number) => {
            const slug = generateSlug(item)

            return (
              <FooterHeaderNavItems
                key={`footer-header-list-${slug}-${index}`}
                header={item?.stringTitle}
                headerSlug={slug}
                subNavItems={item?.navigationSubMenuItems}
              />
            )
          })}
        </BoxContainer>
        <Divider
          variant='solid'
          className='mx-auto my-8 max-w-[70%] border-background/30'
        />

        <BoxContainer
          as='section'
          aria-label='Terms and policy'
          className='mx-auto flex max-w-[90%] flex-col items-center text-center'
        >
          <TermsAndPolicy
            footerLinks={footerData?.footerLinks}
            parentSectionId={`${footerId}_footer`}
          />
          <LocalizedContentParser
            as={'div'}
            className='w-[90%] xl:w-[70%]'
            renderWrapper
            parentSectionId={`${footerId}_footer`}
            overrideElements={{
              p: {
                className: 'm-auto text-background-100/30 leading-6.5',
              },
            }}
          >
            {footerDisclaimer}
          </LocalizedContentParser>
        </BoxContainer>
      </BoxContainer>
    </HideContent>
  )
}
