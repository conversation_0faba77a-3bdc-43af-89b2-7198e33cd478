import { BoxContainer } from '../../../components/common/BoxContainer'
import { SocialLink } from '../../../components/common/SocialLink'
import { FooterEvent } from '../../../types/Analytics/AnalyticsEvents.types'
import type {
  SanityImageType,
  SocialMediaIcon,
} from '../../../types/common.types'
import { FooterLogo } from './FooterLogo'

type MediaAndLogoProps = {
  img?: SanityImageType
  socialLinks?: Array<SocialMediaIcon>
  goHome: string
}

/**
 * MediaAndLogo is used to display the footer logo and social media icons
 */
export function MediaAndLogo({ img, socialLinks, goHome }: MediaAndLogoProps) {
  return (
    <BoxContainer className='flex flex-col items-center'>
      {img && (
        <FooterLogo {...img} href={goHome} parentSectionId='footer_main_link' />
      )}
      <BoxContainer
        as={'ul'}
        className='flex items-center justify-center gap-0.5'
      >
        {socialLinks?.length &&
          socialLinks?.map((item: SocialMediaIcon) => {
            return (
              <SocialLink
                key={`socialLink-${item?.url}`}
                href={item?.url}
                title={item?.title}
                img={item?.icon}
                parentSectionId='footer_social_link'
                customEvent={FooterEvent.social_click}
              />
            )
          })}
      </BoxContainer>
    </BoxContainer>
  )
}
