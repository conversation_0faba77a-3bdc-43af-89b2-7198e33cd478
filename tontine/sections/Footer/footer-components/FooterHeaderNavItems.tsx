import React from 'react'

import { BoxContainer } from '../../../components/common/BoxContainer'
import { NextLink } from '../../../components/common/NextLink'
import { LocalizedContentParser } from '../../../components/typography/LocalizedContentParser'
import { generateSlug } from '../../../helper-functions/UtilFunctions'
import { NavEvent } from '../../../types/Analytics/AnalyticsEvents.types'
import type { LocalizedStringContentType } from '../../../types/sections/section.types'
import type { SubMenuItem } from '../../../types/shared-page-data.types'

type MainMenuProps = {
  header?: LocalizedStringContentType
  subNavItems?: Array<SubMenuItem>
  headerSlug: string
}

/**
 * `FooterHeaderNavItems` is used to display headers and links in center of the footer
 */
export function FooterHeaderNavItems({
  header,
  subNavItems,
  headerSlug,
}: MainMenuProps) {
  // retrieve only links that are not coming soon
  const areAllLinksComingSoon = subNavItems?.filter(
    (item) => !item?.subMenuItemComingSoon
  )

  return (
    <>
      {areAllLinksComingSoon && areAllLinksComingSoon?.length > 0 && (
        <BoxContainer as={'ul'} className='flex flex-col text-center'>
          <BoxContainer
            as={'li'}
            className='mt-6 mb-2 font-semibold text-background-100/80 text-xl duration-200 hover:text-background-100 md:mt-0'
          >
            <NextLink
              href={headerSlug}
              objectId={'footer_main_link'}
              customEvent={NavEvent.item_clicked}
              customHoverEvent={NavEvent.item_hovered}
              trackHover
            >
              <LocalizedContentParser renderDefaultBlock={false}>
                {header}
              </LocalizedContentParser>
            </NextLink>
          </BoxContainer>
          {subNavItems?.map((item, index) => {
            const slug = generateSlug(item)

            if (!item?.subMenuItemDisabled) {
              return (
                <BoxContainer
                  as={'li'}
                  className='text-background-100/52 text-lg duration-200 hover:text-background-100'
                  key={`sub-menu-item-${slug}-${index}`}
                >
                  <NextLink
                    href={slug}
                    objectId='footer_sub_link'
                    customEvent={NavEvent.item_clicked}
                    customHoverEvent={NavEvent.item_hovered}
                    trackHover
                  >
                    <LocalizedContentParser renderDefaultBlock={false}>
                      {item?.title}
                    </LocalizedContentParser>
                  </NextLink>
                </BoxContainer>
              )
            }
            return (
              <React.Fragment key={`missing-links-in-menu-${slug}-${index}`} />
            )
          })}
        </BoxContainer>
      )}
    </>
  )
}
