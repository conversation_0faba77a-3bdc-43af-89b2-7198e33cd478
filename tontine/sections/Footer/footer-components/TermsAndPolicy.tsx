import { BoxContainer } from '../../../components/common/BoxContainer'
import { NextLink } from '../../../components/common/NextLink'
import { Title } from '../../../components/typography/Title'
import { generateSlug } from '../../../helper-functions/UtilFunctions'
import type { LinkCustomType } from '../../../types/common.types'
import type { ParentSectionID } from '../../../types/sections/section.types'

type TermsAndPolicyProps = {
  footerLinks?: Array<LinkCustomType>
} & ParentSectionID

/**
 * `TermsAndPolicy` is used to render the links for Website Terms and Privacy Policy in the footer
 */
export function TermsAndPolicy({
  footerLinks,
  parentSectionId,
}: TermsAndPolicyProps) {
  return (
    <BoxContainer className='flex flex-col md:flex-row md:gap-5'>
      {footerLinks?.map((item: LinkCustomType) => {
        const slug = generateSlug(item)
        return (
          <Title
            title={
              <NextLink href={slug} objectId={parentSectionId}>
                {item?.linkLabel}
              </NextLink>
            }
            key={`footerLawKeys-${slug}-${item?.linkLabel}`}
            className='mb-6 font-semibold text-background-100/70 text-xl hover:underline md:text-2xl'
          />
        )
      })}
    </BoxContainer>
  )
}
