import { NextLink } from '../../../components/common/NextLink'
import { SanityImage } from '../../../components/common/SanityImage'
import { NavEvent } from '../../../types/Analytics/AnalyticsEvents.types'
import type { ParentSectionID } from '../../../types/sections/section.types'

type FooterLogoProps = {
  alt?: string
  src: string
  href: string
} & ParentSectionID

/**
 * Footer<PERSON>ogo is used to render the company logo in footer
 * - `alt` alt prop is mandatory for SEO
 * - `src` the src of the image
 */
export function FooterLogo({
  alt,
  src,
  href,
  parentSectionId,
}: FooterLogoProps) {
  return (
    <NextLink
      href={href}
      objectId={parentSectionId}
      className='flex h-20 w-50 items-center justify-center'
      customEvent={NavEvent.item_clicked}
      customHoverEvent={NavEvent.item_hovered}
      trackHover
    >
      <SanityImage
        alt={alt}
        src={src}
        fillProp
        className='h-12 w-43'
        skeletonProps={{ className: 'w-43 h-12' }}
      />
    </NextLink>
  )
}
