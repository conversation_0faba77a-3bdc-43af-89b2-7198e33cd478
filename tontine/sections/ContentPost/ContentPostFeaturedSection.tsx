'use client'

import { BoxContainer } from '../../components/common/BoxContainer'
import { HeaderInfo } from '../../components/layouts/HeaderInfo'
import { generatePathWithTrailingSlash } from '../../helper-functions/UtilFunctions'
import { useHideContent } from '../../hooks/useHideContent'
import type { ContentPost } from '../../types/sections/content-section.types'
import type { SharedSectionDataType } from '../../types/sections/section.types'
import { ContentCard } from './ContentCard'

type ContentPostsFeaturedSectionProps = {
  parentSlug: string
  featuredPost: ContentPost
  handleClick: (slug: string) => void
} & Omit<SharedSectionDataType, 'slug'>

/** Renders a featured section in the content posts section. */
export function ContentPostFeaturedSection({
  title,
  subtitle,
  featuredPost,
  parentSlug,
  handleClick,
}: ContentPostsFeaturedSectionProps) {
  const hideContent = useHideContent()

  const featuredPostHref = generatePathWithTrailingSlash({
    segments: [parentSlug, featuredPost?.slug?.current],
    leadingSlash: true,
    endsWithSlash: true,
  })
  return (
    <BoxContainer
      as={'section'}
      aria-label={`${parentSlug ? `${parentSlug} header` : 'Info header'} `}
      className='flex flex-col gap-6 bg-grey-200 px-8 py-10'
    >
      <HeaderInfo
        title={title}
        className='mx-auto flex w-fit flex-col gap-3 md:mb-0 lg:w-[85%] xl:w-[80%] xl:items-start xl:justify-start'
        titleProps={{
          className: 'mx-0 md:text-left text-fg font-bold text-4xl',
        }}
        subtitle={subtitle}
        subtitleProps={{ className: 'text-grey-650 text-xl font-semibold' }}
      />
      <ContentCard
        post={featuredPost}
        isFeatured
        className='bg-background-100 content-section-width'
        onClick={() =>
          handleClick(
            generatePathWithTrailingSlash({
              segments: [parentSlug ?? '', featuredPost?.slug?.current],
              leadingSlash: true,
              endsWithSlash: true,
            })
          )
        }
        href={hideContent ? '#' : featuredPostHref}
        objectId={`${featuredPost?.slug?.current}_content_post`}
      />
    </BoxContainer>
  )
}
