import { BoxContainer } from '../../components/common/BoxContainer'
import { SanityImage } from '../../components/common/SanityImage'
import { ReactMuxVideo } from '../../components/mux-video/ReactMuxVideo'
import { Title } from '../../components/typography/Title'
import { Badge } from '../../components/ui/Badge'
import strings from '../../data-resource/strings.json'
import { isCypress } from '../../serverless/keys'
import type { ContentPostProps } from '../../types/sections/content-section.types'
import { AuthorList } from './AuthorList'
import { TimeAndDateLayout } from './TimeAndDateLayout'

/** The `ContentPostHeader` component renders the header section of a content post.
 * It displays the post's title, summary, authors, and other metadata such as
 * the post date and read time. If the post is featured, a badge is displayed.
 * Additionally, if a video ID is provided, a video player is rendered;
 * otherwise, it displays the post image. The component also supports rendering
 * children elements within the video player or below the header content.
 */
export function ContentPostHeader({
  postTitle,
  postSummary,
  authors,
  postImage,
  postDate,
  postReadTime,
  featuredPost,
  videoThumbnail,
  videoId,
  videoDuration,
  slug,
  parentSectionId,
  children,
}: ContentPostProps) {
  const videoMetadata = {
    title: postTitle,
    description: postSummary,
    image: videoThumbnail ?? postImage?.src,
    slug: slug ?? '',
    id: videoId ?? '',
    duration: videoDuration ?? 1,
  }
  return (
    <BoxContainer className='mx-auto flex flex-col gap-3 bg-gradient-to-b from-brand via-[75%] via-brand-400 to-[75%] to-bg pt-10 text-left content-block md:gap-5'>
      {postTitle && (
        <Title
          as='h1'
          className='font-bold text-3xl text-background-100 md:text-5xl'
        >
          {postTitle}
        </Title>
      )}
      {postSummary && (
        <Title
          as='h2'
          className='font-light text-background-100 text-lg leading-tight md:text-3xl'
          parentSectionId={parentSectionId}
        >
          {postSummary}
        </Title>
      )}

      <BoxContainer className='flex flex-wrap items-center gap-3 text-background-100'>
        {featuredPost && (
          <Badge
            variant='solid'
            colorPalette='unstyled'
            className='rounded-full bg-background-100 px-3 py-1.5 font-semibold text-brand text-sm shadow-sm'
          >
            {strings.FEATURED_ALL_CAPS}
          </Badge>
        )}
        <TimeAndDateLayout blockDate={postDate} duration={postReadTime} />
        <AuthorList
          authorArray={authors}
          className='flex flex-wrap text-l'
          authorProps={{ className: 'drop-shadow-none' }}
        />
      </BoxContainer>
      {!isCypress && videoId ? (
        <ReactMuxVideo
          className='overflow-hidden rounded-xl shadow-[0px_4px_10px_0px_rgba(0,_0,_0,_0.2)]'
          playbackId={videoId}
          videoMetadata={videoMetadata}
          videoThumbnail={videoThumbnail ?? postImage?.src}
        >
          {children}
        </ReactMuxVideo>
      ) : (
        children
      )}

      {!videoId && (
        <SanityImage
          skeletonProps={{
            className:
              'aspect-[1.5/1] rounded-md shadow-[0px_4px_10px_0px_rgba(0,_0,_0,_0.2)]',
          }}
          {...postImage}
          fillProp
          objectFitProp={'cover'}
          objectPositionProp={'center'}
          priority
        />
      )}
    </BoxContainer>
  )
}
