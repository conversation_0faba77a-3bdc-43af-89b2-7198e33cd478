import { BoxContainer } from '../../components/common/BoxContainer'
import { HideContent } from '../../components/common/HideContent'
import { NextLink } from '../../components/common/NextLink'
import { Title } from '../../components/typography/Title'
import { Divider } from '../../components/ui/Divider'
import strings from '../../data-resource/strings.json'
import { generatePathWithTrailingSlash } from '../../helper-functions/UtilFunctions'
import type { ContentPost } from '../../types/sections/content-section.types'
import { ContentCard } from './ContentCard'

type MoreLikeThisProps = {
  block: ContentPost
  moreLikeThis: Array<ContentPost>
  parentSlug: string
  postSlug: string
}
/**
 * Renders a "More Like This" section with a list of related articles, videos, or research items.
 * - `block` - The current block for which related items are displayed.
 * - `moreLikeThis` - The list of related items.
 */
export function MoreLikeThis({
  block,
  moreLikeThis,
  parentSlug,
  postSlug,
}: MoreLikeThisProps) {
  return (
    <HideContent>
      {block && (
        <BoxContainer
          aria-label='More like this.'
          as={'section'}
          className='max-w-[125rem] bg-grey-200 py-10'
        >
          <BoxContainer className='mx-auto mb-10 flex w-[90%] flex-col gap-6 sm:mb-12 sm:w-fit md:mb-20 md:w-2/3'>
            <BoxContainer className='flex items-end justify-between pt-5'>
              <Title
                title={strings.MORE_LIKE_THIS_TITLE}
                className='font-semibold text-2xl text-fg'
              />

              <NextLink
                href={generatePathWithTrailingSlash({
                  segments: [parentSlug],
                  leadingSlash: true,
                  endsWithSlash: true,
                })}
                objectId={'more_like_this_view_all'}
                customValue={postSlug}
                className='button transparent'
              >
                {strings.VIEW_ALL_BUTTON_LABEL}
              </NextLink>
            </BoxContainer>
            <Divider className='mb-3' />
            <BoxContainer className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2 lg:gap-8 xl:grid-cols-3'>
              {moreLikeThis &&
                moreLikeThis.length > 0 &&
                moreLikeThis.map((listArticle, index) => {
                  return (
                    <ContentCard
                      post={listArticle}
                      isMoreLikeThis
                      href={generatePathWithTrailingSlash({
                        segments: [parentSlug, listArticle?.slug?.current],
                        leadingSlash: true,
                        endsWithSlash: true,
                      })}
                      key={`more-like-${listArticle.slug.current}-${index}`}
                    />
                  )
                })}
            </BoxContainer>
          </BoxContainer>
        </BoxContainer>
      )}
    </HideContent>
  )
}
