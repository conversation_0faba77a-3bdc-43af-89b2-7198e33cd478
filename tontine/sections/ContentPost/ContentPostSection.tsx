'use client'

import { BoxContainer } from '../../components/common/BoxContainer'
import strings from '../../data-resource/strings.json'
import { generatePathWithTrailingSlash } from '../../helper-functions/UtilFunctions'
import { useHideContent } from '../../hooks/useHideContent'
import { myTontineLiteOrigin } from '../../providers/MTLOrigins'
import type { PageDomainType } from '../../types/common.types'
import type { ContentSectionType } from '../../types/sections/content-section.types'
import { ContentCard } from './ContentCard'
import { ContentPostFeaturedSection } from './ContentPostFeaturedSection'

import dynamic from 'next/dynamic'

const Webschema = dynamic(
  () =>
    import('../../components/web-schema/ContentPageGalleryWebschema').then(
      (mod) => mod.ContentPageGalleryWebschema
    ),
  {
    ssr: false,
  }
)

type ContentProps = {
  sectionData: ContentSectionType & {
    pageSlug?: string
  } & PageDomainType
}

/** Renders a section of content posts. This function is used across multiple pages
 * (e.g., /news, /videos, /research, etc.) and handles the rendering of the top part
 * of the card, which contains only the image.
 *
 * - **`sectionData`** - The data for the section. This includes:
 *   - `postsArray` - An array of article card items for the section.
 *   - `featuredPost` - The featured article card item.
 *   - `pageSlug` - The page slug indicating the current page section.
 *   - `pageDomain` - The domain of the page.
 */
export function ContentPostSection({ sectionData }: ContentProps) {
  const hideContent = useHideContent()

  const featuredPostData = sectionData?.featuredPost

  const webSchemaData = [
    featuredPostData,
    ...(sectionData?.postsArray ?? []),
  ]?.filter((post) => post !== undefined && post !== null)

  /**
   * Handles a click event on an article card by sending a message to the
   * parent window with the article's slug. Used by MyTontine Webapp News feed
   */
  const handleClick = (slug: string) => {
    return window?.parent?.postMessage(
      {
        source: myTontineLiteOrigin,
        payload: { eventId: 'ARTICLE_CLICKED', eventData: slug },
      },
      document?.referrer || window?.origin
    )
  }

  return (
    <>
      {sectionData?.postType !== strings.VIDEO_POST && (
        <Webschema
          webSchemaData={webSchemaData}
          pageSlug={sectionData?.pageSlug ?? ''}
          pageDomain={sectionData?.pageDomain}
        />
      )}
      {featuredPostData && (
        <ContentPostFeaturedSection
          handleClick={handleClick}
          title={sectionData?.title}
          subtitle={sectionData?.subtitle}
          featuredPost={featuredPostData}
          parentSlug={sectionData?.pageSlug ?? ''}
        />
      )}
      <BoxContainer
        as={'section'}
        aria-label={`${sectionData?.pageSlug ? `${sectionData?.pageSlug} section` : 'Main section'}`}
        id={sectionData?.pageSlug ?? 'main-section'}
        className='section grid grid-cols-1 xxl:grid-cols-3 gap-6 px-10 py-10 pb-20 content-section-width lg:grid-cols-2 lg:gap-8'
      >
        {sectionData?.postsArray?.map((post) => {
          const postHref = generatePathWithTrailingSlash({
            segments: [sectionData?.pageSlug ?? '', post?.slug?.current],
            leadingSlash: true,
            endsWithSlash: true,
          })
          return (
            <ContentCard
              post={post}
              onClick={() =>
                handleClick(
                  generatePathWithTrailingSlash({
                    segments: [
                      sectionData?.pageSlug ?? '',
                      post?.slug?.current,
                    ],
                    leadingSlash: true,
                    endsWithSlash: true,
                  })
                )
              }
              href={hideContent ? '#' : postHref}
              key={`${post.slug.current}`}
              objectId={`${post?.slug?.current}_content_post`}
            />
          )
        })}
      </BoxContainer>
    </>
  )
}
