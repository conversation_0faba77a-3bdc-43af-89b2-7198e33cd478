import { DownloadIcon } from '@sanity/icons'

import { BoxContainer } from '../../components/common/BoxContainer'
import { GenericButton } from '../../components/common/GenericButton'
import strings from '../../data-resource/strings.json'
import { ResearchEvent } from '../../types/Analytics/AnalyticsEvents.types'

type PdfEmbedProps = {
  researchPdfFile?: string
  postSlug: string
}

/**
 * The `PdfEmbed` displays the pdf dependent of the browser.
 * `DownloadPdfFileButton` is component that download the passed pdf file.
 * `researchPdfFile` pdf file that has been passed.
 */
export function PdfEmbed({ researchPdfFile, postSlug }: PdfEmbedProps) {
  return (
    <>
      {researchPdfFile && (
        <BoxContainer className='flex h-[80vh] flex-col items-center gap-10 bg-grey-300 px-8 py-12 lg:h-svh'>
          <iframe
            title={postSlug}
            src={researchPdfFile}
            className='mx-auto w-full grow overflow-y-auto border-none bg-background-100 smd:w-[90%]'
          />
          {researchPdfFile && (
            <a
              href={researchPdfFile}
              type='application/pdf'
              download={researchPdfFile}
              className='flex items-center justify-center'
            >
              <GenericButton
                label={strings.DOWNLOAD}
                icon={<DownloadIcon className='h-5 w-5' />}
                customEvent={ResearchEvent.download}
                objectValue={postSlug}
                className='button solid w-40 gap-2 py-4 text-4'
              />
            </a>
          )}
        </BoxContainer>
      )}
    </>
  )
}
