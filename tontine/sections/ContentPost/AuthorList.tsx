import { UserIcon, UsersIcon } from '@sanity/icons'

import type { BoxContainerProps } from '../../components/common/BoxContainer'
import { BoxContainer } from '../../components/common/BoxContainer'
import type { TitleProps } from '../../components/typography/Title'
import { Title } from '../../components/typography/Title'
import { cn } from '../../helper-functions/UtilFunctions'
import type { PersonType } from '../../types/common.types'

type AuthorListProps = {
  authorArray?: Array<PersonType>
  authorNameStyling?: object
  authorWrapperStyling?: object
  authorProps?: TitleProps
} & BoxContainerProps

/** Renders a list of authors with specified styling. */
export function AuthorList({
  authorArray,
  authorProps,
  ...rest
}: AuthorListProps) {
  if (!authorArray || authorArray.length === 0) {
    return <></>
  }
  return (
    <BoxContainer {...rest}>
      {authorArray.length > 1 ? (
        <UsersIcon fontSize='25' className='mr-1' />
      ) : (
        <UserIcon fontSize='25' />
      )}
      {authorArray.map(
        (author, index) =>
          author.personName && (
            <Title
              title={
                index !== authorArray.length - 1
                  ? `${author.personName},`
                  : author.personName
              }
              as='h5'
              key={`author-${author.id}`}
              {...authorProps}
              className={cn('mr-1', rest?.className)}
            />
          )
      )}
    </BoxContainer>
  )
}
