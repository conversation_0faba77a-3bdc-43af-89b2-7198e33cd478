import { BoxContainer } from '../../components/common/BoxContainer'
import { SanityImage } from '../../components/common/SanityImage'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { Title } from '../../components/typography/Title'
import { Badge } from '../../components/ui/Badge'
import { ClickableCard } from '../../components/ui/Card'
import strings from '../../data-resource/strings.json'
import {
  cn,
  convertToPlainText,
  timeAndDateProps,
} from '../../helper-functions/UtilFunctions'
import { STYLE } from '../../styles/style'
import { ContentPostEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { NextLinkProps } from '../../types/components/NextLink.types'
import type { ContentPost } from '../../types/sections/content-section.types'
import type { SharedSectionDataType } from '../../types/sections/section.types'
import { TimeAndDateLayout } from './TimeAndDateLayout'

type ContentCardProps = {
  post: ContentPost
  isFeatured?: boolean
  isMoreLikeThis?: boolean
} & Omit<NextLinkProps, 'children' | 'title'> &
  Omit<SharedSectionDataType, 'slug'>

export const ContentCard = ({
  post,
  isFeatured,
  isMoreLikeThis,
  ...rest
}: ContentCardProps) => {
  return (
    <ClickableCard
      {...rest}
      title={convertToPlainText({ value: post?.title })}
      className={cn(
        'relative flex flex-col overflow-hidden rounded-md shadow-smx duration-200 md:hover:scale-102 md:hover:bg-brand-50',
        isFeatured && 'p-3 md:p-4 xl:flex-row',
        rest.className
      )}
      trackHover
      customEvent={ContentPostEvent.clicked}
      customHoverEvent={ContentPostEvent.hovered}
    >
      <ClickableCard.Header className='relative inline-block'>
        <SanityImage
          skeletonProps={{
            className: cn(
              'h-[11.5rem] w-full sm:h-[16rem] md:h-[16rem] lg:h-[15rem]',
              isFeatured && 'aspect-[9/5] h-full rounded-sm md:aspect-[4/2]'
            ),
          }}
          sizes={STYLE.CONTENT_CARD_IMAGE_OPTIMIZATION}
          {...post?.postImage}
          fillProp
          objectFitProp='cover'
        />
        {Boolean(post?.videoFile) && (
          <BoxContainer className='video-play-button' />
        )}
      </ClickableCard.Header>
      <ClickableCard.Body
        className={cn(
          'flex w-full flex-col gap-1.5 py-2 text-fg md:p-2.5',
          !isFeatured && 'px-2'
        )}
      >
        <BoxContainer className='flex items-center justify-between'>
          <Title
            as='h3'
            className={cn(
              'font-semibold text-xl leading-5.5',
              isMoreLikeThis && 'line-clamp-1',
              isFeatured && 'text-3xl'
            )}
          >
            {post?.title}
          </Title>
        </BoxContainer>
        <LocalizedContentParser
          renderWrapper
          overrideElements={{
            p: {
              className: cn(
                'text-lg leading-5.5',
                isMoreLikeThis && 'line-clamp-2',
                isFeatured && 'text-xl leading-6 md:w-[80%]'
              ),
            },
          }}
        >
          {post?.subtitle}
        </LocalizedContentParser>

        {isFeatured && (
          <BoxContainer className='mt-auto flex flex-col-reverse gap-2 md:flex-row md:items-center md:justify-between'>
            <TimeAndDateLayout
              {...timeAndDateProps({
                postDate: post?.publishDate,
                article: post,
              })}
              textProps={{ className: 'opacity-90' }}
              iconProps={{ className: 'w-6 h-6 base:ml-0' }}
              className='mt-auto w-fit items-start gap-2 md:items-center'
            />
            <Badge
              variant='solid'
              colorPalette='unstyled'
              className='w-fit rounded-md bg-brand px-3 py-1.5 font-semibold text-background-100 shadow-sm md:text-lg'
            >
              {strings.FEATURED_ALL_CAPS}
            </Badge>
          </BoxContainer>
        )}
      </ClickableCard.Body>
      {!isFeatured && (
        <ClickableCard.Footer className='mt-auto flex h-fit items-center gap-1 px-2 py-1.5 text-fg/80'>
          <TimeAndDateLayout
            {...timeAndDateProps({
              postDate: post?.publishDate,
              article: post,
            })}
          />
        </ClickableCard.Footer>
      )}
    </ClickableCard>
  )
}
