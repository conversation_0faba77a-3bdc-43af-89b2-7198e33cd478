import { CalendarIcon, ClockIcon } from '@sanity/icons'

import type { BoxContainerProps } from '../../components/common/BoxContainer'
import { BoxContainer } from '../../components/common/BoxContainer'
import type { WrappedTextProps } from '../../components/typography/WrappedText'
import { WrappedText } from '../../components/typography/WrappedText'
import { cn } from '../../helper-functions/UtilFunctions'
import type { IconProps } from '../../types/component.types'

type TimeAndDateLayoutProps = {
  blockDate?: string
  blockReadTime?: number | string
  blockWatchTime?: number | string
  duration?: number | string
  iconProps?: IconProps
  textProps?: WrappedTextProps
} & Omit<BoxContainerProps, 'children'>

const iconStyling = 'w-7 h-7 relative'

const articleTextStyling = 'text-sm md:text-base'
/**
 * The `TimeAndDateLayout` is used to render date and time used in videos/research/article
 * - `blockDate` is the date when this block is published
 * - `blockReadTime` is rendering estimated time for a reader to read entire block.
 */
export function TimeAndDateLayout({
  blockDate,
  duration,
  iconProps,
  textProps,
  ...rest
}: TimeAndDateLayoutProps) {
  return (
    <BoxContainer
      {...rest}
      className={cn('flex items-center', rest?.className)}
    >
      {blockDate && (
        <BoxContainer className='flex items-center'>
          <CalendarIcon
            {...iconProps}
            className={cn(iconStyling, iconProps?.className)}
          />
          <WrappedText className={cn(articleTextStyling, textProps?.className)}>
            {blockDate}
          </WrappedText>
        </BoxContainer>
      )}
      {duration && (
        <BoxContainer className='flex items-center'>
          <ClockIcon
            {...iconProps}
            className={cn(
              iconStyling,
              blockDate && 'ml-3',
              iconProps?.className
            )}
          />
          <WrappedText className={cn(articleTextStyling, textProps?.className)}>
            {duration}
          </WrappedText>
        </BoxContainer>
      )}
    </BoxContainer>
  )
}
