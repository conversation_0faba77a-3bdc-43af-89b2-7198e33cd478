import { BoxContainer } from '../../components/common/BoxContainer'
import { HeaderInfo } from '../../components/layouts/HeaderInfo'
import type { SanityImageType } from '../../types/common.types'
import type { PartnerType } from '../../types/sections/partner-section.types'
import type { SharedSectionDataType } from '../../types/sections/section.types'
import { PartnerList } from './PartnerList'

type PartnersSectionProps = {
  sectionData: {
    partnersImage: SanityImageType
    partnersCompanies: Array<PartnerType>
  } & SharedSectionDataType
}

/**
 * `PartnersSection`, component that displays information about a partners, including the partners name, logo and information about every partner
 * - `partnersImage` icon that represents the partners
 * - `sectionSlug` value that gives this section an id
 * - `partnersSectionTitle` Name of the team like 'companies we work with'
 * - `partnersCompanies` Array of each partners and data for every partner.
 */
export function PartnersSection({ sectionData }: PartnersSectionProps) {
  return (
    <BoxContainer
      as={'section'}
      aria-label={sectionData?.slug?.current || 'Tontine Partners'}
      className='mx-auto grid w-fit scroll-mt-(--nav-height) place-content-center px-2 py-8'
      id={sectionData?.slug?.current}
    >
      <HeaderInfo title={sectionData?.title} />
      <PartnerList
        partners={sectionData?.partnersCompanies}
        parentSectionId={`${sectionData?.slug?.current}_partners_section`}
      />
    </BoxContainer>
  )
}
