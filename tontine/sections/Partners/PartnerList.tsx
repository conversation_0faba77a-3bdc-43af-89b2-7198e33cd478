import { BoxContainer } from '../../components/common/BoxContainer'
import type { PartnerType } from '../../types/sections/partner-section.types'
import type { ParentSectionID } from '../../types/sections/section.types'
import { Partner } from './Partner'

type PartnerListProp = {
  partners?: Array<PartnerType>
} & ParentSectionID

/** `PartnerList` renders a list of partners. It uses the Partner component to render each element from partners prop */
export function PartnerList({ partners, parentSectionId }: PartnerListProp) {
  return (
    <BoxContainer className='flex flex-wrap justify-center gap-6 px-2.5rem py-5 md:px-0'>
      {partners &&
        partners?.length > 0 &&
        partners?.map((partner) => (
          <Partner
            partner={partner}
            key={`memberId-${partner.stringTitle?.en}`}
            parentSectionId={parentSectionId}
          />
        ))}
    </BoxContainer>
  )
}
