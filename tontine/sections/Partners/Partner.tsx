import { BoxContainer } from '../../components/common/BoxContainer'
import { NextLink } from '../../components/common/NextLink'
import { SanityImage } from '../../components/common/SanityImage'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { Title } from '../../components/typography/Title'
import { Divider } from '../../components/ui/Divider'
import { STYLE } from '../../styles/style'
import type { PartnerType } from '../../types/sections/partner-section.types'
import type { ParentSectionID } from '../../types/sections/section.types'

type PartnerProps = {
  partner?: PartnerType
} & ParentSectionID

/** The 'Partner' is used to display data provided for partner */
export function Partner({ partner, parentSectionId }: PartnerProps) {
  return (
    <>
      {partner?.partnerImage && partner?.partnerExternalLink && (
        <NextLink
          href={partner?.partnerExternalLink}
          hideExternalIcon
          objectId={parentSectionId}
          className='max-w-[20rem] flex-grow rounded-md border border-brand-100 pb-2.5 text-left shadow-smx duration-200 hover:bg-brand-100 hover:shadow-sm md:w-1/2 lg:w-1/3'
        >
          <BoxContainer>
            <BoxContainer className='text-center'>
              <BoxContainer className='relative h-[11.7rem]'>
                <SanityImage
                  {...partner?.partnerImage}
                  fillProp
                  className='object-contain'
                  skeletonProps={{ className: 'h-full w-full' }}
                  sizes={STYLE.PARTNER_IMAGE_OPTIMIZATION}
                />
              </BoxContainer>
              <Title className='p-2.5 font-semibold text-grey-650 text-xl'>
                {partner?.stringTitle}
              </Title>
              <Divider className='mx-auto w-[80%]' />
              <LocalizedContentParser
                renderWrapper
                className='p-2.5 text-grey-650'
              >
                {partner?.subtitle}
              </LocalizedContentParser>
            </BoxContainer>
          </BoxContainer>
        </NextLink>
      )}
    </>
  )
}
