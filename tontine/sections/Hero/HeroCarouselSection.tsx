import { EmblaCarousel } from '../../components/embla-carousel/EmblaCarousel'
import { CONSTANTS } from '../../data-resource/constants'
import type { PageDomainType } from '../../types/common.types'
import type { CarouselHeroSectionType } from '../../types/sections/hero-types'
import type { SharedSectionDataType } from '../../types/sections/section.types'
import { HeroCarouselPost } from './HeroCarouselPost'

type CarouselHeroSectionProps = {
  sectionData: CarouselHeroSectionType & SharedSectionDataType
} & PageDomainType

/** A carousel section that renders a series of hero sections. */
export function HeroCarouselSection({
  sectionData,
  pageDomain,
}: CarouselHeroSectionProps) {
  return (
    <EmblaCarousel
      autoPlayOptions={{
        playOnInit: true,
        delay: CONSTANTS.CAROUSEL.DELAY.HERO,
        stopOnFocusIn: true,
      }}
      emblaWrapperProps={{
        as: 'section',
        'aria-label': sectionData?.slug?.current || 'Hero section',
        className: 'rounded-none',
      }}
      controlsWrapperProps={{
        className:
          'hidden md:flex items-center justify-center absolute h-full top-0 bottom-0 w-full pointer-events-none',
      }}
      showButtons
      showDots
      buttonStyling={{
        className:
          'pointer-events-auto p-2.5 button transparent alternate absolute my-auto z-2',
        nextButtonProps: {
          className: 'right-10',
        },
        prevButtonProps: {
          className: 'left-10',
        },
        arrowProps: {
          className: 'text-3xl w-7 h-7',
        },
      }}
      dotsStyling={{
        currentDotStyle: 'pointer-events-none bg-background-100 ring-0',
        dotProps: {
          className:
            'pointer-events-auto rounded-full w-4.5 h-4.5 p-0 outline-0 ring-1 ring-background-100/70 button transparent alternate',
        },
        dotsWrapperProps: {
          className: 'absolute bottom-5 flex gap-4 justify-center items-center',
        },
      }}
      options={{ loop: true, align: 'start' }}
      sectionContext='carousel_hero_section'
    >
      {sectionData?.carouseHeroSectionPosts?.map((post, index) => (
        <HeroCarouselPost
          pageDomain={pageDomain}
          isCarousel
          key={index}
          isPriority={index === 0}
          heroData={post}
        />
      )) ?? []}
    </EmblaCarousel>
  )
}
