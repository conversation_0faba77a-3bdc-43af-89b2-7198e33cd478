import type { PageDomainType, SanityButton } from '../../types/common.types'
import type {
  SectionMediaType,
  SharedSectionDataType,
} from '../../types/sections/section.types'
import { HeroPost } from './HeroPost'

type HeroSectionProps = {
  sectionData: {
    heroSectionButtons?: Array<SanityButton>
    heroVariants?: 'simple' | 'modern' | 'legacy'
  } & SharedSectionDataType &
    SectionMediaType
} & PageDomainType

export function HeroSection({ sectionData, pageDomain }: HeroSectionProps) {
  return (
    <HeroPost
      heroData={{
        slug: sectionData?.slug,
        title: sectionData?.title,
        subtitle: sectionData?.subtitle,
        buttons: sectionData?.heroSectionButtons,
        heroVariants: sectionData?.heroVariants,
        sectionImage: sectionData?.sectionImage,
        sectionVideo: sectionData?.sectionVideo,
        hideControls: sectionData?.hideControls,
        videoThumbnail: sectionData?.videoThumbnail,
      }}
      pageDomain={pageDomain}
    />
  )
}
