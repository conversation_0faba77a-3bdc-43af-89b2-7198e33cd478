import { EmblaSlide } from '../../components/embla-carousel/EmblaSlide'
import type { HeroPostProps } from '../../types/sections/hero-types'
import { HeroPost } from './HeroPost'

/** A carousel wrapper for HeroPost */
export const HeroCarouselPost = ({
  heroData,
  isCarousel,
  pageDomain,
  isPriority,
  ...rest
}: HeroPostProps) => {
  return (
    <EmblaSlide {...rest}>
      <HeroPost
        isPriority={isPriority}
        heroData={heroData}
        isCarousel={isCarousel}
        pageDomain={pageDomain}
      />
    </EmblaSlide>
  )
}
