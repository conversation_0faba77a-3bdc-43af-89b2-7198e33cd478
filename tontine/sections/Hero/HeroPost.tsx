import { BoxContainer } from '../../components/common/BoxContainer'
import { NextLink } from '../../components/common/NextLink'
import { SanityImage } from '../../components/common/SanityImage'
import { ReactMuxVideo } from '../../components/mux-video/ReactMuxVideo'
import { VideoSchema } from '../../components/mux-video/VideoSchema'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { Title } from '../../components/typography/Title'
import {
  cn,
  convertToPlainText,
  generateSlug,
} from '../../helper-functions/UtilFunctions'
import type { HeroPostProps } from '../../types/sections/hero-types'

/** The `HeroPost` component renders a hero section. */
export const HeroPost = ({
  heroData,
  isCarousel,
  isPriority = true,
  pageDomain,
}: HeroPostProps) => {
  const {
    slug,
    title,
    subtitle,
    buttons,
    heroVariants = 'legacy',
    sectionImage,
    sectionVideo,
    hideControls,
    videoThumbnail,
    ...rest
  } = heroData
  const isModern = heroVariants === 'modern'
  const isSimple = heroVariants === 'simple'
  const isLegacy = heroVariants === 'legacy'

  const heroGradientStyling = 'before:inset-0 before:absolute before:-z-1'
  const legacyGradientStyling = cn(
    heroGradientStyling,
    'before:bg-gradient-to-br before:from-[rgba(35,35,35,0.69)] before:to-transparent'
  )

  return (
    <BoxContainer
      as={isCarousel ? 'div' : 'section'}
      id={slug?.current}
      {...rest}
      className={cn(
        'relative z-0 flex min-h-[35rem] flex-col justify-center py-10 text-center',
        isModern && 'bg-brand-100',
        isSimple && 'bg-background',
        isLegacy && 'items-center md:items-start md:text-left',
        isLegacy && legacyGradientStyling,
        !isLegacy && !isCarousel && 'px-8',
        rest?.className
      )}
    >
      {isLegacy && (
        <SanityImage
          skeletonProps={{
            className: 'absolute w-full h-full -z-2',
          }}
          priority={isPriority}
          fillProp
          objectFitProp='cover'
          {...sectionImage}
        />
      )}

      <BoxContainer
        className={cn(
          'mx-auto mb-2 flex flex-col items-center gap-4 lg:px-4',
          isLegacy &&
            'px-4 font-bold text-white md:w-[70%] md:items-start md:px-0',
          !isLegacy && 'justify-center font-black'
        )}
      >
        <Title
          as='h1'
          className={cn(
            'text-white md:max-w-[20ch]',
            isLegacy &&
              'mx-auto w-full font-semibold text-3xl leading-7 drop-shadow-md sm:leading-9 md:mx-0 md:max-w-[30ch] md:text-4xl md:leading-10 lg:w-[80%] lg:text-5xl lg:leading-12',
            !isLegacy &&
              '!leading-10 sm:!leading-12 md:!leading-14 lg:!leading-22 xl:!leading-25 font-black text-4xl uppercase sm:text-5xl md:text-6xl lg:text-8xl',
            isSimple && 'px-10 text-fg',
            isModern && 'text-brand'
          )}
        >
          {title}
        </Title>

        <Title
          as='h2'
          className={cn(
            'w-full sm:w-[80%] md:w-[60%]',
            'font-semibold text-white text-xl lg:text-2xl',
            isLegacy && 'drop-shadow-md',
            !isLegacy && 'font-light md:text-xl lg:text-2xl xl:leading-9',
            isSimple && 'text-fg',
            isModern && 'text-brand'
          )}
        >
          {subtitle}
        </Title>

        {buttons && (
          <BoxContainer className='mt-3 flex flex-wrap justify-center gap-4'>
            {buttons?.map((button, index) => {
              const buttonSlug = generateSlug(button)
              return (
                <NextLink
                  key={`hero-button-${convertToPlainText({ value: button.buttonLabel })}`}
                  href={buttonSlug}
                  objectId={`${slug?.current}_${isCarousel ? 'carousel_hero_section' : 'hero_section'}`}
                  className={cn(
                    'button min-w-40 text-sm transition-all md:min-w-60 md:text-lg',
                    !isLegacy && 'hover:scale-101',
                    !isLegacy && index < 1 && 'solid',
                    !isLegacy && index > 0 && 'solid inverted',
                    isLegacy && 'transparent alternate py-2 md:min-w-55'
                  )}
                >
                  <LocalizedContentParser>
                    {button.buttonLabel}
                  </LocalizedContentParser>
                </NextLink>
              )
            })}
          </BoxContainer>
        )}
        {!isLegacy && sectionImage && (
          <SanityImage
            {...sectionImage}
            fillProp
            skeletonProps={{
              className:
                'mt-4 w-full h-full max-w-55 md:max-w-full md:w-120 md:h-120 aspect-square',
            }}
          />
        )}
        {sectionVideo && (
          <BoxContainer className='mt-4 h-full w-full max-w-[80rem] overflow-hidden rounded-[1rem] shadow-smx sm:w-[80%] md:w-[60%]'>
            <ReactMuxVideo
              hideControls={hideControls}
              videoThumbnail={videoThumbnail?.src ?? ''}
              playbackId={sectionVideo.playbackId}
            >
              <VideoSchema
                title={title}
                subtitle={subtitle}
                videoThumbnail={videoThumbnail}
                video={sectionVideo}
                pageDomain={pageDomain}
              />
            </ReactMuxVideo>
          </BoxContainer>
        )}
      </BoxContainer>
    </BoxContainer>
  )
}
