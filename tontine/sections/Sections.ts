import { AboutUsSection } from './AboutUs/AboutUsSection'
import { CTACard } from './CTA/CTACard'
import { CtaSection } from './CTA/CtaSection'
import { CompaniesLogosSection } from './CompaniesLogosSection'
import { ContactUsSection } from './ContactUsSection'
import { ContentPostSection } from './ContentPost/ContentPostSection'
import { DownloadSection } from './DownloadSection'
import { FaqSection } from './Faq/FaqSection'
import { FeaturedSection } from './Featured/FeaturedSection'
import { GlossarySection } from './GlossarySection'
import { HeroCarouselSection } from './Hero/HeroCarouselSection'
import { HeroSection } from './Hero/HeroSection'
import { InfoBlockSection } from './Info/InfoBlockSection'
import { InfoHubSection } from './Info/InfoHubSection'
import { MarkdownSection } from './MarkdownSection'
import { ReferralSection } from './MyTontine/ReferralSection'
import { TontinatorSection } from './MyTontine/TontinatorSection'
import { PartnersSection } from './Partners/PartnersSection'
import { TeamSection } from './Team/TeamSection'
import { CarouselSection } from './TestimonialCarousel/TestimonialCarouselSection'
import { TestimonialSection } from './Testimonials/TestimonialSection'
import { VideosSection } from './VideosSection'
import { WalkthroughSection } from './Walkthrough/WalkthroughSection'
import { InfoBannerSection } from './infoBannerSection'

/**
 * This is the list of all the sections that are available in the CMS.
 * The key is the name of the section in the CMS.
 * The value must be the component that will be used to render the section.
 * The 'export default' on components must be used to make the sections available for use.
 * The key must match the name of the section in the CMS, name comes from the _type property of the section.
 * The received props in the component section but match the name of the fields in the CMS.
 */
export const sections = {
  ctaSection: CtaSection,
  featuredSection: FeaturedSection,
  downloadSection: DownloadSection,
  faqSection: FaqSection,
  aboutUsSection: AboutUsSection,
  teamSection: TeamSection,
  partnersSection: PartnersSection,
  infoBlockSection: InfoBlockSection,
  infoHubSection: InfoHubSection,
  carouselSection: CarouselSection,
  contactUsSection: ContactUsSection,
  companiesLogosSection: CompaniesLogosSection,
  infoBannerSection: InfoBannerSection,
  markdownSection: MarkdownSection,
  heroSection: HeroSection,
  carouselHeroSection: HeroCarouselSection,
  testimonialSection: TestimonialSection,
  contentSection: ContentPostSection,
  tontinatorSection: TontinatorSection,
  referralSection: ReferralSection,
  ctaCard: CTACard,
  walkthroughSection: WalkthroughSection,
  glossarySection: GlossarySection,
  videosSection: VideosSection,
}
