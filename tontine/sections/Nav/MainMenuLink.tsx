import { ChevronDownIcon } from '@sanity/icons'

import { NextLink } from '../../components/common/NextLink'
import { Title } from '../../components/typography/Title'
import { NavEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { LocalizedStringHeader } from '../../types/sections/section.types'
import type { SubMenuItem } from '../../types/shared-page-data.types'

type MainMenuLinkProps = {
  href?: string
  navigationSubMenuItems?: Array<SubMenuItem>
} & Omit<LocalizedStringHeader, 'stringSubtitle'>

/**
 * MainMenuLink is used to render the main navbar link
 * - `title` Title of the link
 * - `href` Link to the page
 * - `navigationSubMenuItems` Sub menu items of the link
 * - `isOpen` Boolean to check if the sub menu is open
 */
export function MainMenuLink({
  stringTitle,
  href,
  navigationSubMenuItems,
}: MainMenuLinkProps) {
  return (
    <NextLink
      href={href || '#'}
      className='flex h-fit items-center gap-0.5 text-grey-550 hover:text-brand group-hover/parent:text-brand'
      customHoverEvent={NavEvent.item_hovered}
      role='link'
      customEvent={NavEvent.item_clicked}
      trackHover
    >
      <Title>{stringTitle}</Title>
      {navigationSubMenuItems && navigationSubMenuItems?.length > 0 && (
        <ChevronDownIcon className='mt-0.5 h-6 w-6 transition-transform duration-200 group-hover/parent:rotate-180 group-focus/parent:rotate-180' />
      )}
    </NextLink>
  )
}
