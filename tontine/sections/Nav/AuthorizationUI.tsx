'use client'

import {
  <PERSON>Container,
  type BoxContainerProps,
} from '../../components/common/BoxContainer'
import { GenericButton } from '../../components/common/GenericButton'
import { NextLink } from '../../components/common/NextLink'
import strings from '../../data-resource/strings.json'
import { cn } from '../../helper-functions/UtilFunctions'
import { useAuth } from '../../providers/AuthContext'
import type { SlugType } from '../../types/common.types'

const defaultButtonStyling =
  'button active:opacity-85 flex rounded-sm cursor-pointer px-0 py-1 text-[1.2rem] font-normal hover:opacity-85 xl:px-4 xl:py-1.5 xl:text-[1.1rem]'

function AuthorizationUI({
  className,
  onToggle,
  loginLink,
  registerLink,
  ...rest
}: BoxContainerProps & {
  onToggle?: () => void
  loginLink?: SlugType
  registerLink?: SlugType
}) {
  const { isAuth, logout } = useAuth()

  return (
    <BoxContainer
      className={cn('flex items-center gap-4 pb-3 lg:gap-3 xl:pb-0', className)}
      {...rest}
    >
      {!isAuth ? (
        <>
          <NextLink
            objectId='my_tontine_log_in'
            href={loginLink?.current ?? strings.REFERRAL_LINK}
            tabIndex={-1}
            className={cn(
              'text-grey-550 xl:bg-grey-200 xl:ring xl:ring-gray-200',
              defaultButtonStyling
            )}
            onClick={onToggle}
          >
            {strings.LOG_IN}
          </NextLink>
          <NextLink
            href={registerLink?.current ?? strings.TONTINATOR_LINK}
            objectId='my_tontine_sign_up'
            className={cn(
              'text-brand xl:bg-brand xl:text-background-100',
              defaultButtonStyling
            )}
            onClick={onToggle}
          >
            {strings.SIGN_UP}
          </NextLink>
        </>
      ) : (
        <GenericButton
          onClick={() => {
            logout?.()
            onToggle?.()
          }}
          className={cn('red light', defaultButtonStyling)}
          objectId='my_tontine_log_out'
        >
          {strings.LOG_OUT}
        </GenericButton>
      )}
    </BoxContainer>
  )
}

export { AuthorizationUI }
