import { CloseIcon, MenuIcon } from '@sanity/icons'

import { BoxContainer } from '../../components/common/BoxContainer'
import { GenericButton } from '../../components/common/GenericButton'
import { Collapsible } from '../../components/ui/collapse/Collapse'
import strings from '../../data-resource/strings.json'
import { generateSlug } from '../../helper-functions/UtilFunctions'
import { NavEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type {
  NavBarNavigationMenuItem,
  NavigationMenuDataProps,
} from '../../types/shared-page-data.types'
import { FeedbackModal } from '../Feedback/FeedbackModal'
import { MobileNavItem } from './MobileNavItem'

import dynamic from 'next/dynamic'

const AuthorizationUI = dynamic(
  () => import('./AuthorizationUI').then((mod) => mod.AuthorizationUI),
  {
    ssr: false,
  }
)

/** `MenuToggleButton` is a component that renders a button for toggling
 * the mobile navigation menu. It displays an icon and manages the
 * button's click event, which triggers a custom analytics event.
 */
const MenuToggleButton = ({
  isOpen,
  onClick,
}: {
  isOpen?: boolean
  onClick?: () => void
}) => {
  return (
    <GenericButton
      onClick={onClick}
      objectId={isOpen ? 'nav_bar_close' : 'nav_bar_open'}
      customEvent={
        isOpen ? NavEvent.mobile_collapsed : NavEvent.mobile_expanded
      }
      label={strings.MENU}
      icon={
        isOpen ? (
          <CloseIcon className='h-5 w-5' />
        ) : (
          <MenuIcon className='h-5 w-5' />
        )
      }
      className='flex items-center justify-center gap-1.5 rounded-sm bg-grey-200 py-1.5 pr-2 pl-3 text-grey-650 text-sm ring ring-gray-200 duration-300 hover:bg-transparent lg:py-2.5 lg:pr-3 lg:pl-4'
      aria-label='Toggle Navigation'
      aria-expanded={isOpen}
    />
  )
}

/**
 * MobileNav is used to show the navigation menu on phone-sized screens.
 */
export const MobileNav = ({
  navigationMenuData,
  feedbackData,
  inProp,
  onToggle,
}: NavigationMenuDataProps) => {
  return (
    <>
      <Collapsible
        customTrigger={{
          isOpen: Boolean(inProp),
          toggle: onToggle,
          trigger: <MenuToggleButton isOpen={inProp} onClick={onToggle} />,
        }}
        contentProps={{
          contentInnerProps: {
            className:
              'bg-background-100 h-(--nav-height-offset) flex flex-col',
          },
          className: 'top-full bg-background-100 absolute left-0 w-full',
        }}
      >
        <BoxContainer className='flex h-[calc(100%-110px)] flex-col gap-4 overflow-y-auto'>
          {navigationMenuData?.navigationItems?.map(
            (navItem: NavBarNavigationMenuItem) => {
              const slug = generateSlug({
                pageSlug: navItem?.pageSlug,
                customLink: navItem?.customLink,
                sectionSlug: navItem?.sectionSlug,
                linkType: navItem?.linkType,
                customParamsButton: navItem?.customParamsButton,
                customParams: navItem?.customParams,
              })
              return (
                <MobileNavItem
                  key={`navItemKey-${slug}`}
                  {...navItem}
                  navItemSlug={slug}
                  onToggle={onToggle}
                />
              )
            }
          )}
        </BoxContainer>

        {!navigationMenuData?.hideAuthButtons && (
          <AuthorizationUI
            onToggle={onToggle}
            loginLink={navigationMenuData?.loginLink}
            registerLink={navigationMenuData?.registerLink}
            className='mt-auto mr-auto flex min-h-[50px] w-full border-t-1 border-t-brand-100 bg-brand-50 pb-0 pl-6'
          />
        )}
        <FeedbackModal feedbackModalData={feedbackData} />
      </Collapsible>
    </>
  )
}
