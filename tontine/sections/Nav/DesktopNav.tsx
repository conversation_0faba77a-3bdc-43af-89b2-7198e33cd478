import { Fragment } from 'react'

import { BoxContainer } from '../../components/common/BoxContainer'
import { Popover } from '../../components/ui/Popover'
import { cn, generateSlug } from '../../helper-functions/UtilFunctions'
import type {
  NavBarNavigationMenuItem,
  NavigationMenuDataProps,
} from '../../types/shared-page-data.types'
import { DesktopSubNav } from './DesktopSubNav'
import { MainMenuLink } from './MainMenuLink'

/** DesktopNav is used to display the navigation menu on desktop */
export function DesktopNav({
  navigationMenuData,
  languagePickerVisible,
}: NavigationMenuDataProps & { languagePickerVisible?: boolean }) {
  return (
    <>
      {navigationMenuData?.navigationItems &&
        navigationMenuData?.navigationItems?.length > 0 && (
          <BoxContainer
            as={'ul'}
            className={cn(
              'relative flex gap-5 text-lg xxl:text-xl',
              languagePickerVisible && 'xxl:ml-auto'
            )}
            role='list'
          >
            {navigationMenuData?.navigationItems?.map(
              (navItem: NavBarNavigationMenuItem, index: number) => {
                const slug = generateSlug(navItem)
                const isFirst = index === 0

                const offsetX = cn('xxl:ml-8', isFirst && 'ml-27')
                return (
                  <Fragment key={slug}>
                    {navItem?.navigationSubMenuItems &&
                    navItem?.navigationSubMenuItems?.length > 0 ? (
                      <Popover
                        key={`${slug}-popover`}
                        as='li'
                        className={'group/parent'}
                        placement='center'
                        role='listitem'
                        offsetX={offsetX}
                        tabIndex={-1}
                        trigger={
                          <MainMenuLink
                            href={slug}
                            stringTitle={navItem?.stringTitle}
                            navigationSubMenuItems={
                              navItem?.navigationSubMenuItems
                            }
                          />
                        }
                      >
                        <BoxContainer
                          key={slug}
                          as={'ul'}
                          aria-label={
                            navItem?.stringTitle?.en ?? 'Navigation Menu'
                          }
                          role='list'
                          className='nav-box-shadow z-110 mt-4 grid w-fit min-w-[40rem] grid-cols-(--repeat-2) gap-3 rounded-md bg-background-100 p-5 transition-opacity duration-200'
                        >
                          {navItem?.navigationSubMenuItems?.map(
                            (subMenuItem, subIndex) => {
                              const subSlug = generateSlug(subMenuItem)
                              return (
                                <DesktopSubNav
                                  key={`popover-subMenuItem-${subSlug}-${subIndex}`}
                                  {...subMenuItem}
                                  subSlug={subSlug}
                                />
                              )
                            }
                          )}
                        </BoxContainer>
                      </Popover>
                    ) : (
                      <BoxContainer as='li' role='listitem'>
                        <MainMenuLink
                          href={slug}
                          stringTitle={navItem?.stringTitle}
                          navigationSubMenuItems={
                            navItem?.navigationSubMenuItems
                          }
                        />
                      </BoxContainer>
                    )}
                  </Fragment>
                )
              }
            )}
          </BoxContainer>
        )}
    </>
  )
}
