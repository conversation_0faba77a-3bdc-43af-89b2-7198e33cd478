'use client'

import { ChevronUpIcon } from '@sanity/icons'
import { captureException } from '@sentry/nextjs'
import { isCancel } from 'axios'
import { usePathname } from 'next/navigation'
import { useState } from 'react'

import { sendFeedback } from '../../app/api/feedback'
import { GenericButton } from '../../components/common/GenericButton'
import { HideContent } from '../../components/common/HideContent'
import { NextLink } from '../../components/common/NextLink'
import { SanityImage } from '../../components/common/SanityImage'
import { NextTurnstile } from '../../components/turnstile/Turnstile'
import { FormContainer } from '../../components/typography/FormContainer'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { TextArea } from '../../components/typography/TextArea'
import { WrappedText } from '../../components/typography/WrappedText'
import { Dialog } from '../../components/ui/Dialog'
import { Skeleton } from '../../components/ui/skeleton/Skeleton'
import { CONSTANTS } from '../../data-resource/constants'
import { UI_TEST_ID } from '../../data-resource/data-test-ids'
import images from '../../data-resource/images.json'
import strings from '../../data-resource/strings.json'
import {
  cn,
  convertToPlainText,
  getAttribute,
} from '../../helper-functions/UtilFunctions'
import { useBreakpointValue } from '../../hooks/useBreakpointValue'
import { useToastHook } from '../../hooks/useToast'
import { useLanguage } from '../../providers/LanguageContext'
import { isCypress, isProd } from '../../serverless/keys'
import { STYLE } from '../../styles/style'
import { FeedbackEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { SharedPageData } from '../../types/shared-page-data.types'
import { RatingButtons } from './FeedbackRatingButtons'

const commonButtonStyling = cn(
  'w-full rounded-full py-2 font-semibold text-base duration-200 hover:opacity-70 md:text-xl'
)

/** Custom hook to handle feedback submission with turnstile verification. */
function useFeedback({
  onSuccessfulSubmit,
  feedbackModalData,
}: {
  feedbackModalData?: SharedPageData['websiteFeedbackModal']
  onSuccessfulSubmit?: () => void
}) {
  const { showToast } = useToastHook()

  const { language } = useLanguage()

  const [rating, setRating] = useState(0)
  const [loading, setLoading] = useState(!isCypress)
  const [text, setText] = useState<string | undefined>()
  const [suspiciousActivity, setSuspiciousActivity] = useState(false)

  const submitFeedback = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (rating) {
      try {
        setLoading(true)

        if (!isProd && !isCypress) {
          throw new Error('Not on prod')
        }

        const formData = new FormData(e.currentTarget)
        const token = formData.get('cf-turnstile-response') ?? ''

        await sendFeedback({
          rating,
          text,
          token,
        })

        showToast({
          title:
            convertToPlainText({
              value: feedbackModalData?.successfulFeedbackTitle,
              language,
            }) ?? 'Success!',
          description: convertToPlainText({
            value: feedbackModalData?.successfulFeedbackDescription,
            language,
          }),
          href: CONSTANTS.TONTINE_COMMUNITY_FORUM,
          type: 'success',
        })

        onSuccessfulSubmit?.()
      } catch (error) {
        if (!isCancel(error)) {
          captureException(error)
        }
        showToast({
          title: isProd
            ? (convertToPlainText({
                value: feedbackModalData?.failedFeedbackTitle,
                language,
              }) ?? '')
            : strings.FORM_STAGING_ERROR_TITLE,
          description: isProd
            ? convertToPlainText({
                value: feedbackModalData?.failedFeedbackDescription,
                language,
              })
            : strings.FORM_STAGING_ERROR_DESCRIPTION,
          type: 'error',
        })
        setSuspiciousActivity(true)
      } finally {
        setLoading(false)
      }
    }
  }

  return {
    rating,
    text,
    loaded: true,
    suspiciousActivity,
    setSuspiciousActivity,
    loading,
    setLoading,
    setRating,
    setText,
    submitFeedback,
  }
}

/**
 * A component that renders a user feedback modal. The modal includes a rating system,
 * a text area for additional feedback, and buttons for submission and redirection.
 * **Text feedback is optional.**
 */
export function FeedbackModal({
  feedbackModalData,
  hideMobile,
  onSuccessfulSubmit,
}: {
  feedbackModalData: SharedPageData['websiteFeedbackModal']
  hideMobile?: boolean
  onSuccessfulSubmit?: () => void
}) {
  const [dialogRef, setDialogRef] = useState<HTMLDialogElement>()

  const { isBreakpoint: isDesktop, isLoading } = useBreakpointValue(['xl'])

  const showCard = !(hideMobile && !isDesktop)

  const setDialog = (ref: HTMLDialogElement) => {
    if (ref) setDialogRef(ref)
  }
  const successfulSubmit = () => {
    onSuccessfulSubmit?.()
    dialogRef?.close()
  }

  const {
    rating,
    text,
    loading,
    suspiciousActivity,
    setSuspiciousActivity,
    setRating,
    setLoading,
    setText,
    submitFeedback,
    loaded,
  } = useFeedback({
    feedbackModalData,
    onSuccessfulSubmit: successfulSubmit,
  })
  const location = usePathname()
  return (
    <HideContent>
      {!isLoading &&
        showCard &&
        feedbackModalData &&
        !feedbackModalData?.excludePages?.includes(location.slice(1, -1)) && (
          <Dialog
            setRef={setDialog}
            title={feedbackModalData?.feedbackModalTitle}
            triggerButtonProps={{
              className: cn(
                'xl:-right-7 xl:vertical-text hover:xl:-right-2 sticky top-[15rem] xxl:top-[calc(100vh-48%)] mt-auto flex h-fit min-h-[50px] w-full flex-row-reverse items-center justify-between gap-2 rounded-none bg-jade px-5 text-lg transition-[right] duration-500 ease-in-out active:opacity-90 md:mt-[unset] md:py-0 lg:min-h-[unset] xl:fixed xl:top-[calc(100vh-50%)] xl:bottom-0 xl:my-auto xl:w-auto xl:flex-row xl:rounded-md xl:p-[.25rem_2.25rem_.25rem_.25rem] xl:shadow-md',
                STYLE.Z_INDEX.FEEDBACK
              ),
              objectId: 'feedback_modal_open',
              objectValue: location ?? '/',
              dataTestId: UI_TEST_ID?.feedbackCard,
              children: (
                <>
                  <ChevronUpIcon className='xl:-rotate-90 h-10 w-10 text-background-100' />
                  <WrappedText className='xl:-rotate-180 font-semibold text-background-100'>
                    <LocalizedContentParser>
                      {feedbackModalData?.ctaButtonLabel}
                    </LocalizedContentParser>
                  </WrappedText>
                  <SanityImage
                    skeletonProps={{
                      className: 'hidden xl:block w-6 h-6 mx-2 mt-2.5',
                    }}
                    src={images.SMILEY_EMOJI_WHITE_OUTLINE}
                    fillProp
                  />
                </>
              ),
            }}
            dialogProps={{
              className:
                'p-2 md:p-4 rounded-md bg-brand-50 shadow-md opacity-start duration-400',
              'data-cy': UI_TEST_ID?.feedbackModal,
            }}
            headerProps={{
              className:
                'flex items-start md:items-center justify-between text-lg leading-5 md:leading-[inherit] md:text-xl text-fg-700 gap-2 md:gap-6',
            }}
            closeButtonProps={{
              className:
                'button transparent p-1 rounded-sm aspect-square mt-0.5',
              iconProps: {
                className: 'w-5 h-5',
              },
            }}
          >
            <FormContainer onSubmitProp={submitFeedback}>
              <Skeleton
                className='flex flex-col gap-4 overflow-visible'
                loading={!loaded}
              >
                <RatingButtons
                  lowestRatingFeedbackText={feedbackModalData?.lowestRatingText}
                  highestRatingFeedbackText={
                    feedbackModalData?.highestRatingText
                  }
                  disableButtons={suspiciousActivity}
                  currentRating={rating}
                  setRating={setRating}
                />
                {Boolean(rating) && (
                  <TextArea
                    fieldProps={{
                      className: 'w-full ',
                    }}
                    value={text ?? ''}
                    className={cn(
                      'size-lg min-h-[10rem] w-full resize-none rounded-md border border-grey-450 bg-background-100 p-4 text-[1.1rem] text-dark-grey placeholder:text-gray-muted focus:outline-brand-200',
                      suspiciousActivity ? 'pointer-events-none opacity-50' : ''
                    )}
                    placeholder={feedbackModalData?.textPlaceholder?.en}
                    inputOnChange={(e) => setText(e.target.value)}
                    disabled={suspiciousActivity}
                    dataTestId={UI_TEST_ID?.feedbackText}
                    objectId='feedback_modal_text_area'
                    trackInput
                  />
                )}

                {Boolean(rating) &&
                  (suspiciousActivity ? (
                    <WrappedText className='text-center text-[1.05rem] md:text-[1.1rem] lg:text-[1.2rem]'>
                      {strings.SUSPICIOUS_BOT_ACTIVITY_FEEDBACK_PART1}
                      <NextLink
                        href={CONSTANTS.TONTINE_COMMUNITY_FORUM}
                        className='text-brand-550 hover:underline'
                      >
                        {strings.CLICK_HERE}
                      </NextLink>
                      {strings.SUSPICIOUS_BOT_ACTIVITY_FEEDBACK_PART2}
                    </WrappedText>
                  ) : (
                    <>
                      <NextTurnstile
                        onError={() => setSuspiciousActivity(true)}
                        onSuccess={() => setLoading(false)}
                        onExpire={() => setSuspiciousActivity(true)}
                        onTimeout={() => setSuspiciousActivity(true)}
                      />
                      <GenericButton
                        className={cn(
                          commonButtonStyling,
                          'bg-jade text-background-100'
                        )}
                        customEvent={FeedbackEvent.submit}
                        objectId={'feedback_modal_submit'}
                        isLoading={loading}
                        objectValue={{
                          rating,
                          ...getAttribute(Boolean(text), 'text', text),
                        }}
                        dataTestId={UI_TEST_ID?.feedbackSubmitButton}
                        type='submit'
                      >
                        <LocalizedContentParser>
                          {feedbackModalData?.submitButtonLabel}
                        </LocalizedContentParser>
                      </GenericButton>

                      <NextLink
                        href={CONSTANTS.TONTINE_COMMUNITY_FORUM}
                        objectId='feedback_modal_redirect'
                        dataTestId={UI_TEST_ID?.feedbackRedirectButton}
                        className={cn(
                          commonButtonStyling,
                          'button transparent w-full'
                        )}
                      >
                        <LocalizedContentParser>
                          {feedbackModalData?.redirectButtonLabel}
                        </LocalizedContentParser>
                      </NextLink>
                    </>
                  ))}
              </Skeleton>
            </FormContainer>
          </Dialog>
        )}
    </HideContent>
  )
}
