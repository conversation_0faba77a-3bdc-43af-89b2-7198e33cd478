import { BoxContainer } from '../../components/common/BoxContainer'
import { GenericButton } from '../../components/common/GenericButton'
import { CONSTANTS } from '../../data-resource/constants'
import { UI_TEST_ID } from '../../data-resource/data-test-ids'
import { cn } from '../../helper-functions/UtilFunctions'
import type { LocalizedStringContentType } from '../../types/sections/section.types'
import { Descriptors } from './FeedbackDescriptors'

/**
 * A component that renders a rating system with buttons and descriptors for feedback.
 */
export function RatingButtons({
  lowestRatingFeedbackText,
  highestRatingFeedbackText,
  disableButtons,
  currentRating,
  setRating,
}: {
  lowestRatingFeedbackText?: LocalizedStringContentType
  highestRatingFeedbackText?: LocalizedStringContentType
  disableButtons: boolean
  currentRating: number
  setRating: (rating: number) => void
}) {
  return (
    <BoxContainer
      className={cn(
        'mb-3 flex w-full flex-col items-center',
        disableButtons && 'pointer-events-none opacity-50'
      )}
    >
      <BoxContainer className='my-4 flex h-8 w-full justify-center gap-1 sm:justify-between sm:gap-1 md:h-10 md:gap-2 lg:gap-2'>
        {Array.from({ length: CONSTANTS.NET_PROMOTER_SCORE }, (_, index) => {
          const ratingIndex = index + 1
          const isActive = currentRating === ratingIndex
          return (
            <GenericButton
              key={`feedback-rating-button-${ratingIndex}`}
              className={cn(
                'h-10 w-[40px] max-w-[45px] flex-grow overflow-visible rounded-sm border border-brand-350 bg-background-100 px-0 duration-200 hover:bg-brand-100 md:h-12 md:w-full xl:text-[1.3rem]',
                isActive &&
                  'pointer-events-none scale-110 overflow-visible border-none bg-brand text-background-100 shadow-md filter-none'
              )}
              onClick={() => setRating(ratingIndex)}
              objectValue={ratingIndex.toString()}
              objectId='feedback_modal_rate'
              dataTestId={UI_TEST_ID?.feedbackRating}
              disabled={disableButtons || isActive}
            >
              {ratingIndex}
            </GenericButton>
          )
        })}
      </BoxContainer>

      <Descriptors
        lowestRatingFeedbackText={lowestRatingFeedbackText}
        highestRatingFeedbackText={highestRatingFeedbackText}
      />
    </BoxContainer>
  )
}
