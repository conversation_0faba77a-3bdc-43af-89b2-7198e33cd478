import { useState } from 'react'

import { BoxContainer } from '../../components/common/BoxContainer'
import { GenericButton } from '../../components/common/GenericButton'
import { WrappedText } from '../../components/typography/WrappedText'
import { UI_TEST_ID } from '../../data-resource/data-test-ids'
import strings from '../../data-resource/strings.json'
import { cn } from '../../helper-functions/UtilFunctions'

/**
 * A thumbs up icon component that can be toggled between a selected and unselected state.
 */
function ThumbsUpIcon({ selected = false }: { selected: boolean }) {
  return (
    <svg
      width='21'
      height='22'
      viewBox='0 0 28 26'
      fill={'currentColor'}
      xmlns='http://www.w3.org/2000/svg'
    >
      {selected ? (
        <path d='M28.75 12.5C28.75 11.837 28.4866 11.2011 28.0178 10.7322C27.5489 10.2634 26.913 10 26.25 10H18.35L19.55 4.2875C19.575 4.1625 19.5875 4.025 19.5875 3.8875C19.5875 3.375 19.375 2.9 19.0375 2.5625L17.7125 1.25L9.4875 9.475C9.025 9.9375 8.75 10.5625 8.75 11.25V23.75C8.75 24.413 9.01339 25.0489 9.48223 25.5178C9.95107 25.9866 10.587 26.25 11.25 26.25H22.5C23.5375 26.25 24.425 25.625 24.8 24.725L28.575 15.9125C28.6875 15.625 28.75 15.325 28.75 15V12.5ZM1.25 26.25H6.25V11.25H1.25V26.25Z' />
      ) : (
        <path d='M5.25 10.25V25.25H0.25V10.25H5.25ZM10.25 25.25C9.58696 25.25 8.95107 24.9866 8.48223 24.5178C8.01339 24.0489 7.75 23.413 7.75 22.75V10.25C7.75 9.5625 8.025 8.9375 8.4875 8.4875L16.7125 0.25L18.0375 1.575C18.375 1.9125 18.5875 2.375 18.5875 2.8875L18.55 3.2875L17.3625 9H25.25C25.913 9 26.5489 9.26339 27.0178 9.73223C27.4866 10.2011 27.75 10.837 27.75 11.5V14C27.75 14.325 27.6875 14.625 27.575 14.9125L23.8 23.725C23.425 24.625 22.5375 25.25 21.5 25.25H10.25ZM10.25 22.75H21.5375L25.25 14V11.5H14.2625L15.675 4.85L10.25 10.2875V22.75Z' />
      )}
    </svg>
  )
}
/**
 * A thumbs down icon component that can be toggled between a selected and unselected state.
 */
function ThumbsDownIcon({ selected = false }: { selected: boolean }) {
  return (
    <svg
      width='21'
      height='22'
      viewBox='0 0 28 26'
      fill={'currentColor'}
      xmlns='http://www.w3.org/2000/svg'
    >
      {selected ? (
        <path d='M22.75 15.75H27.75V0.75H22.75M17.75 0.75H6.5C5.4625 0.75 4.575 1.375 4.2 2.275L0.425 11.0875C0.3125 11.375 0.25 11.675 0.25 12V14.5C0.25 15.163 0.513392 15.7989 0.982233 16.2678C1.45107 16.7366 2.08696 17 2.75 17H10.6375L9.45 22.7125C9.425 22.8375 9.4125 22.9625 9.4125 23.1C9.4125 23.625 9.625 24.0875 9.9625 24.425L11.2875 25.75L19.5125 17.5125C19.975 17.0625 20.25 16.4375 20.25 15.75V3.25C20.25 2.58696 19.9866 1.95107 19.5178 1.48223C19.0489 1.01339 18.413 0.75 17.75 0.75Z' />
      ) : (
        <path d='M22.75 15.75V0.75H27.75V15.75H22.75ZM17.75 0.75C18.413 0.75 19.0489 1.01339 19.5178 1.48223C19.9866 1.95107 20.25 2.58696 20.25 3.25V15.75C20.25 16.4375 19.975 17.0625 19.5125 17.5125L11.2875 25.75L9.9625 24.425C9.625 24.0875 9.4125 23.625 9.4125 23.1L9.45 22.7125L10.6375 17H2.75C2.08696 17 1.45107 16.7366 0.982233 16.2678C0.513392 15.7989 0.25 15.163 0.25 14.5V12C0.25 11.675 0.3125 11.375 0.425 11.0875L4.2 2.275C4.575 1.375 5.4625 0.75 6.5 0.75H17.75ZM17.75 3.25H6.4625L2.75 12V14.5H13.725L12.3125 21.15L17.75 15.7125V3.25Z' />
      )}
    </svg>
  )
}

const ratingButtonStyling =
  'flex overflow-visible p-2 gap-1 user-select-none data-active:filter-none disabled:not-data-active:opacity-70'

/**
 * RatingButtons is a component that displays a text and pair of buttons for the user to provide feedback,
 * a thumbs up and a thumbs down button.
 */
export function ThumbsRatingButtons({
  questionData,
}: {
  questionData: object
}) {
  const [rate, setRate] = useState('')
  const [disabledState, setDisabledState] = useState(false)

  /**
   * Toggles the state of the rating buttons and sets the disabled state to the selected one
   */
  const handleDisable = (feedback: string) => {
    setDisabledState(!disabledState)
    setRate(feedback)
  }

  return (
    <BoxContainer className='mt-3 flex w-full items-center justify-between'>
      <WrappedText className='font-semibold'>
        {strings.FAQ_RATING_TEXT}
      </WrappedText>
      <BoxContainer className='flex gap-0.5'>
        <GenericButton
          dataTestId={UI_TEST_ID.faqFeedback}
          objectValue={questionData}
          isActive={rate === 'positive'}
          className={cn(
            ratingButtonStyling,
            'hover:text-brand data-active:text-brand'
          )}
          objectId={'faq_section_rate_positive'}
          onClick={() => handleDisable('positive')}
          disabled={disabledState}
        >
          <ThumbsUpIcon selected={rate === 'positive'} />
          <WrappedText>{strings.YES}</WrappedText>
        </GenericButton>
        <GenericButton
          dataTestId={UI_TEST_ID.faqFeedback}
          objectValue={questionData}
          isActive={rate === 'negative'}
          className={cn(
            ratingButtonStyling,
            'hover:text-error-red data-active:text-error-red'
          )}
          objectId={'faq_section_rate_negative'}
          onClick={() => handleDisable('negative')}
          disabled={disabledState}
        >
          <ThumbsDownIcon selected={rate === 'negative'} />
          <WrappedText>{strings.NO}</WrappedText>
        </GenericButton>
      </BoxContainer>
    </BoxContainer>
  )
}
