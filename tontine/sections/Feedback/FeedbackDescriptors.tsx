import { BoxContainer } from '../../components/common/BoxContainer'
import { SanityImage } from '../../components/common/SanityImage'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { WrappedText } from '../../components/typography/WrappedText'
import images from '../../data-resource/images.json'
import type { LocalizedStringContentType } from '../../types/sections/section.types'

/**
 * A component that renders a two scale descriptors with the lowest and highest rating feedback text.
 */
export function Descriptors({
  lowestRatingFeedbackText,
  highestRatingFeedbackText,
}: {
  lowestRatingFeedbackText?: LocalizedStringContentType
  highestRatingFeedbackText?: LocalizedStringContentType
}) {
  return (
    <BoxContainer className='flex w-full justify-between'>
      <BoxContainer className='flex items-center gap-2'>
        <SanityImage
          src={images.NOT_LIKELY_EMOJI}
          skeletonProps={{ className: 'w-[20px] h-[20px]' }}
          fillProp
        />
        <WrappedText>
          <LocalizedContentParser>
            {lowestRatingFeedbackText}
          </LocalizedContentParser>
        </WrappedText>
      </BoxContainer>
      <BoxContainer className='flex items-center gap-2'>
        <SanityImage
          src={images.MOST_LIKELY_EMOJI}
          skeletonProps={{ className: 'w-[20px] h-[20px]' }}
          fillProp
        />
        <WrappedText>
          <LocalizedContentParser>
            {highestRatingFeedbackText}
          </LocalizedContentParser>
        </WrappedText>
      </BoxContainer>
    </BoxContainer>
  )
}
