import { BoxContainer } from '../../components/common/BoxContainer'
import { HeaderInfo } from '../../components/layouts/HeaderInfo'
import {
  renameTestimonialFields,
  splitAndArrangeArrayIntoColumns,
} from '../../helper-functions/UtilFunctions'
import type {
  SharedSectionDataType,
  TestimonialType,
} from '../../types/sections/section.types'
import { TestimonialCard } from './TestimonialCard'

type TestimonialSectionProps = {
  sectionData: {
    testimonialPosts: Array<TestimonialType>
  } & SharedSectionDataType
}

/**
 * `Testimonial Sections` renders a Title and a NestedFlexLayout of Testimonials that simulated grid behavior
 */
export const TestimonialSection = ({
  sectionData,
}: TestimonialSectionProps) => {
  if (!sectionData) return null

  const { slug, title, testimonialPosts } = sectionData
  const formattedTestimonials = renameTestimonialFields(testimonialPosts)
  const renderColumns = (columns: number) =>
    splitAndArrangeArrayIntoColumns(formattedTestimonials, columns).map(
      (customCardColumns, index) => (
        <BoxContainer key={index} className='flex flex-col gap-6'>
          {customCardColumns.map((card, cardKey) => (
            <TestimonialCard key={cardKey} slug={slug} profile={card} />
          ))}
        </BoxContainer>
      )
    )

  return (
    <BoxContainer
      as='section'
      aria-label={slug?.current || 'Testimonials'}
      id={slug?.current}
      className='flex max-w-[125rem] flex-col items-center justify-center gap-8 bg-grey-200 px-8 pt-6 pb-16'
    >
      <HeaderInfo title={title} className='w-full py-6' />
      <BoxContainer className='xxl:flex hidden flex-row justify-center gap-6'>
        {renderColumns(3)}
      </BoxContainer>
      <BoxContainer className='flex xxl:hidden flex-wrap justify-center gap-6'>
        {renderColumns(2)}
      </BoxContainer>
    </BoxContainer>
  )
}
