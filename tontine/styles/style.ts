import { cn } from '../helper-functions/UtilFunctions'
import type { BreakpointsType } from '../types/common.types'

const STYLE = {
  WALKTHROUGH_GRID_SIZE: {
    DESKTOP: {
      COLUMNS: 60, // 960px image width
      ROWS: 35, // 560px image height
      CELL_SIZE: { base: '.5rem', xl: '.7rem', xml: '.8rem', xxl: '1rem' },
    },
    MOBILE: {
      COLUMNS: 20, // 320px image width
      ROWS: 40, // 640px image height
      CELL_SIZE: { base: '.9rem' },
    },
  },
  MARKDOWN_COLOR_CODES: {
    cerulean: '#2975bf',
    gold: '#ffcc04',
    darkGrey: '#4d5155',
    red: '#e53e3e',
    green: '##22b573',
  },
  Z_INDEX: {
    TOASTER: 'z-101',
    DIALOG_BACKDROP: 'z-100',
    NAV_BAR: 'z-99',
    FEEDBACK: 'z-98',
    INFO_BANNER: 'z-22',
    CTA_CARD: 'z-21',
    SKELETON: 'z-20',
  },
  FEATURED_CARD_IMAGE_OPTIMIZATION:
    '(max-width: 480px) 300px, (max-width: 768px) 420px, (max-width: 1304px) 550px, 700px',
  DOWNLOAD_SECTION_IMAGE_OPTIMIZATION:
    '(max-width: 480px) 210px, (max-width: 768px) 250px, (max-width: 1304px) 350px, 500px',
  FEATURED_SECTION_IMAGE_OPTIMIZATION:
    '(max-width: 480px) 300px, (max-width: 768px) 420px, (max-width: 1350px) 550px, 700px',
  PERSON_TEAM_SECTION_IMAGE_OPTIMIZATION:
    '(max-width: 480px) 280px, (max-width: 768px) 360px, (max-width: 1024px) 390px, 390px',
  PARTNER_IMAGE_OPTIMIZATION:
    '(max-width: 480px) 130px, (max-width: 768px) 130px, (max-width: 1024px) 250px, 350px',
  TESTIMONIAL_IMAGE_OPTIMIZATION:
    '(max-width: 480px) 100px, (max-width: 768px) 100px, (max-width: 1024px) 230px, 300px',
  ICON_IMAGE_OPTIMIZATION:
    '(max-width: 480px) 24px, (max-width: 768px) 32px, (max-width: 1024px) 40px, 48px',
  SMALL_ICON_IMAGE_OPTIMIZATION:
    '(max-width: 480px) 16px, (max-width: 768px) 20px, (max-width: 1024px) 24px, 32px',
  LARGE_ICON_IMAGE_OPTIMIZATION:
    '(max-width: 480px) 32px, (max-width: 768px) 48px, (max-width: 1024px) 64px, 80px',
  AVATAR_IMAGE_OPTIMIZATION:
    '(max-width: 480px) 40px, (max-width: 768px) 48px, (max-width: 1024px) 56px, 64px',
  THUMBNAIL_IMAGE_OPTIMIZATION:
    '(max-width: 480px) 150px, (max-width: 768px) 200px, (max-width: 1024px) 250px, 300px',
  CONTENT_CARD_IMAGE_OPTIMIZATION:
    '(max-width: 480px) 280px, (max-width: 768px) 320px, (max-width: 1024px) 400px, 480px',
} as const

const getGridStyles = ({
  columns,
  rows,
  rowSize = '1fr',
  columnSize = '1fr',
  cellSize,
}: {
  rows?: number
  columns?: number
  rowSize?: string
  columnSize?: string
  cellSize?: Partial<Record<BreakpointsType, string>>
}) => {
  const baseClasses = ['grid']

  // Handle non-breakpoint grids
  if (!cellSize) {
    if (rows) baseClasses.push(`grid-rows-[repeat(${rows},${rowSize})]`)
    if (columns)
      baseClasses.push(`grid-cols-[repeat(${columns},${columnSize})]`)
    return cn(...baseClasses, rows)
  }

  // Handle breakpoint version
  const breakpoints = Object.keys(cellSize) as Array<Partial<BreakpointsType>>

  const responsiveClasses = breakpoints.flatMap((bp) => {
    const size = cellSize[bp]
    const prefix = bp === 'base' ? '' : `${bp}:`

    return [
      rows && `${prefix}grid-rows-[repeat(${rows},${size})]`,
      columns && `${prefix}grid-cols-[repeat(${columns},${size})]`,
    ].filter(Boolean)
  })

  return cn(...baseClasses, ...responsiveClasses)
}

export { getGridStyles, STYLE }
