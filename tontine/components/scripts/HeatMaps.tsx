import Script from 'next/script'

import { heatMapsId } from '../../serverless/keys'

export function HeatMaps() {
  return (
    <>
      <Script
        strategy='afterInteractive'
        id='smartlook-Heatmaps'
        type='text/javascript'
        // biome-ignore lint/security/noDangerouslySetInnerHtml: <Need to do this>
        dangerouslySetInnerHTML={{
          __html: `
          window.smartlook||(function(d) {
            var o=smartlook=function(){ o.api.push(arguments)},h=d.getElementsByTagName('head')[0];
            var c=d.createElement('script');o.api=new Array();c.async=true;c.type='text/javascript';
            c.charset='utf-8';c.src='https://web-sdk.smartlook.com/recorder.js';h.appendChild(c);
            })(document);
            smartlook('init', '${heatMapsId}', { region: 'eu' });
        `,
        }}
      />
    </>
  )
}
