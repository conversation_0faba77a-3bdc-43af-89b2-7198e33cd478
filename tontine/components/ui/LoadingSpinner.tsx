import { SpinnerIcon } from '@sanity/icons'

import { cn } from '../../helper-functions/UtilFunctions'
import type { IconProps } from '../../types/component.types'
import type { BoxContainerProps } from '../common/BoxContainer'
import { BoxContainer } from '../common/BoxContainer'

export const LoadingSpinner = ({
  iconProps,
  ...rest
}: { iconProps?: IconProps } & BoxContainerProps) => {
  return (
    <BoxContainer
      className={cn(
        'pointer-events-none absolute inset-0 z-50 flex items-center justify-center gap-1 bg-gray-400 opacity-100',
        rest?.className
      )}
      {...rest}
    >
      <SpinnerIcon
        className={cn('h-5 w-5 animate-spin', iconProps?.className)}
      />
    </BoxContainer>
  )
}
