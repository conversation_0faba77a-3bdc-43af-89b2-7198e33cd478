import { useState } from 'react'

import { cn, getPlacementClasses } from '../../helper-functions/UtilFunctions'
import type { ComponentPlacement } from '../../types/component.types'
import { BoxContainer, type BoxContainerProps } from '../common/BoxContainer'

type PopoverProps = {
  trigger: React.ReactNode
  innerWrapperClass?: string
} & BoxContainerProps &
  ComponentPlacement

export const Popover = ({
  children,
  trigger,
  className,
  innerWrapperClass,
  placement = 'center',
  offsetY,
  offsetX,
  ...rest
}: PopoverProps) => {
  const [isVisible, setIsVisible] = useState(false)

  return (
    <BoxContainer
      className={cn('relative inline-block', className)}
      onMouseOver={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
      onFocus={() => setIsVisible(true)}
      onBlur={() => setIsVisible(false)}
      {...rest}
    >
      {trigger}
      <BoxContainer
        className={cn(
          'absolute z-999 transition-opacity duration-200',
          getPlacementClasses({ placement, offsetY, offsetX }),
          isVisible ? 'opacity-100' : 'pointer-events-none opacity-0',
          innerWrapperClass
        )}
        tabIndex={-1}
      >
        {children}
      </BoxContainer>
    </BoxContainer>
  )
}
