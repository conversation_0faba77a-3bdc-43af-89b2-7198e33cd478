import {
  cn,
  convertToPlainText,
  timeAndDateProps,
} from '../../helper-functions/UtilFunctions'
import { TimeAndDateLayout } from '../../sections/ContentPost/TimeAndDateLayout'
import type { ContentPost } from '../../types/sections/content-section.types'
import { BoxContainer } from '../common/BoxContainer'
import { ReactMuxVideo } from '../mux-video/ReactMuxVideo'
import { Title } from '../typography/Title'
import { Card } from './Card'

type VideoCardProps = {
  video: ContentPost
  index: number
  isPlaying: boolean
  setPlayingIndex: React.Dispatch<React.SetStateAction<number | null>>
}

/**
 * The `VideoCard` component renders a card for a video, with a title, publish date, and video player.
 * The component supports playing and pausing the video, and it also provides a way to display a
 * "play" button when the video is not playing.
 */
export const VideoCard = ({
  video,
  index,
  isPlaying,
  setPlayingIndex,
}: VideoCardProps) => {
  return (
    <Card
      key={`reels-${video.slug.current}-${index}`}
      aria-label={`Video: ${convertToPlainText({ value: video.title })}, ${isPlaying ? 'playing' : 'paused'}`}
      className=' min-h-85'
    >
      <Card.Header
        className={cn(
          'hover:-translate-y-2 group relative scale-100 overflow-hidden rounded-lg shadow-md transition-all duration-150 hover:scale-105 hover:shadow-lg',
          isPlaying ? '-translate-y-2 scale-105 shadow-2xl' : 'scale-100'
        )}
      >
        <ReactMuxVideo
          isPlaying={isPlaying}
          playbackId={video?.videoFile?.playbackId}
          videoThumbnail={video?.videoThumbnail?.src ?? video?.postImage?.src}
          className={'overflow-hidden rounded-lg shadow-xl'}
          onPlay={() => setPlayingIndex(index)}
          onPause={() => setPlayingIndex(null)}
        />

        <BoxContainer
          className={cn(
            'video-play-button pointer-events-none transition-opacity duration-150',
            isPlaying ? 'opacity-0' : 'opacity-50 group-hover:opacity-100'
          )}
        />
      </Card.Header>
      <Card.Body className='mt-2'>
        <Title className='mb-1 font-semibold text-fg text-xl'>
          {video?.title}
        </Title>
      </Card.Body>
      <Card.Footer className='mt-auto'>
        <TimeAndDateLayout
          {...timeAndDateProps({
            postDate: video?.publishDate,
            article: video,
          })}
          iconProps={{
            className: 'w-6 h-6 base:-ml-1',
          }}
          className='w-fit items-start gap-2 text-fg opacity-80 md:items-center'
        />
      </Card.Footer>
    </Card>
  )
}
