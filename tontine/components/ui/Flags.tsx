import type { ReactNode, SVGProps } from 'react'

/** A component that renders an SVG with a circular mask for the flag.
 * The flag is displayed within a circle shape based on the provided language and children.
 * The mask ensures that the contents are clipped to a circular shape. */
const CircleFlagBase = ({
  lang,
  children,
  ...props
}: SVGProps<SVGSVGElement> & { lang: string; children: ReactNode }) => {
  const maskId = `circleFlagsMask-${lang}`

  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='22'
      height='22'
      viewBox='0 0 512 512'
      {...props}
    >
      <mask id={maskId}>
        <circle cx='256' cy='256' r='256' fill='#fff' />
      </mask>
      <g mask={`url(#${maskId})`}>{children}</g>
    </svg>
  )
}

/** A record that holds the JSX elements representing different country flags. */
const flagContents: Record<string, ReactNode> = {
  en: (
    <>
      <path
        fill='#eee'
        d='M256 0L0 256v64l32 32l-32 32v128l22-8l23 8h23l54-32l54 32h32l48-32l48 32h32l54-32l54 32h68l-8-22l8-23v-23l-32-54l32-54v-32l-32-48l32-48v-32l-32-54l32-54V0z'
      />
      <path
        fill='#d80027'
        d='M224 64v64h160l64-64zm0 128l32 64l-48 48v208h96V304h208v-96H304l16-16zM0 320v64h128l-64 64H0v64h45l131-131v-45l16-16zm336 16l176 176v-45L381 336Z'
      />
      <path
        fill='#0052b4'
        d='M0 0v256h256V0zm512 68L404 176h108zM404 336l108 108V336zm-228 68L68 512h108zm160 0v108h108z'
      />
      <path
        fill='#eee'
        d='m187 243l57-41h-70l57 41l-22-67zm-81 0l57-41H93l57 41l-22-67zm-81 0l57-41H12l57 41l-22-67zm162-81l57-41h-70l57 41l-22-67zm-81 0l57-41H93l57 41l-22-67zm-81 0l57-41H12l57 41l-22-67Zm162-82l57-41h-70l57 41l-22-67zm-81 0l57-41H93l57 41l-22-67Zm-81 0l57-41H12l57 41l-22-67Z'
      />
    </>
  ),
  es: (
    <>
      <path fill='#ffda44' d='m0 128l256-32l256 32v256l-256 32L0 384Z' />
      <path fill='#d80027' d='M0 0h512v128H0zm0 384h512v128H0z' />
      <g fill='#eee'>
        <path d='M144 304h-16v-80h16zm128 0h16v-80h-16z' />
        <ellipse cx='208' cy='296' rx='48' ry='32' />
      </g>
      <g fill='#d80027'>
        <rect width='16' height='24' x='128' y='192' rx='8' />
        <rect width='16' height='24' x='272' y='192' rx='8' />
        <path d='M208 272v24a24 24 0 0 0 24 24a24 24 0 0 0 24-24v-24h-24z' />
      </g>
      <rect width='32' height='16' x='120' y='208' fill='#ff9811' ry='8' />
      <rect width='32' height='16' x='264' y='208' fill='#ff9811' ry='8' />
      <rect width='32' height='16' x='120' y='304' fill='#ff9811' rx='8' />
      <rect width='32' height='16' x='264' y='304' fill='#ff9811' rx='8' />
      <path
        fill='#ff9811'
        d='M160 272v24c0 8 4 14 9 19l5-6l5 10a21 21 0 0 0 10 0l5-10l5 6c6-5 9-11 9-19v-24h-9l-5 8l-5-8h-10l-5 8l-5-8z'
      />
      <path
        fill='#d80027'
        d='M122 248a4 4 0 0 0-4 4a4 4 0 0 0 4 4h172a4 4 0 0 0 4-4a4 4 0 0 0-4-4zm0 24a4 4 0 0 0-4 4a4 4 0 0 0 4 4h28a4 4 0 0 0 4-4a4 4 0 0 0-4-4zm144 0a4 4 0 0 0-4 4a4 4 0 0 0 4 4h28a4 4 0 0 0 4-4a4 4 0 0 0-4-4z'
      />
      <path
        fill='#eee'
        d='M196 168c-7 0-13 5-15 11l-5-1c-9 0-16 7-16 16s7 16 16 16c7 0 13-4 15-11a16 16 0 0 0 17-4a16 16 0 0 0 17 4a16 16 0 1 0 10-20a16 16 0 0 0-27-5q-4.5-6-12-6m0 8c5 0 8 4 8 8c0 5-3 8-8 8c-4 0-8-3-8-8c0-4 4-8 8-8m24 0c5 0 8 4 8 8c0 5-3 8-8 8c-4 0-8-3-8-8c0-4 4-8 8-8m-44 10l4 1l4 8c0 4-4 7-8 7s-8-3-8-8c0-4 4-8 8-8m64 0c5 0 8 4 8 8c0 5-3 8-8 8c-4 0-8-3-8-7l4-8z'
      />
      <path fill='none' d='M220 284v12c0 7 5 12 12 12s12-5 12-12v-12z' />
      <path fill='#ff9811' d='M200 160h16v32h-16z' />
      <path fill='#eee' d='M208 224h48v48h-48z' />
      <path
        fill='#d80027'
        d='m248 208l-8 8h-64l-8-8c0-13 18-24 40-24s40 11 40 24m-88 16h48v48h-48z'
      />
      <rect
        width='20'
        height='32'
        x='222'
        y='232'
        fill='#d80027'
        rx='10'
        ry='10'
      />
      <path
        fill='#ff9811'
        d='M168 232v8h8v16h-8v8h32v-8h-8v-16h8v-8zm8-16h64v8h-64z'
      />
      <g fill='#ffda44'>
        <circle cx='186' cy='202' r='6' />
        <circle cx='208' cy='202' r='6' />
        <circle cx='230' cy='202' r='6' />
      </g>
      <path
        fill='#d80027'
        d='M169 272v43a24 24 0 0 0 10 4v-47zm20 0v47a24 24 0 0 0 10-4v-43z'
      />
      <g fill='#338af3'>
        <circle cx='208' cy='272' r='16' />
        <rect width='32' height='16' x='264' y='320' ry='8' />
        <rect width='32' height='16' x='120' y='320' ry='8' />
      </g>
    </>
  ),
  pt: (
    <>
      <path fill='#6da544' d='M512 0v512H0l224-288Z' />
      <path fill='#ffda44' d='m346 166l-122 58l-58 122l90 66l212-156Z' />
      <path
        fill='#0052b4'
        d='m319 193l-95 31l-31 95a89 89 0 0 0 136-12l14-34a89 89 0 0 0-24-80'
      />
      <path
        fill='#eee'
        d='M255 257c29 9 55 26 74 50c7-10 12-22 14-34c-17-18-38-32-62-42l-57-7Z'
      />
      <path fill='#fff' d='M0 512L512 0H0Z' />
      <path fill='#496e2d' d='M0 0v512l167-167l32-172L167 0Z' />
      <path fill='#d80027' d='M167 0v167l89 89L512 0Z' />
      <path
        fill='#ffda44'
        d='M167 167a89 89 0 0 0-89 89a89 89 0 0 0 89 89l89-89a89 89 0 0 0-89-89'
      />
      <path fill='#d80027' d='M117 212v55a50 50 0 1 0 100 0v-56H117Z' />
      <path fill='#eee' d='M167 284c-9 0-17-8-17-17v-22h34v22c0 9-8 17-17 17' />
    </>
  ),
}

/** A component that renders a circle flag based on the selected language.
 * The flag contents uses a set of flags, with a English as default. */
export function CircleFlag({
  lang = 'en',
  ...props
}: SVGProps<SVGSVGElement> & { lang?: string }) {
  return (
    <CircleFlagBase lang={lang} {...props}>
      {flagContents[lang]}
    </CircleFlagBase>
  )
}
