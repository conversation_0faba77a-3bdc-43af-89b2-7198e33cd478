import { cn, isLoadedWindow } from '../../../helper-functions/UtilFunctions'
import { Select } from './Select'

/** WebsiteIdSelect component for rendering a dropdown for selecting a website ID. */
export const WebsiteIdSelect = ({
  websiteId,
  websiteTitles,
  setWebsiteId,
}: {
  websiteId: string
  websiteTitles: Array<{
    cleanedTitle: string
    id: string
    title: string
  }>
  setWebsiteId: (newWebsiteId: string) => void
}) => {
  const handleWebsiteSelect = (selectedWebsiteId: string) => {
    setWebsiteId(selectedWebsiteId)
    if (isLoadedWindow()) {
      window.location.reload()
    }
  }

  return (
    <Select
      clearable={false}
      className={cn(
        'absolute top-25 right-5.5 w-40 border border-brand-350 opacity-70 transition-all hover:opacity-100',
        '-z-1'
      )}
      placeholder='Select domain'
      defaultOption={{
        value: websiteId,
        label:
          websiteTitles?.find((website) => website?.id === websiteId)
            ?.cleanedTitle ?? '',
      }}
      setSelectedValue={handleWebsiteSelect}
      options={websiteTitles?.map((website) => {
        return {
          value: website?.id,
          label: website?.cleanedTitle,
        }
      })}
    />
  )
}
