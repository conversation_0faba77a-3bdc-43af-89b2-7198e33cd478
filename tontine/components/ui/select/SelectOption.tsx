import { NextLink } from '../../../components/common/NextLink'
import { cn } from '../../../helper-functions/UtilFunctions'
import type { SelectOptionProps } from '../../../types/components/Select.types'
import { BoxContainer } from '../../common/BoxContainer'
import { GenericButton } from '../../common/GenericButton'

/** `SelectOption` - A component that renders a selectable option within a list.
 * It uses a button to display the option's label and triggers a selection handler when clicked.
 */
export const SelectOption = <T,>({
  option,
  handleSelect,
  buttonProps,
  ...rest
}: SelectOptionProps<T>) => {
  const { value, label, href, disabled } = option
  return (
    <BoxContainer as='li' {...rest}>
      {href && !disabled ? (
        <NextLink
          href={href}
          skipLocalization
          objectId='language_select'
          customValue={value}
          className={cn(
            'button relative w-full cursor-pointer select-none items-start justify-between rounded-sm px-3.5 py-3 text-left hover:bg-gray-100',
            buttonProps?.className
          )}
        >
          {label}
        </NextLink>
      ) : (
        <GenericButton
          disabled={disabled}
          type='button'
          onClick={() => handleSelect?.(option)}
          className={cn(
            'relative w-full cursor-pointer select-none items-start justify-between rounded-sm px-3.5 py-3 text-left hover:bg-gray-100',
            buttonProps?.className
          )}
        >
          {label}
        </GenericButton>
      )}
    </BoxContainer>
  )
}
