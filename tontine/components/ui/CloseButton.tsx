import { CloseIcon } from '@sanity/icons'

import type { IconProps } from '../../types/component.types'
import type { GenericButtonProps } from '../common/GenericButton'
import { GenericButton } from '../common/GenericButton'

export type CloseButtonProps = { iconProps?: IconProps } & GenericButtonProps

/** `CloseButton` - A button component that renders a close icon by default.
 * It extends our `GenericButton` component.
 * This component can include custom children or default to a `CloseIcon`.
 */
export function CloseButton({ iconProps, ...rest }: CloseButtonProps) {
  return (
    <GenericButton aria-label='Close' {...rest}>
      {rest?.children ?? <CloseIcon {...iconProps} />}
    </GenericButton>
  )
}
