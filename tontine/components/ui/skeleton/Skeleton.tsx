import { cn } from '../../../helper-functions/UtilFunctions'
import { isCypress } from '../../../serverless/keys'
import type { SkeletonBoxProps } from '../../../types/components/Skeleton.types'
import { BoxContainer } from '../../common/BoxContainer'

export function Skeleton({
  className,
  children,
  loading,
  ...rest
}: SkeletonBoxProps) {
  return (
    <BoxContainer
      className={cn(
        'relative overflow-hidden rounded-md',
        'before:pointer-events-none before:absolute before:inset-0 before:z-50 before:from-grey-350 before:to-brand-50 before:opacity-100',
        loading && 'before:animate-pulse',
        !loading && 'before:opacity-0 before:duration-2000',
        className,
        isCypress && 'before:opacity-0 before:duration-50'
      )}
      {...rest}
    >
      {children}
    </BoxContainer>
  )
}
