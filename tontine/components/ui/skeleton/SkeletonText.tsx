import { cn } from '../../../helper-functions/UtilFunctions'
import type { SkeletonTextProps } from '../../../types/components/Skeleton.types'
import { BoxContainer } from '../../common/BoxContainer'

export function SkeletonText({
  noOfLines = 3,
  className,
  ...rest
}: SkeletonTextProps) {
  return (
    <BoxContainer {...rest} className={cn('flex flex-col gap-2', className)}>
      {Array.from({ length: noOfLines }).map((_, index) => (
        <BoxContainer
          key={index}
          className={cn('h-4 animate-pulse rounded', {
            'max-w-[80%]': index === noOfLines - 1,
          })}
        />
      ))}
    </BoxContainer>
  )
}
