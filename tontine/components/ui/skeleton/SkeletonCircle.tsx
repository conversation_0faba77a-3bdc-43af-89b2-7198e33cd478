import { cn } from '../../../helper-functions/UtilFunctions'
import type { SkeletonCircleProps } from '../../../types/components/Skeleton.types'
import { BoxContainer } from '../../common/BoxContainer'

export function SkeletonCircle({
  size = '8',
  className,
  ...rest
}: SkeletonCircleProps) {
  return (
    <BoxContainer
      className={cn('animate-pulse rounded-full', className)}
      style={{
        width: size,
        height: size,
      }}
      {...rest}
    />
  )
}
