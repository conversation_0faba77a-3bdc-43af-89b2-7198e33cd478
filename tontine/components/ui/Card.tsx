import type { NextLinkProps } from '../../types/components/NextLink.types'
import type {
  LocalizedContentType,
  LocalizedStringContentType,
} from '../../types/sections/section.types'
import type { BoxContainerProps } from '../common/BoxContainer'
import { BoxContainer } from '../common/BoxContainer'
import { NextLink } from '../common/NextLink'
import type { TitleProps } from '../typography/Title'
import { Title } from '../typography/Title'

type CardHeaderProps = {
  title?: LocalizedContentType | LocalizedStringContentType
  titleProps?: TitleProps
} & BoxContainerProps

const CardRoot = (props: BoxContainerProps) => (
  <BoxContainer {...props}>{props.children}</BoxContainer>
)

/**
 * A CardRoot that can also be a link. If the href is '#', it will render a
 * clickable div with role 'button' and tabIndex 0. Otherwise, it will render a
 * NextLink.
 */
export const CardRootClickable = (props: NextLinkProps) => {
  const { href, onClick, className, children } = props

  if (href === '#') {
    return (
      <div
        onClick={onClick}
        className={className}
        role='button'
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') onClick?.()
        }}
      >
        {children}
      </div>
    )
  }

  return <NextLink {...props}>{children}</NextLink>
}

const CardHeader = ({
  title,
  titleProps,
  children,
  ...rest
}: CardHeaderProps) => (
  <BoxContainer {...rest}>
    {children || (
      <Title as='h3' {...titleProps}>
        {title}
      </Title>
    )}
  </BoxContainer>
)

const CardBody = (props: BoxContainerProps) => (
  <BoxContainer {...props}>{props.children}</BoxContainer>
)

const CardFooter = (props: BoxContainerProps) => (
  <BoxContainer {...props}>{props.children}</BoxContainer>
)

export const Card = Object.assign(CardRoot, {
  Header: CardHeader,
  Body: CardBody,
  Footer: CardFooter,
})

export const ClickableCard = Object.assign(CardRootClickable, {
  Header: CardHeader,
  Body: CardBody,
  Footer: CardFooter,
})
