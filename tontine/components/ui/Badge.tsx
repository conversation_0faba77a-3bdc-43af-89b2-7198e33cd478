import { cn } from '../../helper-functions/UtilFunctions'
import type { ComponentColorPalettes } from '../../types/component.types'
import type { WrappedTextProps } from '../typography/WrappedText'
import { WrappedText } from '../typography/WrappedText'

export type BadgeProps = {
  colorPalette?: ComponentColorPalettes
  variant?: 'solid' | 'outline'
} & WrappedTextProps

export const Badge = ({
  children,
  colorPalette = 'gray',
  variant = 'solid',
  className,
  ...rest
}: BadgeProps) => {
  const colorStyle =
    variant === 'solid'
      ? colorPalette
      : cn(colorPalette, 'border border-currentColor')

  return (
    <WrappedText
      as='span'
      {...rest}
      className={cn(
        'inline-flex items-center rounded-full px-2.5 py-0.5 font-medium text-xs',
        colorStyle,
        className
      )}
    >
      {children}
    </WrappedText>
  )
}
