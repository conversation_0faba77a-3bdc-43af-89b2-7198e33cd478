'use client'

import { BoxContainer } from '../../../components/common/BoxContainer'
import { CONSTANTS } from '../../../data-resource/constants'
import { cn } from '../../../helper-functions/UtilFunctions'
import { useToast } from '../../../providers/ToasterProvider'
import { STYLE } from '../../../styles/style'
import { Toast } from './Toast'

/** A component that renders a stack of toasts. */
export const ToastContainer = () => {
  const { toasts, removeToast } = useToast()

  return (
    <BoxContainer
      className={cn(
        'fixed top-4 right-4 space-y-2 transition',
        STYLE.Z_INDEX.TOASTER
      )}
    >
      {toasts.map((toast) => (
        <Toast
          key={toast?.id}
          toast={{
            ...toast,
            duration: toast?.duration ?? CONSTANTS.TOAST_TIMEOUT,
          }}
          onDismiss={removeToast}
        />
      ))}
    </BoxContainer>
  )
}
