'use client'

import type { TestID } from 'cypress/support/ui-component-ids'
import type { DetailedHTMLProps, DialogHTMLAttributes } from 'react'
import { useEffect, useRef, useState } from 'react'

import { cn, getPlacementClasses } from '../../helper-functions/UtilFunctions'
import { STYLE } from '../../styles/style'
import type { ComponentPlacement } from '../../types/component.types'
import type {
  LocalizedContentType,
  LocalizedStringContentType,
} from '../../types/sections/section.types'
import type { BoxContainerProps } from '../common/BoxContainer'
import { BoxContainer } from '../common/BoxContainer'
import type { GenericButtonProps } from '../common/GenericButton'
import { GenericButton } from '../common/GenericButton'
import type { TitleProps } from '../typography/Title'
import { Title } from '../typography/Title'
import type { CloseButtonProps } from './CloseButton'
import { CloseButton } from './CloseButton'

export type DialogProps = {
  title?: LocalizedContentType | LocalizedStringContentType
  children: React.ReactNode
  placement?: ComponentPlacement['placement']
  triggerButtonProps?: GenericButtonProps
  headerProps?: Omit<BoxContainerProps, 'children'>
  titleProps?: TitleProps
  closeButtonProps?: CloseButtonProps
  showBackdropExternal?: boolean
  dialogProps?: Omit<
    BoxContainerProps &
      DetailedHTMLProps<
        DialogHTMLAttributes<HTMLDialogElement>,
        HTMLDialogElement
      >,
    'children' | 'onClick'
  > & { 'data-cy'?: TestID }
  setRef?: (ref: HTMLDialogElement) => void
} & Omit<BoxContainerProps, 'title'> &
  Omit<ComponentPlacement, 'placement'>

/** `Dialog` - A component that renders a modal dialog with customizable positioning and appearance.
 *
 * This component provides an optional trigger button to open the dialog, and allows for customization
 * of its header, content, and close button through various props. It also handles outside clicks to close
 * the dialog.
 *
 * Props:
 * - `title`: The title displayed in the dialog header.
 * - `children`: The content to be displayed inside the dialog.
 * - `placement`: The positioning of the dialog on the screen. Defaults to 'center'.
 * - `offsetX`: Horizontal offset for dialog positioning.
 * - `offsetY`: Vertical offset for dialog positioning.
 * - `showOpenButton`: Boolean to control the rendering of the dialog opener button.
 * - `triggerButtonProps`: Props for the button that triggers the dialog to open.
 * - `rootProps`: Props for the root dialog element for additional customization.
 * - `headerProps`: Props to customize the header section of the dialog.
 * - `closeButtonProps`: Props for the close button in the dialog header.
 * - `titleProps`: Props for customizing the title element within the dialog header.
 *
 * Utilizes `useRef` to manage the dialog element and handle open/close actions.
 */
export function Dialog({
  title,
  children,
  placement = 'center',
  offsetX,
  offsetY,
  triggerButtonProps,
  dialogProps,
  headerProps,
  closeButtonProps,
  titleProps,
  showBackdropExternal,
  setRef,
}: DialogProps) {
  const [showBackdrop, setShowBackdrop] = useState(false)
  const dialogRef = useRef<HTMLDialogElement>(null)

  useEffect(() => {
    if (setRef && dialogRef?.current) {
      setRef(dialogRef?.current)
    }
  }, [setRef])

  const handleOpen = () => {
    dialogRef?.current?.showModal()
    setShowBackdrop(true)
  }

  const handleClose = () => {
    dialogRef?.current?.close()
    setShowBackdrop(false)
  }

  const handleClickOutside = (e: React.MouseEvent<HTMLDialogElement>) => {
    if (e.target === dialogRef.current) handleClose()
  }

  return (
    <>
      {/* Dialog Opener Button */}
      <GenericButton onClick={handleOpen} {...triggerButtonProps}>
        {triggerButtonProps?.children || 'Open Dialog'}
      </GenericButton>

      {(showBackdropExternal || showBackdrop) && (
        <div
          className={cn(
            'pointer-events-none fixed inset-0 bg-background-1000/50 backdrop-blur-[.25rem] starting:backdrop-blur-[0] transition-[backdrop-filter] duration-350',
            STYLE.Z_INDEX.DIALOG_BACKDROP
          )}
        />
      )}

      {/* Dialog */}
      <dialog
        ref={dialogRef}
        aria-labelledby='dialog-title'
        {...dialogProps}
        onClick={handleClickOutside}
        onClose={handleClose}
        className={cn(
          'dialog',
          'fixed top-[10%] w-full md:w-fit md:min-w-2xl',
          'overflow-y-hidden',
          getPlacementClasses({ placement, offsetX, offsetY }),
          dialogProps?.className
        )}
      >
        {/* Header */}
        <BoxContainer {...headerProps}>
          <Title id='dialog-title' as='h2' {...titleProps}>
            {title}
          </Title>
          <CloseButton
            onClick={handleClose}
            aria-label='Close dialog'
            {...closeButtonProps}
            className={cn(
              'text-gray-500 hover:bg-brand-100 hover:text-gray-800',
              closeButtonProps?.className
            )}
          />
        </BoxContainer>
        {/* Content */}
        {children}
      </dialog>
    </>
  )
}
