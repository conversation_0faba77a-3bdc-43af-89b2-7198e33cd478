'use client'

import { useRouter } from 'next/navigation'

import { CONSTANTS } from '../../data-resource/constants'
import strings from '../../data-resource/strings.json'
import { useCountDownTimer } from '../../hooks/useCountDownTimer'
import { useLanguage } from '../../providers/LanguageContext'
import { NextLink } from '../common/NextLink'
import { WrappedText } from '../typography/WrappedText'

/**
 * Error page title with a countdown timer that redirects to home page after 10 seconds
 */
export const ErrorCountDownTitle = () => {
  const { language } = useLanguage()
  const router = useRouter()
  const { secs } = useCountDownTimer({
    hours: CONSTANTS.ERROR_PAGE_TIMER_HOURS,
    minutes: CONSTANTS.ERROR_PAGE_TIMER_MINUTES,
    seconds: CONSTANTS.ERROR_PAGE_TIMER_SECONDS,
    onCountdownFinished: () => {
      router.push(language === 'en' ? '/' : `/${language}`)
    },
  })
  return (
    <>
      <WrappedText className='text-center font-semibold text-2xl text-background-100 drop-shadow-lg md:text-3xl lg:text-4xl'>{`${strings.ERROR_PAGE_RESURRECT} ${secs} ${strings.ERROR_PAGE_RESSURECT_SECONDS}`}</WrappedText>

      <NextLink
        href={language === 'en' ? '/' : `/${language}`}
        className='button solid mt-4 px-10'
      >
        {strings.GO_BACK_TO_HOME}
      </NextLink>
    </>
  )
}
