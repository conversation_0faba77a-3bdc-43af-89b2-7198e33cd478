import strings from '../../data-resource/strings.json'
import { BoxContainer } from '../common/BoxContainer'
import { Title } from '../typography/Title'
import { ErrorCountDownTitle } from './ErrorCountDownTitle'

/**
 * The error page content is added here
 * background image, title, subtitle, caption and a link to go back to home
 */
export const ErrorContent = () => {
  return (
    <BoxContainer className='flex h-(--nav-height-offset) flex-col items-center justify-center gap-4 bg-[url(https://cdn.sanity.io/images/hl9czw39/production/eb4d34a0f32c6d39dc3dfb650c9b1758fa321d77-2880x1620.webp)] bg-center bg-cover bg-no-repeat p-4'>
      <Title
        title={strings.ERROR_PAGE_TITLE}
        className='text-center font-semibold text-3xl text-background-100 drop-shadow-lg md:text-4xl lg:text-5xl'
      />
      <ErrorCountDownTitle />
    </BoxContainer>
  )
}
