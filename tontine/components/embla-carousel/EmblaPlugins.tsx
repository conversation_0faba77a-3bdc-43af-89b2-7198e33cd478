'use client'

import { useCallback, useEffect, useRef } from 'react'

import track from '../../app/api/analytics'
import { CONSTANTS } from '../../data-resource/constants'
import { numberWithinRange } from '../../helper-functions/UtilFunctions'
import { CarouselEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { SectionChunks } from '../../types/Analytics/AnalyticsObjectIds.types'
import type { EmblaCarouselApi } from '../../types/common.types'

type EffectOptions = {
  emblaApi: EmblaCarouselApi
  shouldApplyEffect?: boolean
  effectClass?: string
  tweenFactorBase?: number
}

/**
 * `useEmblaEffect` is a custom hook that applies a visual effect to the Embla Carousel.
 * - `options` - The options for the effect.
 * - `emblaApi` - The Embla Carousel API.
 * - `shouldApplyEffect` - Whether to apply the effect.
 * - `effectClass` - The CSS class of the elements to apply the effect to.
 * - `tweenFactorBase` - The base factor for the tween effect defaults to 0.2.
 * - `calculateEffectValue` - A function to calculate the effect value.
 * - `applyEffectCallback` - A function to apply the effect.
 */
const useEmblaEffect = ({
  emblaApi,
  shouldApplyEffect,
  effectClass,
  tweenFactorBase = CONSTANTS.CAROUSEL.TWEEN_FACTOR.DEFAULT,
  applyEffectCallback,
  calculateEffectValue,
}: EffectOptions & {
  calculateEffectValue: (diffToTarget: number, tweenFactor: number) => number
  applyEffectCallback: (tweenNode: HTMLElement | null, value: number) => void
}) => {
  const tweenFactor = useRef(0)
  const tweenNodes = useRef<Array<HTMLElement>>([])

  const setTweenNodes = useCallback(
    (tweenEmblaApi: EmblaCarouselApi): void => {
      if (tweenEmblaApi)
        tweenNodes.current = tweenEmblaApi
          .slideNodes()
          .map(
            (slideNode) =>
              slideNode.querySelector(`.${effectClass}`) as HTMLElement
          )
    },
    [effectClass]
  )

  const setTweenFactor = useCallback(
    (tweenFactorEmblaApi: EmblaCarouselApi) => {
      if (tweenFactorEmblaApi)
        tweenFactor.current =
          tweenFactorBase * tweenFactorEmblaApi.scrollSnapList().length
    },
    [tweenFactorBase]
  )

  const applyEffect = useCallback(
    (tweenScaleEmblaApi: EmblaCarouselApi, eventName?: string) => {
      if (!tweenScaleEmblaApi) return
      const engine = tweenScaleEmblaApi.internalEngine()
      const scrollProgress = tweenScaleEmblaApi.scrollProgress()
      const slidesInView = tweenScaleEmblaApi.slidesInView()
      const isScrollEvent = eventName === 'scroll'

      tweenScaleEmblaApi.scrollSnapList().forEach((scrollSnap, snapIndex) => {
        let diffToTarget = scrollSnap - scrollProgress
        const slidesInSnap = engine.slideRegistry[snapIndex]

        slidesInSnap?.forEach((slideIndex) => {
          if (isScrollEvent && !slidesInView.includes(slideIndex)) return

          if (engine.options.loop) {
            engine.slideLooper.loopPoints.forEach((loopItem) => {
              const target = loopItem.target()

              if (slideIndex === loopItem.index && target !== 0) {
                const sign = Math.sign(target)

                if (sign === -1) {
                  diffToTarget = scrollSnap - (1 + scrollProgress)
                }
                if (sign === 1) {
                  diffToTarget = scrollSnap + (1 - scrollProgress)
                }
              }
            })
          }

          const value = calculateEffectValue(diffToTarget, tweenFactor.current)
          const tweenNode = tweenNodes.current[slideIndex]
          if (tweenNode) applyEffectCallback(tweenNode, value)
        })
      })
    },
    [applyEffectCallback, calculateEffectValue]
  )

  useEffect(() => {
    if (!emblaApi || !shouldApplyEffect) return

    setTweenNodes(emblaApi)
    setTweenFactor(emblaApi)
    applyEffect(emblaApi)

    emblaApi
      .on('reInit', setTweenNodes)
      .on('reInit', setTweenFactor)
      .on('reInit', applyEffect)
      .on('scroll', applyEffect)
      .on('slideFocus', applyEffect)
  }, [applyEffect, emblaApi, setTweenFactor, setTweenNodes, shouldApplyEffect])
}

/** `useScalingEffect` is a custom hook that applies a scaling effect to the Embla Carousel utilizing useScalingEffect. */
export const useScalingEffect = (options: EffectOptions) => {
  useEmblaEffect({
    ...options,
    calculateEffectValue: (diffToTarget, tweenFactor) => {
      const tweenValue = 1 - Math.abs(diffToTarget * tweenFactor)
      return numberWithinRange(tweenValue, 0, 1)
    },
    applyEffectCallback: (tweenNode, scale) => {
      if (tweenNode) tweenNode.style.transform = `scale(${scale})`
    },
  })
}

/** `useParallaxEffect` is a custom hook that applies a parallax effect to the Embla Carousel utilizing useScalingEffect. [ Currently Unused ] */
export const useParallaxEffect = (options: EffectOptions) => {
  useEmblaEffect({
    ...options,
    calculateEffectValue: (diffToTarget, tweenFactor) => {
      return diffToTarget * (-1 * tweenFactor) * 100
    },
    applyEffectCallback: (tweenNode, translate) => {
      if (tweenNode) tweenNode.style.transform = `translateX(${translate}%)`
    },
  })
}

/** Tracks the current slide from the Embla carousel. */
export const trackCurrentSlide = (
  emblaApi: EmblaCarouselApi | undefined,
  sectionContext: SectionChunks,
  eventType: 'next' | 'previous' | 'choose',
  index?: number
) => {
  if (!emblaApi) return
  const currentIndex = index ?? emblaApi.selectedScrollSnap()
  const currentSlide = emblaApi.slideNodes()[currentIndex]
  track({
    event: CarouselEvent?.[eventType],
    properties: {
      object_id: `${sectionContext}_${eventType}`,
      object_value: currentSlide?.textContent,
      description: CONSTANTS.ANALYTICS_DESCRIPTIONS.CAROUSEL_SCROLL,
      slide_index: currentIndex,
    },
  })
}
