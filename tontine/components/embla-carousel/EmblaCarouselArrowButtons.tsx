'use client'

import { useCallback, useEffect, useState } from 'react'

import type { SectionChunks } from '../../types/Analytics/AnalyticsObjectIds.types'
import type { EmblaCarouselApi } from '../../types/common.types'
import { trackCurrentSlide } from './EmblaPlugins'

type UsePrevNextButtonsType = {
  prevBtnDisabled: boolean
  nextBtnDisabled: boolean
  onPrevButtonClick: () => void
  onNextButtonClick: () => void
}

/**
 * `usePrevNextButtons` is a custom hook that provides functionality for the previous and next buttons in a carousel.
 * - `emblaApi` The API of the Embla Carousel instance.
 * - `prevBtnDisabled` A boolean indicating whether the previous button is disabled.
 * - `nextBtnDisabled` A boolean indicating whether the next button is disabled.
 * - `onPrevButtonClick` A function that scrolls to the previous item when the previous button is clicked.
 * - `onNextButtonClick` A function that scrolls to the next item when the next button is clicked.
 */
export const usePrevNextButtons = (
  emblaApi: EmblaCarouselApi | undefined,
  sectionContext: SectionChunks
): UsePrevNextButtonsType => {
  const [prevBtnDisabled, setPrevBtnDisabled] = useState(true)
  const [nextBtnDisabled, setNextBtnDisabled] = useState(true)

  const logAction = useCallback(
    (eventType: 'next' | 'previous' | 'choose') => {
      trackCurrentSlide(emblaApi, sectionContext, eventType)
    },
    [emblaApi, sectionContext]
  )

  const onPrevButtonClick = useCallback(() => {
    if (!emblaApi) return
    emblaApi.scrollPrev()
    logAction('previous')
  }, [emblaApi, logAction])

  const onNextButtonClick = useCallback(() => {
    if (!emblaApi) return
    emblaApi.scrollNext()
    logAction('next')
  }, [emblaApi, logAction])

  const onSelect = useCallback((onSelectEmblaApi: EmblaCarouselApi) => {
    if (onSelectEmblaApi) {
      setPrevBtnDisabled(!onSelectEmblaApi.canScrollPrev())
      setNextBtnDisabled(!onSelectEmblaApi.canScrollNext())
    }
  }, [])

  useEffect(() => {
    if (!emblaApi) return
    onSelect(emblaApi)
    emblaApi.on('reInit', onSelect).on('select', onSelect)
  }, [emblaApi, onSelect])

  return {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  }
}
