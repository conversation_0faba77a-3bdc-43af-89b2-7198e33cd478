import { cn } from '../../helper-functions/UtilFunctions'
import type { BoxContainerProps } from '../common/BoxContainer'
import { BoxContainer } from '../common/BoxContainer'

/** A wrapper component for individual slides in an Embla carousel. */
export function EmblaSlide({
  children,
  className,
  ...rest
}: BoxContainerProps) {
  return (
    <BoxContainer
      as='li'
      {...rest}
      className={cn('min-w-0 flex-(--embla-size-base)', className)}
    >
      {children}
    </BoxContainer>
  )
}
