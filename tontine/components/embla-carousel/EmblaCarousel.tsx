'use client'

import Autoplay from 'embla-carousel-autoplay'
import useEmblaCarousel from 'embla-carousel-react'

import { CONSTANTS } from '../../data-resource/constants'
import { cn } from '../../helper-functions/UtilFunctions'
import type { BreakpointsType } from '../../types/common.types'
import type { EmblaCarouselProps } from '../../types/components/Embla.types'
import { BoxContainer } from '../common/BoxContainer'
import { GenericButton } from '../common/GenericButton'
import { Skeleton } from '../ui/skeleton/Skeleton'
import { EmblaButton } from './EmblaButton'
import { usePrevNextButtons } from './EmblaCarouselArrowButtons'
import { useDotButton } from './EmblaCarouselDotButton'
import { useScalingEffect } from './EmblaPlugins'

/** Generates gradient styles with pseudo-elements based on the provided breakpoints. */
const getGradientStyles = ({
  breakpoints,
}: {
  breakpoints?: Array<BreakpointsType>
}) => {
  if (!breakpoints) return ''

  // Generate display classes for pseudo-elements
  const generateDisplayClasses = () => {
    return CONSTANTS.BREAKPOINTS?.map((bp) => {
      const display = breakpoints?.includes(bp) ? 'block' : 'hidden'
      return bp === 'base' ? display : `${bp}:${display}`
    })
  }

  return cn(generateDisplayClasses())
}

/**
 * `EmblaCarousel` is a component that renders an Embla Carousel with optional navigation buttons and dots.
 * - `children` Items to show in the slider preferably EmblaSlide elements.
 * - `options` Embla carousel options https://www.embla-carousel.com/api/options/.
 * - `showButtons` A boolean to decide if next and prev buttons should be shown.
 * - `showDots` A boolean to decide if navigation dots should be shown.
 * - `dotsStyling` The styling for the navigation dots.
 * - `buttonStyling` The styling for the navigation buttons.
 * - `controlsWrapperProps` The styling for the controls wrapper.
 * - `emblaWrapperStyling` Styles for the upmost wrapper element.
 * - `sliderWrapperProps` Styles for the slider wrapper.
 * - `autoPlayOptions` Options for autoplay https://www.embla-carousel.com/plugins/autoplay/#options.
 * - `shouldScale` and `shouldParallax` A boolean to decide if the effect should be applied.
 * - `parallaxFactor` and `scaleFactor` The effect potency factor. More is more.
 * - `parallaxClass` and `scaleClass` What the class name of the effect layer should be.
 */
export const EmblaCarousel = (props: EmblaCarouselProps) => {
  const {
    children,
    sectionContext,
    options,
    showButtons,
    showDots,
    dotsStyling,
    buttonStyling,
    gradientClassName,
    controlsWrapperProps,
    emblaWrapperProps,
    sliderWrapperProps,
    autoPlayOptions,
    shouldScale,
    shouldParallax,
    scaleFactor,
    parallaxFactor,
    scaleClass,
    parallaxClass,
    gradientBreakpoints,
  } = props

  const { prevButtonProps, nextButtonProps, ...restButtonProps } =
    buttonStyling ?? {}

  const [emblaRef, emblaApi] = useEmblaCarousel(options, [
    Autoplay(autoPlayOptions),
  ])

  const { selectedIndex, scrollSnaps, onDotButtonClick } = useDotButton(
    emblaApi,
    sectionContext
  )

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi, sectionContext)

  useScalingEffect({
    emblaApi,
    shouldApplyEffect: shouldScale,
    tweenFactorBase: scaleFactor,
    effectClass: scaleClass,
  })

  useScalingEffect({
    emblaApi,
    shouldApplyEffect: shouldParallax,
    tweenFactorBase: parallaxFactor,
    effectClass: parallaxClass,
  })

  return (
    <Skeleton
      loading={!emblaApi}
      {...emblaWrapperProps}
      className={cn(
        gradientBreakpoints && 'embla-gradient',
        emblaWrapperProps?.className
      )}
    >
      {gradientBreakpoints && (
        <div
          className={cn(
            'left',
            gradientBreakpoints &&
              getGradientStyles({
                breakpoints: gradientBreakpoints,
              })
          )}
        />
      )}

      <div
        className={cn(
          'relative h-full w-full overflow-hidden',
          gradientClassName
        )}
        ref={emblaRef}
      >
        <BoxContainer
          as='ul'
          {...sliderWrapperProps}
          className={cn(
            'backface-hidden flex w-full touch-pan-y touch-pinch-zoom list-none',
            sliderWrapperProps?.className
          )}
        >
          {children}
        </BoxContainer>

        {(showButtons || showDots) && (
          <BoxContainer {...controlsWrapperProps}>
            {showButtons && (
              <EmblaButton
                direction='left'
                onClick={onPrevButtonClick}
                disabled={prevBtnDisabled}
                {...restButtonProps}
                {...prevButtonProps}
                className={cn(
                  restButtonProps?.className,
                  prevButtonProps?.className
                )}
                arrowProps={restButtonProps?.arrowProps}
              />
            )}
            {showDots && (
              <BoxContainer {...dotsStyling?.dotsWrapperProps}>
                {scrollSnaps.map((_, index) => (
                  <GenericButton
                    key={index}
                    aria-label={'dot-icon'}
                    onClick={() => onDotButtonClick(index)}
                    {...dotsStyling?.dotProps}
                    className={cn(
                      dotsStyling?.dotProps?.className,
                      index === selectedIndex && dotsStyling?.currentDotStyle
                    )}
                  />
                ))}
              </BoxContainer>
            )}
            {showButtons && (
              <EmblaButton
                direction='right'
                onClick={onNextButtonClick}
                disabled={nextBtnDisabled}
                {...restButtonProps}
                {...nextButtonProps}
                className={cn(
                  restButtonProps?.className,
                  nextButtonProps?.className
                )}
                arrowProps={restButtonProps?.arrowProps}
              />
            )}
          </BoxContainer>
        )}
      </div>
      {gradientBreakpoints && (
        <div
          className={cn(
            'right',
            getGradientStyles({
              breakpoints: gradientBreakpoints,
            })
          )}
        />
      )}
    </Skeleton>
  )
}
