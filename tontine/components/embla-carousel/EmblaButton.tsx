import { ArrowLeftIcon, ArrowRightIcon } from '@sanity/icons'

import type { EmblaButtonProps } from '../../types/components/Embla.types'
import { GenericButton } from '../common/GenericButton'

/** `EmblaButton` is a component that renders a button with optional styling and direction.
 * - `children` The children nodes to be rendered in the button.
 * - `direction` The direction of the button, either 'next' or 'prev'.
 */
export const EmblaButton = ({
  direction,
  arrowProps,
  ...rest
}: EmblaButtonProps) => {
  const directionalArrow =
    direction === 'right' ? (
      <ArrowRightIcon {...arrowProps} />
    ) : (
      <ArrowLeftIcon {...arrowProps} />
    )

  return (
    <GenericButton aria-label={`${direction}-arrow`} type='button' {...rest}>
      {directionalArrow}
    </GenericButton>
  )
}
