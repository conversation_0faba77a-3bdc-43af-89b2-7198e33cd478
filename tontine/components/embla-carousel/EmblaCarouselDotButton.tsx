'use client'

import { useCallback, useEffect, useState } from 'react'

import type { SectionChunks } from '../../types/Analytics/AnalyticsObjectIds.types'
import type { EmblaCarouselApi } from '../../types/common.types'
import { trackCurrentSlide } from './EmblaPlugins'

type UseDotButtonType = {
  selectedIndex: number
  scrollSnaps: Array<number>
  onDotButtonClick: (index: number) => void
}

/**
 * `useDotButton` is a custom hook that provides functionality for a dot button in a carousel.
 * - `emblaApi` The API of the Embla Carousel instance.
 * - `selectedIndex` The index of the currently selected dot.
 * - `scrollSnaps` An array of scroll snap points.
 * - `onDotButtonClick` A function that scrolls to a specific index when a dot button is clicked.
 */
export const useDotButton = (
  emblaApi: EmblaCarouselApi | undefined,
  sectionContext: SectionChunks
): UseDotButtonType => {
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [scrollSnaps, setScrollSnaps] = useState<Array<number>>([])

  const logAction = useCallback(
    (eventType: 'next' | 'previous' | 'choose', index: number) => {
      trackCurrentSlide(emblaApi, sectionContext, eventType, index)
    },
    [emblaApi, sectionContext]
  )

  const onDotButtonClick = useCallback(
    (index: number) => {
      if (!emblaApi) return
      logAction('choose', index)
      emblaApi.scrollTo(index)
    },
    [emblaApi, logAction]
  )

  const onInit = useCallback((emblaApi2: EmblaCarouselApi) => {
    if (emblaApi2) setScrollSnaps(emblaApi2.scrollSnapList())
  }, [])

  const onSelect = useCallback((emblaApi3: EmblaCarouselApi) => {
    if (emblaApi3) setSelectedIndex(emblaApi3.selectedScrollSnap())
  }, [])

  useEffect(() => {
    if (!emblaApi) return

    onInit(emblaApi)
    onSelect(emblaApi)
    emblaApi.on('reInit', onInit).on('reInit', onSelect).on('select', onSelect)
  }, [emblaApi, onInit, onSelect])

  return {
    selectedIndex,
    scrollSnaps,
    onDotButtonClick,
  }
}
