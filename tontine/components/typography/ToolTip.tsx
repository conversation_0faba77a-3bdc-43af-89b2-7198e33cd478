import { cn } from '../../helper-functions/UtilFunctions'
import type {
  LocalizedContentType,
  LocalizedStringContentType,
} from '../../types/sections/section.types'
import { BoxContainer } from '../common/BoxContainer'
import { LocalizedContentParser } from './LocalizedContentParser'
import { WrappedText } from './WrappedText'

export type TooltipWrapperProps = {
  type?: string
  children?: React.ReactNode
  textTooltip?: LocalizedContentType
  glossaryTooltip?: {
    title?: LocalizedStringContentType
    subtitle?: LocalizedContentType
  }
}

const tooltipPositioningClass =
  'absolute bottom-full left-0 z-10 mb-2 md:w-[min(90vw,22rem)] right-0'

const tooltipStyling =
  'rounded-sm border border-gray-400 bg-background p-2 text-base shadow-md'

const tooltipVisibilityClass =
  'invisible group-hover:visible group-active:visible group-focus:visible'

/**
 * A wrapper component that renders a tooltip on hover
 * Accepts either a custom text tooltip or a glossary tooltip content.
 */
export const TooltipWrapper = ({
  children,
  type,
  textTooltip,
  glossaryTooltip,
}: TooltipWrapperProps) => {
  const tooltipContent =
    type === 'text' ? textTooltip : glossaryTooltip?.subtitle

  if (!tooltipContent) return <>{children}</>

  return (
    <BoxContainer as='span' className='group inline-block md:relative'>
      <WrappedText as='span' className='bolder cursor-help text-brand'>
        {children}
      </WrappedText>

      <BoxContainer
        as='span'
        className={cn(
          tooltipPositioningClass,
          tooltipStyling,
          tooltipVisibilityClass
        )}
      >
        <BoxContainer
          as='span'
          className='-bottom-[0.45rem] absolute left-0 ml-1.5 hidden h-3 w-3 rotate-225 border-gray-400 border-t border-l bg-background md:block '
        />
        <LocalizedContentParser
          overrideElements={{
            p: {
              tag: 'span',
              className: 'text-sm leading-6',
            },
          }}
        >
          {tooltipContent}
        </LocalizedContentParser>
      </BoxContainer>
    </BoxContainer>
  )
}
