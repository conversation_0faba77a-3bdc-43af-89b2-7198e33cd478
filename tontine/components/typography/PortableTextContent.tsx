import { PortableText } from 'next-sanity'

import { cn } from '../../helper-functions/UtilFunctions'
import type { PortableContentProps } from '../../types/components/PortableText.types'
import { BoxContainer } from '../common/BoxContainer'
import { portableTextParser } from './PortableTextParser'

/**
 * A component for rendering markdown content using Portable Text.
 * It supports an optional wrapper element, customizable rendering for
 * specific block types, and flexible element overrides.
 */
export const PortableTextContent = ({
  markDownText,
  parentSectionId,
  renderDefaultBlock,
  renderWrapper = false,
  as,
  className,
  overrideElements = {},
  ...rest
}: PortableContentProps) => {
  return (
    <>
      {renderWrapper ? (
        <BoxContainer
          as={as ?? 'article'}
          {...rest}
          className={cn('markdown markdown-defaults flex flex-col', className)}
        >
          {markDownText && (
            <PortableText
              value={markDownText}
              components={portableTextParser({
                overrideElements,
                renderDefaultBlock,
                parentSectionId,
              })}
            />
          )}
        </BoxContainer>
      ) : (
        <>
          {markDownText && (
            <PortableText
              value={markDownText}
              components={portableTextParser({
                renderDefaultBlock,
                overrideElements,
                parentSectionId,
              })}
            />
          )}
        </>
      )}
    </>
  )
}
