import { cn } from '../../helper-functions/UtilFunctions'
import type { TableComponentProps } from '../../types/components/PortableText.types'

/** A table component for rendering tabular data. It supports both standard and bi-directional tables. */
export const Table = (props: TableComponentProps) => {
  const [head, ...rows] = props.value.rows
  const isBiDirectional = head?.cells?.[0]?.length === 0

  return (
    <table className='w-full table-fixed overflow-hidden rounded-md shadow-sm ring ring-brand-300'>
      <thead className='bg-brand-300 text-background'>
        <tr>
          {head?.cells?.map((cell) => (
            <th
              className='w-max p-2 text-center font-semibold text-sm md:text-lg'
              key={cell}
            >
              {cell}
            </th>
          ))}
        </tr>
      </thead>
      <tbody>
        {rows?.map((row, index) => (
          <tr
            className='border-brand-200 nth-last-[1]:border-0 border-b-1'
            key={index}
          >
            {row?.cells?.map((cell, cellIndex) => {
              const Component = isBiDirectional && cellIndex === 0 ? 'th' : 'td'
              const isLastCell = cellIndex === row?.cells?.length - 1
              return (
                <Component
                  className={cn(
                    'p-2 text-left align-baseline text-sm md:text-base',
                    !isLastCell && 'border-brand-200 border-r'
                  )}
                  key={cellIndex}
                >
                  {cell}
                </Component>
              )
            })}
          </tr>
        ))}
      </tbody>
    </table>
  )
}
