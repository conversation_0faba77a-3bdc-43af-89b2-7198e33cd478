'use client'

import { cn } from '../../helper-functions/UtilFunctions'
import { useDynamicMetrics } from '../../hooks/useDynamicMetrics'
import { Skeleton } from '../ui/skeleton/Skeleton'

/** Displays a metric with a loading skeleton while fetching data. */
export const DynamicMetric = ({ metric }: { metric?: string }) => {
  const { isLoading, metrics } = useDynamicMetrics(metric ?? '')
  return (
    <Skeleton
      loading={isLoading}
      as={'span'}
      className={cn(
        'inline-flex h-min rounded-lg',
        isLoading && 'max-h-[32px] max-w-[50px] opacity-50'
      )}
    >
      {metrics}
    </Skeleton>
  )
}
