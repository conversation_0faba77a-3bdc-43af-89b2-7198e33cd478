import { CloseIcon } from '@sanity/icons'
import type {
  <PERSON><PERSON><PERSON>,
  ChangeEventH<PERSON>ler,
  DetailedHTMLProps,
  InputHTMLAttributes,
} from 'react'

import track from '../../app/api/analytics'
import { CONSTANTS } from '../../data-resource/constants'
import { cn, debounce } from '../../helper-functions/UtilFunctions'
import type { AnalyticsEvents } from '../../types/Analytics/Analytics.types'
import { InputFieldEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { AnalyticsObjectIds } from '../../types/Analytics/AnalyticsObjectIds.types'
import { BoxContainer } from '../common/BoxContainer'
import { GenericButton } from '../common/GenericButton'
import type { FieldProps } from '../ui/Field'
import { Field } from '../ui/Field'

const debouncedTrack = debounce(track, CONSTANTS.INPUT_DEBOUNCE_TIME)

export type InputWrapperProps = {
  inputProps?: DetailedHTMLProps<
    InputHTMLAttributes<HTMLInputElement>,
    HTMLInputElement
  >
  fieldProps?: FieldProps
  inputVal?: string
  inputClearButton?: boolean
  objectId?: AnalyticsObjectIds
  clearEvent?: AnalyticsEvents
  trackInput?: boolean
  inputOnChange?: ChangeEventHandler<HTMLInputElement>
  inputOnClear?: () => void
  inputOnClick?: () => void
}

/** `InputWrapper` - An input field component with optional tracking.
 *
 * Handles user interactions with an input field, including typing and clicking,
 * and can track these interactions for analytics purposes.
 *
 * Functionality:
 * - Executes an optional change handler when the input value changes.
 * - Tracks typing and clicking events if tracking is enabled.
 * - Renders a clear button if `inputClearButton` is true and the input is not empty.
 */
export function InputWrapper({
  fieldProps,
  inputVal,
  inputClearButton,
  inputOnChange,
  inputOnClear,
  inputOnClick,
  objectId,
  clearEvent,
  trackInput,
  inputProps,
}: InputWrapperProps) {
  const onChange = (e: ChangeEvent<HTMLInputElement>) => {
    inputOnChange?.(e)
    if (trackInput && objectId) {
      debouncedTrack({
        event: InputFieldEvent.typed,
        properties: {
          object_id: objectId,
          object_value: e.target.value,
        },
      })
    }
  }
  const onClick = () => {
    inputOnClick?.()
    if (trackInput && objectId) {
      track({
        event: InputFieldEvent.clicked,
        properties: {
          object_id: objectId,
        },
      })
    }
  }

  return (
    <Field {...fieldProps}>
      <BoxContainer className={'relative flex w-full'}>
        <input
          {...inputProps}
          value={inputVal}
          onChange={onChange}
          onClick={onClick}
          maxLength={inputProps?.maxLength ?? CONSTANTS.INPUT_MAX_LENGTH}
          className={cn(
            'focus-visible:outline-none focus-visible:ring-brand',
            inputProps?.className
          )}
        />

        {inputClearButton && inputVal !== '' && (
          <GenericButton
            className='absolute top-0 right-0 bottom-0 h-full rounded-none px-2 opacity-70 hover:bg-gray-100 hover:opacity-100'
            onClick={inputOnClear}
            objectId={trackInput ? objectId : undefined}
            objectValue={trackInput ? inputVal : undefined}
            customEvent={clearEvent}
            icon={<CloseIcon />}
            type='button'
          />
        )}
      </BoxContainer>
    </Field>
  )
}
