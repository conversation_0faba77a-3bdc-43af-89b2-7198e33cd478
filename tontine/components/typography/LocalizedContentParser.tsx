'use client'

import { useLanguage } from '../../providers/LanguageContext'
import type { PortableContentProps } from '../../types/components/PortableText.types'
import type {
  LocalizedContentType,
  LocalizedStringContentType,
} from '../../types/sections/section.types'
import { PortableTextContent } from './PortableTextContent'

type LocalizedContentWrapperProps = {
  children?: LocalizedContentType | LocalizedStringContentType
}

/**
 * A component that renders localized content based on the current language setting.
 * It supports plain text, markdown arrays, and falls back to English if the
 * desired language content is unavailable.
 *
 * If the content is a string, it is rendered as-is. If the content is an array,
 * it is passed to the `PortableTextContent` component for rendering. If no content
 * is available for the selected language, the component returns `null`.
 */
export const LocalizedContentParser = ({
  children,
  ...rest
}: LocalizedContentWrapperProps &
  Omit<PortableContentProps, 'markDownText'>) => {
  const { language } = useLanguage()
  const content = children?.[language] ?? children?.en

  if (!content) {
    return null
  }

  if (typeof content === 'string') {
    return <>{content}</>
  }

  if (Array.isArray(content)) {
    return <PortableTextContent {...rest} markDownText={content} />
  }

  return null
}
