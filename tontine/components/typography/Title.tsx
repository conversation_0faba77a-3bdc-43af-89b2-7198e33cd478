import type React from 'react'

import type {
  AdditionalComponentAttributes,
  ComponentTags,
} from '../../types/component.types'
import type {
  LocalizedContentType,
  LocalizedStringContentType,
  ParentSectionID,
} from '../../types/sections/section.types'
import { LocalizedContentParser } from './LocalizedContentParser'

export type TitleProps = {
  as?: ComponentTags['heading']
  children?: LocalizedContentType | LocalizedStringContentType
  title?: React.ReactNode
} & Omit<AdditionalComponentAttributes, 'children' | 'title'> &
  ParentSectionID

/**
 * Renders a title as a heading element, supporting both static and localized content.
 */
export function Title({
  children,
  title,
  dataTestId,
  parentSectionId,
  as = 'h4',
  ...rest
}: TitleProps) {
  const HeadingTag = as
  if (title) {
    return (
      <HeadingTag data-cy={dataTestId} {...rest}>
        {title}
      </HeadingTag>
    )
  }
  return (
    <HeadingTag data-cy={dataTestId} {...rest}>
      <LocalizedContentParser
        parentSectionId={parentSectionId}
        renderDefaultBlock={false}
        renderWrapper={false}
      >
        {children}
      </LocalizedContentParser>
    </HeadingTag>
  )
}
