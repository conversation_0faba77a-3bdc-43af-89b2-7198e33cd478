import type React from 'react'
import type { ChangeEvent } from 'react'

import track from '../../app/api/analytics'
import { CONSTANTS } from '../../data-resource/constants'
import { UI_TEST_ID } from '../../data-resource/data-test-ids'
import { cn, debounce } from '../../helper-functions/UtilFunctions'
import { InputFieldEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { AnalyticsObjectIds } from '../../types/Analytics/AnalyticsObjectIds.types'
import type { AdditionalComponentAttributes } from '../../types/common.types'
import type { FieldProps } from '../ui/Field'
import { Field } from '../ui/Field'

const debouncedTrack = debounce(track, CONSTANTS.INPUT_DEBOUNCE_TIME)

type TextareaWrapperProps = {
  fieldProps?: FieldProps
  inputOnChange: (event: React.ChangeEvent<HTMLTextAreaElement>) => void
  inputOnClick?: () => void
  objectId?: AnalyticsObjectIds
  trackInput?: boolean
} & AdditionalComponentAttributes &
  React.DetailedHTMLProps<
    React.TextareaHTMLAttributes<HTMLTextAreaElement>,
    HTMLTextAreaElement
  >

/** `TextArea` - A textarea component that captures user input and tracks interactions.
 *
 * This component renders a textarea within a field, handling user input changes and
 * clicks while optionally tracking these interactions for analytics purposes.
 */
export function TextArea({
  fieldProps,
  inputOnChange,
  inputOnClick,
  objectId,
  trackInput,
  dataTestId,
  ...rest
}: TextareaWrapperProps) {
  const onChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    inputOnChange?.(e)
    if (trackInput && objectId) {
      debouncedTrack({
        event: InputFieldEvent.typed,
        properties: {
          object_id: objectId,
          object_value: e.target.value,
        },
      })
    }
  }
  const onClick = () => {
    inputOnClick?.()
    if (trackInput && objectId) {
      track({
        event: InputFieldEvent.clicked,
        properties: {
          object_id: objectId,
        },
      })
    }
  }
  return (
    <Field {...fieldProps}>
      <textarea
        {...rest}
        className={cn(
          'focus-visible:outline-none focus-visible:ring-brand',
          rest?.className
        )}
        onChange={onChange}
        onClick={onClick}
        data-cy={dataTestId ?? UI_TEST_ID?.formTextArea}
      />
    </Field>
  )
}
