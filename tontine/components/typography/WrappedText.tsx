import type {
  AdditionalComponentAttributes,
  ComponentTags,
} from '../../types/component.types'

export type WrappedTextProps = {
  as?: ComponentTags['text']
} & AdditionalComponentAttributes

/** Renders a wrapped text element with the specified styling properties, class name, click handler, and tab index. */
export function WrappedText({
  children,
  tabIndex,
  as = 'p',
  ...rest
}: WrappedTextProps) {
  const TextTag = as

  return (
    <TextTag tabIndex={tabIndex} {...rest}>
      {children}
    </TextTag>
  )
}
