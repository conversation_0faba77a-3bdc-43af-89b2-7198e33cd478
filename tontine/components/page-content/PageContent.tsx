import { Fragment } from 'react'
import { UI_TEST_ID } from '../../data-resource/data-test-ids'
import { camelCaseString } from '../../helper-functions/UtilFunctions'
import { sections } from '../../sections/Sections'
import type { PageDomainType } from '../../types/common.types'
import type {
  AllSectionKeys,
  SectionType,
} from '../../types/sections/section.types'
import { BoxContainer } from '../common/BoxContainer'

type PageContentProps = {
  pageData?: Array<SectionType>
  pageSubtitle?: string
  pageSlug?: string
} & PageDomainType

/**
 * - `Page Content` component is a wrapper for navbar,footer and all sections of
 *   the page with data received from Sanity CMS
 */
export function PageContent({
  pageData,
  pageDomain,
  pageSlug,
}: PageContentProps) {
  return (
    <BoxContainer
      as={'main'}
      dataTestId={UI_TEST_ID?.sectionWrapper}
      className='sectionWrapper mx-auto w-full max-w-[125rem]'
    >
      {pageData?.map((section: SectionType, index) => {
        const sectionType = camelCaseString(section?._type) as AllSectionKeys
        if (!sectionType)
          return (
            <Fragment
              key={`no-section-type-${section?.sectionSlug}-${index}`}
            />
          )
        const SectionComponent = sections[sectionType]
        // following line prevents React error when no section component
        // is found
        if (!SectionComponent)
          return (
            <Fragment
              key={`no-section-component-${section?.sectionSlug}-${index}`}
            />
          )
        return (
          <SectionComponent
            sectionData={{ ...section, pageSlug }}
            pageDomain={pageDomain ?? ''}
            key={`section-found-${section?.sectionSlug}-${index}`}
          />
        )
      })}
    </BoxContainer>
  )
}
