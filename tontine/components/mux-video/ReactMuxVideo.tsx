'use client'

import MuxVideo from '@mux/mux-video-react'
import { type SyntheticEvent, useEffect, useRef } from 'react'

import track from '../../app/api/analytics'
import { CONSTANTS } from '../../data-resource/constants'
import {
  cn,
  convertToPlainText,
  replaceString,
} from '../../helper-functions/UtilFunctions'
import { logServerless } from '../../serverless/ApiUtilFunctions'
import { isCypress } from '../../serverless/keys'
import { VideoEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { AnalyticsObjectIds } from '../../types/Analytics/AnalyticsObjectIds.types'
import type {
  LocalizedContentType,
  LocalizedStringContentType,
} from '../../types/sections/section.types'
import { BoxContainer, type BoxContainerProps } from '../common/BoxContainer'

type VideoMetadata = {
  slug: string
  title?: LocalizedContentType | LocalizedStringContentType
  description?: LocalizedContentType | LocalizedStringContentType
  id: string
  duration: number
  thumbTime?: number
}

type ReactMuxVideoProps = {
  autoPlay?: boolean
  loop?: boolean
  muted?: boolean
  playbackId?: string
  isPlaying?: boolean
  videoMetadata?: VideoMetadata
  videoThumbnail: string
  hideControls?: boolean
} & BoxContainerProps

/** ReactMuxVideo is a custom video component that integrates with MuxVideo for enhanced video functionality.
 * It supports autoplay, looping, muted videos, and tracking events such as play, pause, seek, and end.
 */
function ReactMuxVideo({
  autoPlay = false,
  loop,
  muted,
  playbackId,
  videoMetadata,
  isPlaying = true,
  videoThumbnail,
  className,
  hideControls,
  ...rest
}: ReactMuxVideoProps) {
  /** Replace all occurrences of a substring in a given string with another substring. */
  const objectId: AnalyticsObjectIds = `${replaceString({
    str: videoMetadata?.slug ?? 'explainer',
    replace: '-',
    replaceWith: '_',
  })}_content_post`

  const videoRef = useRef<HTMLVideoElement>(null)

  // Handles the video pause when one video is playing and another is clicked
  useEffect(() => {
    if (videoRef.current && !isPlaying) {
      videoRef.current.pause()
    }
  }, [isPlaying])

  const handlePlay = (event: SyntheticEvent<HTMLVideoElement>) => {
    const { currentTime } = event.currentTarget
    if (autoPlay && currentTime === 0) return // Autoplay triggers a play event at 0
    track({
      event: VideoEvent.played,
      properties: {
        object_id: objectId,
      },
    })
  }

  const handlePause = (event: SyntheticEvent<HTMLVideoElement>) => {
    const { currentTime, duration } = event.currentTarget

    // When a video ends it triggers a paused event
    if (currentTime !== duration) {
      track({
        event: VideoEvent.paused,
        properties: {
          object_id: objectId,
        },
      })
    }
  }

  const handleSeek = (event: SyntheticEvent<HTMLVideoElement>) => {
    const { currentTime } = event.currentTarget

    if (autoPlay && currentTime === 0.1) return // Autoplay causes a seek event at 0.1
    track({
      event: VideoEvent.seek,
      properties: {
        object_id: objectId,
        object_value: Math.trunc(currentTime),
        description: CONSTANTS.ANALYTICS_DESCRIPTIONS.VIDEO_SEEK,
      },
    })
  }

  const handleEnded = () => {
    track({
      event: VideoEvent.finished,
      properties: {
        object_id: objectId,
      },
    })
  }

  const handleClick = () => {
    if (!isPlaying && videoRef.current) {
      videoRef.current.play().catch((error) => {
        logServerless({
          message: 'Error playing video on click',
          logLevel: 'error',
          error,
        })
      })
    }
  }

  const autoPlayVideo = hideControls ? true : autoPlay
  const showControls = hideControls ? false : isPlaying

  return (
    <BoxContainer
      {...rest}
      className={cn('aspect-video cursor-pointer', className)}
      onClick={handleClick}
    >
      <MuxVideo
        ref={videoRef}
        className='h-full w-full'
        poster={videoThumbnail}
        controls={showControls}
        playbackId={isCypress ? undefined : playbackId}
        metadata-video-title={convertToPlainText({
          value: videoMetadata?.title,
        })}
        metadata-video-id={videoMetadata?.id}
        metadata-video-description={convertToPlainText({
          value: videoMetadata?.description,
        })}
        metadata-video-duration={videoMetadata?.duration}
        metadata={{
          video_id: videoMetadata?.id,
          video_title: videoMetadata?.title,
          video_duration: videoMetadata?.duration,
          video_poster_url: videoThumbnail,
        }}
        autoPlay={isCypress ? false : autoPlayVideo}
        loop={loop}
        muted={hideControls ? true : muted}
        preload='metadata'
        onPlay={handlePlay}
        onPause={handlePause}
        onSeeked={handleSeek}
        onEnded={handleEnded}
      />
      {rest?.children}
    </BoxContainer>
  )
}

export { ReactMuxVideo }
