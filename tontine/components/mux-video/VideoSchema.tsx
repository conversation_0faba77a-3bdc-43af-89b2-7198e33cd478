import { UI_TEST_ID } from '../../data-resource/data-test-ids'
import {
  generateVideoSchema,
  getAttribute,
} from '../../helper-functions/UtilFunctions'
import type { VideoMetadataType } from '../../types/common.types'

export const VideoSchema = ({ video, ...rest }: VideoMetadataType) => {
  const videoProps = generateVideoSchema({ video, ...rest })

  return (
    <script
      id={video?.playbackId}
      type='application/ld+json'
      // biome-ignore lint/security/noDangerouslySetInnerHtml: <Need to do this>
      dangerouslySetInnerHTML={{ __html: JSON.stringify(videoProps) }}
      {...getAttribute(
        Boolean(UI_TEST_ID?.webschemaScript),
        'data-cy',
        UI_TEST_ID?.webschemaScript
      )}
    />
  )
}
