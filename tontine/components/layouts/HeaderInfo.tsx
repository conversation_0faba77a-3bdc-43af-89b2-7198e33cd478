import { cn } from '../../helper-functions/UtilFunctions'
import type { PortableContentProps } from '../../types/components/PortableText.types'
import type {
  LocalizedContentHeader,
  ParentSectionID,
} from '../../types/sections/section.types'
import type { BoxContainerProps } from '../common/BoxContainer'
import { BoxContainer } from '../common/BoxContainer'
import type { SanityImageProps } from '../common/SanityImage'
import { LocalizedContentParser } from '../typography/LocalizedContentParser'
import type { TitleProps } from '../typography/Title'
import { Title } from '../typography/Title'

type HeaderInfoProps = {
  titleProps?: Omit<TitleProps, 'children'>
  subtitleProps?: Omit<PortableContentProps, 'markDownText'>
  imageProps?: SanityImageProps
} & Omit<BoxContainerProps, 'title' | 'children'> &
  ParentSectionID &
  LocalizedContentHeader

/**
 * A reusable component for rendering a header with an icon and a title.
 *
 * It renders an icon if the `icon` prop is provided, followed by a title in
 * an `h2` element, followed by the `subtitle` in an `h3` element. The `icon` is
 * rendered in a `BoxContainer` with a relative position and a width and height
 * that are set based on the size of the icon. The `title` and `subtitle` are
 * rendered in `Title` and `PortableTextContent` components, respectively, with
 * styling that is set based on the `titleStyling` and `subtitleStyling` objects.
 *
 */
export const HeaderInfo = ({
  title,
  subtitle,
  parentSectionId,
  titleProps,
  subtitleProps,
  ...rest
}: HeaderInfoProps) => {
  return (
    <BoxContainer
      {...rest}
      className={cn(
        'flex w-full flex-col items-center justify-center gap-1 py-4 md:mb-6',
        rest?.className
      )}
    >
      <Title
        as='h3'
        className={cn(
          'section heading mx-6 font-bold text-3xl sm:text-4xl smd:text-5xl md:text-6xl lg:text-5xl xl:text-5xl',
          titleProps?.className
        )}
      >
        {title}
      </Title>
      <LocalizedContentParser
        parentSectionId={parentSectionId}
        as='div'
        {...subtitleProps}
        overrideElements={{
          p: {
            className: cn(
              'section sub-heading m-0 w-fit text-brand text-lg leading-10 md:text-xl xl:text-2xl',
              subtitleProps?.className
            ),
          },
        }}
      >
        {subtitle}
      </LocalizedContentParser>
    </BoxContainer>
  )
}
