import strings from '../../data-resource/strings.json'
import { generatePathWithTrailingSlash } from '../../helper-functions/UtilFunctions'
import { ContentPostHeader } from '../../sections/ContentPost/ContentPostHeader'
import { PdfEmbed } from '../../sections/ContentPost/PdfEmbed'
import type { ContentPostProps } from '../../types/sections/content-section.types'
import { LocalizedContentParser } from '../typography/LocalizedContentParser'

/** The `ContentPostLayout` component is used to render the content post layout.
 * The content post layout is a layout that contains the header, body, and footer of a content post.
 * The header contains the post title, summary, authors, post date, read time, featured post, and post image.
 * The body contains the post body markdown content.
 * The footer contains an optional pdf embed for research pdf files.
 */
export const ContentPostLayout = ({
  postTitle,
  postSummary,
  authors,
  videoId,
  postBodyMarkdown,
  postDate,
  postReadTime,
  postImage,
  videoThumbnail,
  videoDuration,
  researchPdfFile,
  featuredPost,
  slug,
  parentSlug,
  parentSectionId,
  children,
}: ContentPostProps) => {
  return (
    <>
      <ContentPostHeader
        postTitle={postTitle}
        postSummary={postSummary}
        authors={authors}
        postDate={postDate}
        postReadTime={postReadTime}
        featuredPost={featuredPost}
        postImage={postImage}
        videoThumbnail={videoThumbnail}
        videoId={videoId}
        videoDuration={videoDuration}
        slug={slug}
        parentSlug={parentSlug}
        parentSectionId={parentSectionId}
      >
        {children}
      </ContentPostHeader>

      {postBodyMarkdown && (
        <LocalizedContentParser
          renderWrapper
          className='mt-5 mb-15 grid gap-3 text-left text-fg text-xl content-block'
        >
          {postBodyMarkdown}
        </LocalizedContentParser>
      )}
      {researchPdfFile && slug && (
        <PdfEmbed
          postSlug={slug}
          researchPdfFile={generatePathWithTrailingSlash({
            segments: [
              parentSlug ?? strings.RESEARCH_PDF_PATH,
              slug,
              researchPdfFile,
            ],
            leadingSlash: true,
          })}
        />
      )}
      {!videoId && children}
    </>
  )
}
