import dynamic from 'next/dynamic'

import { isProd, turnstileSiteKey } from '../../serverless/keys'

const Turnstile = dynamic(() => import('react-turnstile'), { ssr: false })

/** A wrapper around the react-turnstile component that provides a default site key for
 * production environments and a "fail" site key for development environments.
 */
export const NextTurnstile = ({
  onError,
  onExpire,
  onLoad,
  onVerify,
  onSuccess,
  onTimeout,
}: {
  onError: () => void
  onExpire: () => void
  onLoad?: () => void
  onVerify?: () => void
  onSuccess: () => void
  onTimeout: () => void
}) => {
  const siteKey = isProd ? turnstileSiteKey : '1x00000000000000000000AA' // Always fails in non-production

  return (
    <Turnstile
      sitekey={siteKey}
      onError={onError}
      onExpire={onExpire}
      onLoad={onLoad}
      onVerify={onVerify}
      onSuccess={onSuccess}
      onTimeout={onTimeout}
      refreshExpired='never' // auto/manual for interactive captcha
      retry='never' // auto for retries, with refresh
      theme='light' // auto for dark mode
      appearance='interaction-only' // always to show widget
      size='invisible' // flexible to show widget, with appearance
      className='pointer-events-none absolute h-0 grow overflow-hidden'
    />
  )
}

/** --- Site keys ---

Always passes

NEXT_PUBLIC_TURNSTILE_SITE_KEY=1x00000000000000000000AA

Always Blocks

NEXT_PUBLIC_TURNSTILE_SITE_KEY=2x00000000000000000000AB

Forces interactive challenge

NEXT_PUBLIC_TURNSTILE_SITE_KEY=3x00000000000000000000FF

 */
