'use client'

import React from 'react'

import strings from '../../data-resource/strings.json'
import type { WebSchemasWebsiteWideProps } from '../../types/common.types'
import type {
  WebSchemaCorporationProps,
  WebSchemaInvestmentOrDepositProps,
} from '../../types/webschema.types'
import { InvestmentOrDepositWebschema } from './InvestmentOrDepositWebschema'
import { OrganizationWebschema } from './OrganizationWebschema'

/**
 * Component which contains all website wide schemas and renders them.
 */
export function WebschemasWebsiteWide({
  webSchemaData,
}: {
  webSchemaData: Array<WebSchemasWebsiteWideProps>
}) {
  return (
    <>
      {webSchemaData?.map((schema, index: number) => {
        if (schema?._type?.includes(strings.INVESTMENT_OR_DEPOSIT_SCHEMA)) {
          const investmentOrDepositSchema =
            schema as WebSchemaInvestmentOrDepositProps
          return (
            <InvestmentOrDepositWebschema
              webSchemaInvestmentOrDeposit={investmentOrDepositSchema}
              key={`webSchema-${investmentOrDepositSchema?.investmentOrDepositSchemaName}-${index}`}
            />
          )
        }
        if (schema?._type?.includes(strings.CORPORATION_SCHEMA)) {
          const corporationSchema = schema as WebSchemaCorporationProps
          return (
            <OrganizationWebschema
              webSchemaCorporation={corporationSchema}
              key={`webSchema-${corporationSchema?.corporationSchemaName}-${index}`}
            />
          )
        }
        return <React.Fragment key={`no-corp-invest-webSchema-${index}`} />
      })}
    </>
  )
}
