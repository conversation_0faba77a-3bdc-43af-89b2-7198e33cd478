import Script from 'next/script'
import type { Organization, WithContext } from 'schema-dts'

import { UI_TEST_ID } from '../../data-resource/data-test-ids'
import { getAttribute } from '../../helper-functions/UtilFunctions'
import type { WebSchemaCorporationProps } from '../../types/webschema.types'

/**
 * CorporationWebschema is a component that renders the JSON-LD schema for the website.
 * It is used to provide information about the website to search engines.
 */
export function OrganizationWebschema({
  webSchemaCorporation,
}: {
  webSchemaCorporation: WebSchemaCorporationProps
}) {
  // setting up the JSON-LD schema
  const jsonLd: WithContext<Organization> = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: webSchemaCorporation?.corporationSchemaName,
    description: webSchemaCorporation?.corporationSchemaDescription,
    actionableFeedbackPolicy:
      webSchemaCorporation?.corporationSchemaActionableFeedbackPolicy,
    address: {
      '@type': 'PostalAddress',
      streetAddress: webSchemaCorporation?.corporationSchemaStreetAddress,
      addressLocality: webSchemaCorporation?.corporationSchemaAddressLocality,
      addressRegion: webSchemaCorporation?.corporationSchemaAddressRegion,
      postalCode: webSchemaCorporation?.corporationSchemaPostalCode,
      addressCountry: webSchemaCorporation?.corporationSchemaAddressCountry,
    },
    correctionsPolicy: webSchemaCorporation?.corporationSchemaCorrectionsPolicy,
    diversityPolicy: webSchemaCorporation?.corporationSchemaDiversityPolicy,
    diversityStaffingReport:
      webSchemaCorporation?.corporationSchemaDiversityStaffingReport,
    duns: webSchemaCorporation?.corporationSchemaDuns,
    email: webSchemaCorporation?.corporationSchemaEmail,
    ethicsPolicy: webSchemaCorporation?.corporationSchemaEthicsPolicy,
    faxNumber: webSchemaCorporation?.corporationSchemaFaxNumber,
    founder: [
      {
        '@type': 'Person',
        name: webSchemaCorporation?.corporationSchemaFounder,
      },
    ],
    foundingDate: webSchemaCorporation?.corporationSchemaFoundingDate,
    foundingLocation: {
      '@type': 'Place',
      address: {
        '@type': 'PostalAddress',
        addressLocality:
          webSchemaCorporation?.corporationSchemaFoundingAddressLocality,
        addressRegion:
          webSchemaCorporation?.corporationSchemaFoundingAddressRegion,
        postalCode: webSchemaCorporation?.corporationSchemaFoundingPostalCode,
        addressCountry:
          webSchemaCorporation?.corporationSchemaFoundingAddressCountry,
      },
    },
    globalLocationNumber:
      webSchemaCorporation?.corporationSchemaGlobalLocationNumber,
    keywords: webSchemaCorporation?.corporationSchemaKeywords?.map(
      (keyword: string) => keyword
    ),
    knowsAbout: webSchemaCorporation?.corporationSchemaKnowsAbout,
    knowsLanguage: webSchemaCorporation?.corporationSchemaKnowsLanguage,
    legalName: webSchemaCorporation?.corporationSchemaLegalName,
    leiCode: webSchemaCorporation?.corporationSchemaLeiCode,
    logo: {
      '@type': 'ImageObject',
      url: webSchemaCorporation?.corporationSchemaLogo,
    },
    numberOfEmployees: webSchemaCorporation?.corporationSchemaNumberOfEmployees,
    publishingPrinciples:
      webSchemaCorporation?.corporationSchemaPublishingPrinciples,
    slogan: webSchemaCorporation?.corporationSchemaSlogan,
    taxID: webSchemaCorporation?.corporationSchemaTaxID,
    telephone: webSchemaCorporation?.corporationSchemaTelephone,
    vatID: webSchemaCorporation?.corporationSchemaVatID,
    alternateName: webSchemaCorporation?.corporationSchemaAlternateName,
    url: webSchemaCorporation?.corporationSchemaUrl,
  }

  // converting the JSON-LD schema to a string
  const jsonLdString: string = JSON.stringify(jsonLd)
  return (
    <Script
      id='organization-webschema'
      type='application/ld+json'
      // biome-ignore lint/security/noDangerouslySetInnerHtml: <Need to do this>
      dangerouslySetInnerHTML={{ __html: jsonLdString }}
      {...getAttribute(
        Boolean(UI_TEST_ID?.organizationSchema),
        'data-cy',
        UI_TEST_ID?.organizationSchema
      )}
    />
  )
}
