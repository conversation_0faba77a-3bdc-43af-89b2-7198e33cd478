import type { FAQPage, WithContext } from 'schema-dts'

import { UI_TEST_ID } from '../../data-resource/data-test-ids'
import { getAttribute } from '../../helper-functions/UtilFunctions'
import type { FaqCategory } from '../../types/sections/faq-section.types'

type FAQsWebschemaProps = {
  faqCategories: Array<FaqCategory>
}
/**
 * FAQsWebschema is a component that renders the JSON-LD FAQs schema for the website FAQs page.
 * It is used to provide information about the website to search engines.
 */
export function FAQsWebschema({ faqCategories }: FAQsWebschemaProps) {
  // setting up the JSON-LD schema
  const allQuestions =
    faqCategories?.length > 0 ? faqCategories.flatMap((e) => e?.faqData) : []
  const jsonLd: WithContext<FAQPage> = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: allQuestions?.map((question) => {
      return {
        '@type': 'Question',
        name: question?.question,
        acceptedAnswer: {
          '@type': 'Answer',
          text: question?.answer,
        },
        keywords: question?.tags,
      }
    }),
  } as WithContext<FAQPage>
  // converting the JSON-LD schema to a string
  const jsonLdString: string = JSON.stringify(jsonLd)
  return (
    <script
      id='faq-schema'
      type='application/ld+json'
      // biome-ignore lint/security/noDangerouslySetInnerHtml: <Need to do this>
      dangerouslySetInnerHTML={{ __html: jsonLdString }}
      {...getAttribute(
        Boolean(UI_TEST_ID?.webschemaScript),
        'data-cy',
        UI_TEST_ID?.webschemaScript
      )}
    />
  )
}
