import Script from 'next/script'
import type { InvestmentOrDeposit, WithContext } from 'schema-dts'

import { UI_TEST_ID } from '../../data-resource/data-test-ids'
import { getAttribute } from '../../helper-functions/UtilFunctions'
import type { WebSchemaInvestmentOrDepositProps } from '../../types/webschema.types'

/**
 * InvestmentOrDepositWebschema is a component that renders the JSON-LD schema for the website.
 * It is used to provide information about the website to search engines.
 */
export function InvestmentOrDepositWebschema({
  webSchemaInvestmentOrDeposit,
}: {
  webSchemaInvestmentOrDeposit: WebSchemaInvestmentOrDepositProps
}) {
  // setting up the JSON-LD schema
  const jsonLd: WithContext<InvestmentOrDeposit> = {
    '@context': 'https://schema.org',
    '@type': 'InvestmentOrDeposit',
    name: webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaName,
    description:
      webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaDescription,
    amount: {
      '@type': 'MonetaryAmount',
      currency: 'USD',
      minValue: webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaMinValue,
      maxValue: webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaMaxValue,
    },
    annualPercentageRate:
      webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaAnnualPercentageRate,
    feesAndCommissionsSpecification:
      webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaFeesAndCommissionsSpecification,
    interestRate:
      webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaInterestRate,
    areaServed:
      webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaAreaServed,
    audience: {
      '@type': 'Audience',
      audienceType:
        webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaAudienceType,
    },
    award: webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaAward,
    brand: {
      '@type': 'Brand',
      name: webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaBrandName,
      url: webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaBrandUrl,
    },
    category: webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaCategory,
    logo: {
      '@type': 'ImageObject',
      url: webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaLogoUrl,
    },
    providerMobility:
      webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaProviderMobility,
    termsOfService:
      webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaTermsOfService,
    alternateName:
      webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaAlternateName,
    url: webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaUrl,
    sameAs: webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaSameAs.map(
      (url) => url
    ),
    slogan: webSchemaInvestmentOrDeposit?.investmentOrDepositSchemaSlogan,
  }

  // converting the JSON-LD schema to a string
  const jsonLdString: string = JSON.stringify(jsonLd)
  return (
    <Script
      id='investment-webschema'
      type='application/ld+json'
      // biome-ignore lint/security/noDangerouslySetInnerHtml: <Need to do this>
      dangerouslySetInnerHTML={{ __html: jsonLdString }}
      {...getAttribute(
        Boolean(UI_TEST_ID?.investmentSchema),
        'data-cy',
        UI_TEST_ID?.investmentSchema
      )}
    />
  )
}
