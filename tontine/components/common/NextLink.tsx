'use client'

import { LaunchIcon } from '@sanity/icons'
import Link from 'next/link'

import { UI_TEST_ID } from '../../data-resource/data-test-ids'
import {
  cn,
  externalLinkAttributes,
  formatHref,
  replaceString,
} from '../../helper-functions/UtilFunctions'
import { useLinkTrackEvent } from '../../hooks/useLinkTracking'
import { useLanguage } from '../../providers/LanguageContext'
import type { AnalyticsObjectIds } from '../../types/Analytics/AnalyticsObjectIds.types'
import type { NextLinkProps } from '../../types/components/NextLink.types'

/** General link component with optional click and hover tracking */
export function NextLink({
  href,
  children,
  customEvent,
  customValue,
  objectId,
  linkLabel,
  trackHover,
  customHoverEvent,
  dataTestId,
  onClick,
  hideExternalIcon,
  externalIconProps,
  skipLocalization,
  ...rest
}: NextLinkProps) {
  const { language } = useLanguage()

  const validObjectId = objectId?.includes('-')
    ? (replaceString({
        str: objectId,
        replace: '-',
        replaceWith: '_',
      }) as AnalyticsObjectIds)
    : objectId

  const triggerClickEvent = useLinkTrackEvent({
    href: href ?? '/',
    objectId: validObjectId,
    customEvent,
    customValue,
    linkLabel,
    onClick,
  })

  const triggerHoverEvent = useLinkTrackEvent({
    href: href ?? '/',
    objectId: validObjectId,
    customEvent: customHoverEvent,
    customValue,
    linkLabel,
    trackHover,
  })

  const handleClick = objectId ? triggerClickEvent : undefined
  const onHover = objectId && trackHover ? triggerHoverEvent : undefined

  const securityAttributes = externalLinkAttributes(href)

  const resolvedHref = formatHref(href, language, skipLocalization)

  return (
    <Link
      href={resolvedHref}
      {...securityAttributes}
      onClick={handleClick}
      onMouseOver={onHover}
      data-cy={dataTestId ?? UI_TEST_ID?.nextLink}
      {...rest}
      className={cn(
        securityAttributes &&
          !hideExternalIcon &&
          'inline-flex items-center justify-center gap-1',
        rest.className
      )}
    >
      {children}
      {securityAttributes && !hideExternalIcon && (
        <LaunchIcon {...externalIconProps} />
      )}
    </Link>
  )
}
