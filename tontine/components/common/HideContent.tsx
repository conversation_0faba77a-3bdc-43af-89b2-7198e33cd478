'use client'

import type { ReactNode } from 'react'
import { useHideContent } from '../../hooks/useHideContent'

/**
 * HideContent is a wrapper component that takes a single child
 * (which can be any ReactNode) and conditionally renders it based
 * on whether the hideContent query parameter is set in the URL.
 *
 * We do this to prevent NEXT.js error
 * https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout
 */
export const HideContent = ({ children }: { children: ReactNode }) => {
  const hideContent = useHideContent()

  return <>{!hideContent && children}</>
}
