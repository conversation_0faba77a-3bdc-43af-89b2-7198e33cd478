'use client'

import track from '../../app/api/analytics'
import { cn, getAttribute } from '../../helper-functions/UtilFunctions'
import type { AnalyticsEvents } from '../../types/Analytics/Analytics.types'
import { ButtonEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { AnalyticsObjectIds } from '../../types/Analytics/AnalyticsObjectIds.types'
import type { ButtonType } from '../../types/common.types'
import type { AdditionalComponentAttributes } from '../../types/component.types'
import { LoadingSpinner } from '../ui/LoadingSpinner'

export type GenericButtonProps = {
  label?: string
  onClick?: React.MouseEventHandler<HTMLButtonElement>
  type?: ButtonType
  isLoading?: boolean
  isActive?: boolean
  icon?: React.ReactNode
  objectId?: AnalyticsObjectIds
  objectValue?: unknown
  customEvent?: AnalyticsEvents
} & AdditionalComponentAttributes

/** `GenericButton` - A flexible button with native event tracking.
 * This component handles button interactions and tracks user actions for analytics purposes.
 */
export const GenericButton = ({
  label,
  onClick,
  children,
  objectId,
  objectValue,
  customEvent,
  className,
  disabled,
  isActive,
  isLoading,
  dataTestId,
  icon,
  ...rest
}: GenericButtonProps) => {
  const triggerEvent: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    onClick?.(e)
    track({
      event: customEvent ?? ButtonEvent.clicked,
      properties: {
        ...getAttribute(Boolean(objectValue), 'object_value', objectValue),
        ...getAttribute(Boolean(objectId), 'object_id', objectId),
        ...getAttribute(Boolean(label), 'label', label),
      },
    })
  }

  return (
    <button
      type={'button'}
      {...rest}
      onClick={onClick || objectId || objectValue ? triggerEvent : undefined}
      {...getAttribute(Boolean(isActive), 'data-active', isActive)}
      disabled={disabled ?? isLoading}
      data-cy={dataTestId}
      className={cn(
        'relative overflow-hidden',
        className,
        isLoading && 'pointer-events-none',
        disabled && 'select-none'
      )}
    >
      {isLoading && <LoadingSpinner />}
      {label || children}
      {icon && icon}
    </button>
  )
}
