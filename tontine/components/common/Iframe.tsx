'use client'

import type { IframeHTMLAttributes } from 'react'
import { forwardRef } from 'react'

export type IframeProps = IframeHTMLAttributes<HTMLIFrameElement>

/** A utility component for rendering an `<iframe>`
 *
 * TODO: Remove forwardRef in the future, as it will be no longer needed in React 19
 */
export const Iframe = forwardRef<HTMLIFrameElement, IframeProps>(
  function Iframe({ title, ...rest }, ref) {
    return <iframe ref={ref} title={title} {...rest} />
  }
)
