'use client'

import Image, { type ImageProps } from 'next/image'
import { useState } from 'react'

import { CONSTANTS } from '../../data-resource/constants'
import { cn, getImagePath } from '../../helper-functions/UtilFunctions'
import { isCypress } from '../../serverless/keys'
import type { ImageObjectFitType } from '../../types/common.types'
import { Skeleton } from '../ui/skeleton/Skeleton'
import type { BoxContainerProps } from './BoxContainer'

export type SanityImageProps = {
  alt?: string
  src?: string
  className?: string
  fillProp?: boolean
  priority?: boolean
  objectFitProp?: ImageObjectFitType | undefined
  objectPositionProp?: string
  skeletonProps?: BoxContainerProps
  cornerRadius?: number
  rounded?: number
} & Omit<ImageProps, 'src' | 'alt'>

/**
 * Image quality ranges from 1-100. 100 is the highest quality and takes up more
 * storage
 */
const IMAGE_QUALITY = CONSTANTS.IMAGE_QUALITY.MINIMUM

/** `SanityImage` - Renders an image with optional skeleton loading and custom styling.
 *
 * Handles the display of images with support for lazy loading, skeleton loading state,
 * and various styling options.
 *
 * Functionality:
 * - Shows a skeleton loader while the image is loading by default (optional).
 * - Renders the image with specified styles and properties.
 * - Supports lazy loading and priority loading.
 * - Allows customization of object fit and position.
 */
export function SanityImage({
  alt,
  src,
  className,
  fillProp,
  priority,
  objectPositionProp,
  objectFitProp,
  skeletonProps,
  cornerRadius,
  rounded,
  ...rest
}: SanityImageProps) {
  const [isLoading, setIsLoading] = useState(true)

  return (
    <>
      {!isCypress && src ? (
        <Skeleton
          loading={isLoading}
          {...skeletonProps}
          className={cn(
            `relative z-0 h-full w-full ${!isLoading && 'rounded-none'}`,
            priority && 'before:duration-1000',
            skeletonProps?.className,
            rounded && cornerRadius && 'roundedImage'
          )}
          style={{
            '--cornerRadius': rounded ? `${cornerRadius}%` : '0',
          }}
        >
          <Image
            {...rest}
            className={cn(
              className,
              `object-${
                objectFitProp ?? 'contain'
                // biome-ignore lint/nursery/useSortedClasses: <explanation>
              } object-${objectPositionProp ?? 'center'}`
            )}
            quality={IMAGE_QUALITY}
            alt={alt || 'Data unavailable'}
            src={getImagePath({ imagePath: src })}
            fill={Boolean(fillProp)}
            priority={priority}
            onLoad={() => setIsLoading(false)}
          />
        </Skeleton>
      ) : (
        <div />
      )}
    </>
  )
}
