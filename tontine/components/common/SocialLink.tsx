import type { AnalyticsEvents } from '../../types/Analytics/Analytics.types'
import type { ShareIdChunk } from '../../types/Analytics/AnalyticsObjectIds.types'
import type { SanityImageType } from '../../types/common.types'
import type { ParentSectionID } from '../../types/sections/section.types'
import { BoxContainer } from './BoxContainer'
import { NextLink } from './NextLink'
import { SanityImage } from './SanityImage'

type SocialLinkProps = {
  img: SanityImageType
  href: string
  title?: ShareIdChunk
  customEvent?: AnalyticsEvents
} & ParentSectionID

/**
 * SocialLink is used to wrap social media icons
 */
export function SocialLink({
  img,
  href,
  title,
  parentSectionId,
  customEvent,
}: SocialLinkProps) {
  return (
    <BoxContainer
      as={'li'}
      className='flex h-12 w-12 opacity-85 duration-200 hover:opacity-100'
    >
      <NextLink
        href={href}
        className='p-3'
        aria-label={title ?? img?.alt}
        objectId={title ?? parentSectionId}
        customEvent={title && customEvent ? customEvent : undefined}
        hideExternalIcon
      >
        <SanityImage
          fillProp
          {...img}
          skeletonProps={{ className: 'h-6 w-6' }}
        />
      </NextLink>
    </BoxContainer>
  )
}
