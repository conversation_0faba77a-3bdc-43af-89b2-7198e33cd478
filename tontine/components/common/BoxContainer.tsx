import { cn, getAttribute } from '../../helper-functions/UtilFunctions'
import type {
  AdditionalComponentAttributes,
  BoxContainerTags,
} from '../../types/component.types'

export type BoxContainerProps = AdditionalComponentAttributes & {
  as?: BoxContainerTags
}

/** A utility component for wrapping arbitrary content in a layout element (eg. div, section, list). */
export const BoxContainer = ({
  children,
  className,
  dataTestId,
  disabled,
  as = 'div',
  ...rest
}: BoxContainerProps) => {
  const BoxTag = as

  return (
    <BoxTag
      className={cn(className, {
        'pointer-events-none': disabled,
        'cursor-default': disabled,
      })}
      data-cy={dataTestId}
      {...rest}
      {...getAttribute(Boolean(disabled), 'tabIndex', disabled ? -1 : 0)}
      aria-disabled={disabled}
      data-disabled={disabled}
    >
      {children}
    </BoxTag>
  )
}
