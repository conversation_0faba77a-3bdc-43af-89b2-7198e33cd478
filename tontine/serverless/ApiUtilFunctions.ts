import { captureException } from '@sentry/nextjs'
import { AxiosError, isCancel } from 'axios'

import strings from '../data-resource/strings.json'
import type { LogServerlessParams } from './serverless.types'

const tontineLog = {
  warn: {
    logTag: 'TONTINE_WARN:',
    console: console.warn,
  },
  info: {
    logTag: 'TONTINE_INFO:',
    console: console.info,
  },
  error: {
    logTag: 'TONTINE_ERROR:',
    console: console.error,
  },
  success: {
    logTag: 'TONTINE_SUCCESS:',
    console: console.log,
  },
} as const

/**
 * Filters the stack trace to only include lines up to (but not including) the first line that contains 'tontine-websites/node_modules' as to filter out useless next stuff in the error stack trace.
 * - `stack` The original stack trace.
 */
function filterStackTrace(stack: string) {
  const lines = stack.split('\n').map((line) => line.trim())
  const endIndex = lines.findIndex((line) =>
    line.includes(strings.ERROR_STACK_CUTOUT)
  )
  return endIndex > 0
    ? `\n|-| ${lines.slice(1, endIndex).join('\n|-| ')}`
    : stack
}

/**
 * Logs a message with the specified log level.
 */
function logServerless({ message, logLevel, error }: LogServerlessParams) {
  const logMethod = tontineLog[logLevel]
  if (error && logLevel === 'error') {
    if (error instanceof AxiosError) {
      logMethod.console(
        `${logMethod.logTag} ${message}, ${error.name}:`,
        error.response?.status ?? 500,
        error.response?.data ?? error.message
      )
    } else if (error instanceof Error) {
      logMethod.console(
        `${logMethod.logTag} ${message}`,
        error.message,
        error.stack ? filterStackTrace(error.stack) : ''
      )
    } else {
      logMethod.console(`${logMethod.logTag} ${message}`, error)
    }
    if (!isCancel(error)) {
      captureException(error)
    }
  } else {
    logMethod.console(`${logMethod.logTag} ${message}`)
  }
}

/**
 * Returns an error message object with status code 400 and a JSON stringified body containing a message indicating that the specified variable is not found in the environment variables.
 */
function getDailyRebuildErrorMessage(variableName: string) {
  logServerless({
    message: `${variableName} not specified in environment variables, skipping rebuild`,
    logLevel: 'error',
  })
  return {
    statusCode: 400,
  }
}

export { getDailyRebuildErrorMessage, logServerless }
