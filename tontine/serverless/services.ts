import type { AxiosResponse } from 'axios'

import { getAttribute } from '../helper-functions/UtilFunctions'
import { axiosRequest } from '../hooks/Axios/useAxios'
import { API } from './API'
import { logServerless } from './ApiUtilFunctions'
import { oneSignalRestAPIKey } from './keys'
import type {
  ContactUsBodyType,
  ContactUsHeadersType,
  EmailContent,
} from './serverless.types'

const oneSignalAuthorization = `Basic ${oneSignalRestAPIKey}`

/**
 * Adds an user to the OneSignal users list
 */
const addNewUser = async ({
  email,
  firstName,
  lastName,
  selectedOption,
  message,
  country,
  lat,
  long,
  timezone,
}: ContactUsBodyType & ContactUsHeadersType) => {
  try {
    const contactPayload = {
      subscriptions: [
        {
          type: 'Email',
          token: email,
          enabled: true,
        },
      ],
      properties: {
        ...getAttribute(Boolean(country), 'country', country),
        ...getAttribute(Boolean(lat), 'lat', Number(lat)),
        ...getAttribute(Boolean(long), 'long', Number(long)),
        ...getAttribute(Boolean(timezone), 'timezone', timezone),
        tags: {
          first_name: firstName,
          last_name: lastName,
          ...getAttribute(
            Boolean(selectedOption),
            'contact_us_selected_option',
            selectedOption
          ),
          ...getAttribute(Boolean(message), 'contact_us_message', message),
        },
      },
    }

    logServerless({
      message: `Adding user: ${email} to OneSignal`,
      logLevel: 'info',
    })

    const response = await axiosRequest({
      url: API.createUser,
      config: {
        method: 'post',
        data: contactPayload,
        headers: {
          Authorization: oneSignalAuthorization,
        },
      },
    })

    logServerless({
      message: `Added user to OneSignal with status: ${response?.status} - ${response?.statusText}`,
      logLevel: 'success',
    })
    return response
  } catch (error) {
    logServerless({
      message: `addNewUser() - Could not add user: ${email}`,
      logLevel: 'error',
      error,
    })
    throw error
  }
}

/**
 * Sends an email using the OneSignal API.
 *
 * This function sends a POST request to the OneSignal API using the provided email data.
 * The response status and message are logged on success, and any errors encountered
 * during the process are also logged
 */
const sendEmail = async (emailData: EmailContent): Promise<AxiosResponse> => {
  try {
    const response = await axiosRequest({
      url: API.sendEmail,
      config: {
        method: 'post',
        data: emailData,
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          Authorization: `Basic ${oneSignalRestAPIKey}`,
        },
      },
    })
    logServerless({
      message: `Sent email to ${emailData?.include_email_tokens?.[0]} with status: ${response?.status} - ${response?.statusText}`,
      logLevel: 'success',
    })
    return response
  } catch (error) {
    logServerless({
      message: 'sendEmail() - Failed to send email',
      logLevel: 'error',
      error,
    })
    throw error
  }
}

export { addNewUser, sendEmail }
