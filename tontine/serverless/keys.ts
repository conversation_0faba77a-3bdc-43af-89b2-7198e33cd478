import { CONSTANTS } from '../data-resource/constants'

// Environment Type
const buildContext:
  | 'development'
  | 'staging'
  | 'production'
  | 'deploy_preview' =
  (process.env.NEXT_PUBLIC_ENVIRONMENT_TYPE as
    | 'development'
    | 'staging'
    | 'production'
    | 'deploy_preview') ?? 'development'
const isProd = buildContext === 'production'
const isCypress = process.env.NEXT_PUBLIC_CYPRESS_RUN
const isDev = process.env.NODE_ENV === 'development'
const isBuild = process.env.BUILD_ENV === 'true'

// OneSignal
const oneSignalRestAPIKey = process.env.ONESIGNAL_REST_API_KEY
const defaultRecipientMail =
  process.env.NEXT_PUBLIC_DEFAULT_RECIPIENT_EMAIL ?? ''
const oneSignalAppId = process.env.ONESIGNAL_REST_APP_ID ?? ''

// OneSignal Templates
const contactUsTemplateId = process.env.ONESIGNAL_CONTACT_US_TEMPLATE_ID ?? ''
const defaultRecipientTemplateId =
  process.env.ONESIGNAL_CONTACT_US_DEFAULT_TEMPLATE_ID ?? ''

// Turnstile
const turnstileSiteKey = process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY ?? ''
const turnstileSecret = process.env.TURNSTILE_SECRET_KEY ?? ''

// Netlify
const heatMapsId = process.env.NEXT_PUBLIC_NETLIFY_SMARTLOOK_HEATMAPS

// Sentry
const sentryDSN = process.env.NEXT_PUBLIC_SENTRY_DSN
const sentryEnabled = Boolean(process.env.NEXT_PUBLIC_SENTRY_ENABLED)
const sentryDebug = Boolean(process.env.NEXT_PUBLIC_SENTRY_DEBUG)
const sentrySampleRate = Number(process.env.NEXT_PUBLIC_SENTRY_SAMPLE_RATE) || 1

// Tracking
const shouldTrack = false // Combined constant for ease of use
const mixpanelToken = process.env.NEXT_PUBLIC_MIX_PANEL_API_KEY ?? ''
const mixpanelProxyDomain = process.env.NEXT_PUBLIC_MIXPANEL_PROXY_DOMAIN

// Website ID
const prodWebsiteID = process.env.NEXT_PUBLIC_WEBSITE_ID
const defaultWebsiteId = 'a9fa5cf2-3b17-4181-899f-733b3f8f5513'
const cookieWebsiteId = 'websiteId'
const cookieLanguage = 'language'
const websiteDomain =
  process.env.NETLIFY_WEBSITE_DOMAIN ?? CONSTANTS.DEFAULT_DOMAIN

export {
  buildContext,
  contactUsTemplateId,
  cookieLanguage,
  cookieWebsiteId,
  defaultRecipientMail,
  defaultRecipientTemplateId,
  defaultWebsiteId,
  heatMapsId,
  isBuild,
  isCypress,
  isDev,
  isProd,
  mixpanelProxyDomain,
  mixpanelToken,
  oneSignalAppId,
  oneSignalRestAPIKey,
  prodWebsiteID,
  sentryDebug,
  sentryDSN,
  sentryEnabled,
  sentrySampleRate,
  shouldTrack,
  turnstileSecret,
  turnstileSiteKey,
  websiteDomain,
}
