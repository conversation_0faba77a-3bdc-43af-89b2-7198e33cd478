import { isProd, oneSignalAppId } from './keys'

const ROBO_ACTUARY_URLS = {
  STAGING: 'https://staging-api.mytontine.com',
  PROD: 'https://api.mytontine.com',
} as const

const API_COMMON = {
  contactForm: '/api/oneSignal',
  verifyTurnstile: '/api/verifyTurnstile',
  oneSignal: 'https://api.onesignal.com',
  siteVerify: 'https://challenges.cloudflare.com/turnstile/v0/siteverify',
} as const

const ONE_SIGNAL = {
  sendEmail: `${API_COMMON.oneSignal}/notifications`,
  createUser: `${API_COMMON.oneSignal}/apps/${oneSignalAppId}/users`,
} as const

const createApiConfig = (
  baseUrl: (typeof ROBO_ACTUARY_URLS)[keyof typeof ROBO_ACTUARY_URLS]
) =>
  ({
    ...API_COMMON,
    ...ONE_SIGNAL,
    returnRate: `${baseUrl}/returns`,
    george<PERSON>ield: `${baseUrl}/george_yield`,
    tracking: `${baseUrl}/tracking`,
    feedback: `${baseUrl}/feedback/add`,
    geolocation: `${baseUrl}/geolocation/ip`,
  }) as const

const API_STAGING = createApiConfig(ROBO_ACTUARY_URLS.STAGING)
const API_PROD = createApiConfig(ROBO_ACTUARY_URLS.PROD)

const API_STATUS = {
  OK: 200,
  CREATED: 201,
  ACCEPTED: 202,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
} as const
const API = isProd ? API_PROD : API_STAGING

export { API, API_STATUS }
