type EmailContent = {
  app_id: string // Required. Identifies your application within our system.
  email_subject: string // Required. Specifies the subject of the email.
  template_id?: string // Identifies a specific template configured within the dashboard.
  name?: string // Serves as an internal identifier to help organize your email campaigns.
  include_unsubscribed?: boolean // Defaults to false. If true, the email will also be sent to unsubscribed users.
  included_segments?: Array<string> // The segments to target for sending the message to.
  filters?: Array<{ [key: string]: unknown }> // Filters allow you to dynamically define your email's recipients based on their attributes or behaviors.
  include_aliases?: { [key: string]: unknown } // Targets specific users based on their Aliases.
  include_email_tokens?: Array<string> // Allows targeting users based on their email address.
  email_body?: string
  target_channel?: string
  first_name?: string
  last_name?: string
  tags?: Array<{
    // Adds custom tags to your email.
    [key: string]: string
  }>
  custom_data?: {
    // Adds custom tags to your email.
    [key: string]: string
  }
}

type LogLevel = 'warn' | 'info' | 'success' | 'error'

interface LogServerlessParams {
  message?: string
  logLevel: LogLevel
  error?: unknown
}

type ContactUsBodyType = {
  firstName: string
  lastName: string
  selectedOption: string
  email: string
  message: string
}

type ContactUsHeadersType = {
  lat?: string
  long?: string
  timezone?: string
  country?: string
}

type TurnstileResponse = {
  success: boolean
  challenge_ts: string
  hostname: string
  'error-codes'?: Array<string>
}

export type {
  ContactUsBodyType,
  ContactUsHeadersType,
  EmailContent,
  LogServerlessParams,
  TurnstileResponse,
}
