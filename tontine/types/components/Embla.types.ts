import type { AutoplayOptionsType } from 'embla-carousel-autoplay'
import type useEmblaCarousel from 'embla-carousel-react'

import type { BoxContainerProps } from '../../components/common/BoxContainer'
import type { GenericButtonProps } from '../../components/common/GenericButton'
import type { SectionChunks } from '../Analytics/AnalyticsObjectIds.types'
import type { BreakpointsType } from '../common.types'
import type { IconProps } from '../component.types'

type UseCarouselParameters = Parameters<typeof useEmblaCarousel>
type CarouselOptions = UseCarouselParameters[0]

type EmblaButtonProps = GenericButtonProps & {
  arrowProps?: IconProps
  direction: 'right' | 'left'
}

type EmblaCarouselProps = {
  children: Array<React.ReactElement>
  sectionContext: SectionChunks
  options: CarouselOptions
  showButtons?: boolean
  showDots?: boolean
  emblaWrapperProps: BoxContainerProps
  sliderWrapperProps?: BoxContainerProps
  controlsWrapperProps?: BoxContainerProps
  gradientClassName?: string
  autoPlayOptions: AutoplayOptionsType
  shouldScale?: boolean
  shouldParallax?: boolean
  gradientBreakpoints?: Array<BreakpointsType>
  tweenFactorBase?: number
  scaleFactor?: number
  parallaxFactor?: number
  scaleClass?: string
  parallaxClass?: string
  dotsStyling?: {
    dotsWrapperProps: BoxContainerProps
    dotProps: GenericButtonProps
    currentDotStyle: GenericButtonProps['className']
  }
  buttonStyling?: {
    prevButtonProps?: GenericButtonProps
    nextButtonProps?: GenericButtonProps
  } & Omit<EmblaButtonProps, 'direction'>
}

export type {
  CarouselOptions,
  EmblaButtonProps,
  EmblaCarouselProps,
  UseCarouselParameters,
}
