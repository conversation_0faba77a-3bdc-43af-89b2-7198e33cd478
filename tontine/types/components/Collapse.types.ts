import type { BoxContainerProps } from '../../components/common/BoxContainer'
import type { GenericButtonProps } from '../../components/common/GenericButton'
import type { TitleProps } from '../../components/typography/Title'
import type { AnalyticsEvents } from '../Analytics/Analytics.types'
import type { IconProps } from '../component.types'
import type {
  LocalizedContentType,
  LocalizedStringContentType,
} from '../sections/section.types'

type CommonCollapseProps = {
  title?: LocalizedContentType | LocalizedStringContentType
  showArrow?: boolean
  triggerArrowProps?: IconProps
  triggerTitleProps?: TitleProps
}

type CollapseTriggerProps = {
  isOpen?: boolean
  collapseEvent?: AnalyticsEvents
  expandEvent?: AnalyticsEvents
  currentIsOpen?: boolean
  toggleCollapse?: () => void
} & CommonCollapseProps &
  Omit<GenericButtonProps, 'title'>

type CollapseContentProps = {
  currentIsOpen?: boolean
  contentInnerProps?: BoxContainerProps
} & BoxContainerProps

type CollapseProps = {
  children: React.ReactNode
  customTrigger?: {
    trigger?: React.ReactNode
    toggle?: () => void
    isOpen: boolean
  }
  rootProps?: BoxContainerProps
  contentProps?: CollapseContentProps
  triggerProps?: CollapseTriggerProps
} & CommonCollapseProps

export type { CollapseContentProps, CollapseProps, CollapseTriggerProps }
