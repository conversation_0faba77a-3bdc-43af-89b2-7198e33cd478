import type { PortableTextBlock } from 'next-sanity'

import type { BoxContainerProps } from '../../components/common/BoxContainer'
import type { ComponentTags } from '../../types/component.types'
import type { ParentSectionID } from '../../types/sections/section.types'

type OverrideElement = {
  overrideElements?: Record<
    string,
    {
      tag?: ComponentTags['box' | 'text']
      className?: string
    }
  >
}

type ComponentParserType = {
  renderDefaultBlock?: boolean
} & OverrideElement &
  ParentSectionID

type PortableContentProps = {
  markDownText?: Array<PortableTextBlock>
  renderWrapper?: boolean
  as?: ComponentTags['box']
} & Omit<BoxContainerProps, 'children' | 'as'> &
  ComponentParserType

type TableRow = {
  cells: Array<string>
}

type TableComponentProps = {
  value: {
    rows: [TableRow, ...Array<TableRow>]
  }
}

export type {
  ComponentParserType,
  OverrideElement,
  PortableContentProps,
  TableComponentProps,
}
