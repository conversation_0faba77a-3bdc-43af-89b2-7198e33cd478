enum PageEvent {
  viewed = 'page_viewed',
}

enum ErrorEvent {
  encountered = 'error_encountered',
}

enum ButtonEvent {
  clicked = 'button_clicked',
}

enum CarouselEvent {
  next = 'carousel_next_clicked',
  previous = 'carousel_previous_clicked',
  choose = 'carousel_slide_choose',
}

enum NavEvent {
  item_clicked = 'nav_item_clicked',
  item_hovered = 'nav_item_hovered',
  item_expanded = 'nav_item_expanded',
  item_collapsed = 'nav_item_collapsed',
  mobile_expanded = 'nav_mobile_expanded',
  mobile_collapsed = 'nav_mobile_collapsed',
}

enum FooterEvent {
  social_click = 'footer_social_click',
}

enum FAQEvent {
  item_expanded = 'faq_item_expanded',
  item_collapsed = 'faq_item_collapsed',
  category_selected = 'faq_category_selected',
  searched = 'faq_searched',
  search_cleared = 'faq_search_cleared',
}

enum VideoEvent {
  played = 'video_played',
  paused = 'video_paused',
  seek = 'video_seek',
  finished = 'video_finished',
  shared = 'video_shared',
  clicked = 'video_card_clicked',
}

enum ContentPostEvent {
  clicked = 'content_card_clicked',
  hovered = 'content_card_hovered',
  shared = 'content_post_shared',
}

enum ResearchEvent {
  download = 'research_pdf_download',
}

enum AppDownloadEvent {
  download = 'app_download_clicked',
}

enum SectionEvent {
  btn_clicked = 'section_btn_clicked',
  link_clicked = 'section_link_clicked',
  link_hovered = 'section_link_hovered',
  btn_hovered = 'section_btn_hovered',
}

enum ContactEvent {
  submit = 'contact_submit',
  email = 'contact_manual_email',
}

enum InputFieldEvent {
  typed = 'input_field_typed',
  clicked = 'input_field_clicked',
  selected = 'input_field_selected',
}

enum FeedbackEvent {
  submit = 'feedback_submit',
}

export {
  AppDownloadEvent,
  ButtonEvent,
  CarouselEvent,
  ContactEvent,
  ContentPostEvent,
  ErrorEvent,
  FAQEvent,
  FeedbackEvent,
  FooterEvent,
  InputFieldEvent,
  NavEvent,
  PageEvent,
  ResearchEvent,
  SectionEvent,
  VideoEvent,
}
