import type {
  AppDownloadEvent,
  ButtonEvent,
  CarouselEvent,
  ContactEvent,
  ContentPostEvent,
  ErrorEvent,
  FAQEvent,
  FeedbackEvent,
  FooterEvent,
  InputFieldEvent,
  NavEvent,
  PageEvent,
  ResearchEvent,
  SectionEvent,
  VideoEvent,
} from './AnalyticsEvents.types'
import type { AnalyticsObjectIds } from './AnalyticsObjectIds.types'

type AnalyticsEvents =
  | PageEvent
  | ErrorEvent
  | ButtonEvent
  | CarouselEvent
  | NavEvent
  | SectionEvent
  | FAQEvent
  | ContentPostEvent
  | VideoEvent
  | ResearchEvent
  | ContactEvent
  | FooterEvent
  | InputFieldEvent
  | FeedbackEvent
  | AppDownloadEvent

type AnalyticsProperties = {
  description?: string
  object_id?: AnalyticsObjectIds
  object_value?: unknown
  url_type?: 'internal' | 'external'
  slide_index?: number
  label?: string
}

type TrackProps = {
  event: AnalyticsEvents
  properties: AnalyticsProperties
}

export type { AnalyticsEvents, AnalyticsProperties, TrackProps }
