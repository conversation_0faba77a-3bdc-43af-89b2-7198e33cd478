type Faq = 'faq'
type NavBar = 'nav_bar'
type FeedbackModal = 'feedback_modal'
type Toast = 'toast_message'
type NavItem = 'nav_item'
type Mobile = 'mobile'
type Footer = 'footer'
type MyTontine = 'my_tontine'
type InfoBanner = 'info_banner'
type ContactUs = 'contact_us'
type MoreLikeThis = 'more_like_this'
type WalkthroughInfoModal = 'walkthrough_modal_info'
type WalkthroughPin = 'walkthrough_pin'
type LanguageSelect = 'language_select'

type TextInputIdChunk =
  | 'first_name'
  | 'last_name'
  | 'email'
  | 'search_bar'
  | 'text_area'

type ButtonIdChunk =
  | 'close'
  | 'open'
  | 'next'
  | 'previous'
  | 'back'
  | 'dismiss'
  | 'submit'
  | 'log_in'
  | 'log_out'
  | 'sign_up'
  | 'mailto'
  | 'view_all'
  | 'rate_positive'
  | 'rate_negative'
  | 'choose'
  | 'redirect'
  | 'rate'

type SectionChunks =
  | 'cta_section'
  | 'featured_section'
  | 'faq_section'
  | 'about_us_section'
  | 'team_section'
  | 'partners_section'
  | 'info_block_section'
  | 'carousel_section'
  | 'contact_us_section'
  | 'companies_logos_section'
  | 'info_banner_section'
  | 'hero_section'
  | 'carousel_hero_section'
  | 'testimonial_section'
  | 'content_section'
  | 'tontinator_section'
  | 'referral_section'
  | 'cta_card_section'
  | 'footer'
  | 'markdown_section'
  | 'walkthrough_section'
  | 'download_section'
  | 'info_hub_section'

type ButtonUiId =
  `${InfoBanner | SectionChunks | MyTontine | NavBar | NavItem | ContactUs | MoreLikeThis | FeedbackModal | WalkthroughPin | WalkthroughInfoModal | Toast}_${ButtonIdChunk}`

type MobileButtonUiId = `${Mobile}_${ButtonUiId}`

type FAQIdChunk = 'category' | 'item' | 'search'

type LinksIdChunk = 'main_link' | 'sub_link' | 'social_link'

type ShareIdChunk =
  | 'facebook'
  | 'x'
  | 'linkedin'
  | 'youtube'
  | 'instagram'
  | 'clipboard'

type FAQUiId = `${Faq}_${FAQIdChunk}`

type NavBarUiId = `${NavBar}_${LinksIdChunk}`

type MobileNavBar = `${Mobile}_${NavBarUiId}`

type FooterUiId = `${Footer}_${LinksIdChunk}`

type SectionUiID = `${string}_${SectionChunks}`

type ContentPostUiId = `${string}_content_post`

type MoreLikeThisUiId = `${string}_${MoreLikeThis}`

type TextInputUiId = `${Faq | ContactUs | FeedbackModal}_${TextInputIdChunk}`

/**
 * All possible `object_id` arguments
 */
type AnalyticsObjectIds =
  | FAQUiId
  | NavBarUiId
  | FooterUiId
  | SectionUiID
  | ContentPostUiId
  | ShareIdChunk
  | ButtonUiId
  | MoreLikeThisUiId
  | MobileButtonUiId
  | MobileNavBar
  | TextInputUiId
  | LanguageSelect

export type { AnalyticsObjectIds, SectionChunks, ShareIdChunk }
