import type { SanityImageType } from '../common.types'
import type { LocalizedContentType } from './section.types'

type WalkthroughPinType = {
  _id: string
  title: LocalizedContentType
  subtitle: LocalizedContentType
  placement: {
    column: number
    row: number
  }
}

type WalkthroughGridType = {
  walkthroughGridImage: SanityImageType
  walkthroughPins: Array<WalkthroughPinType>
}

type WalkthroughSectionType = {
  desktopWalkthroughs?: Array<WalkthroughGridType>
  mobileWalkthroughs?: Array<WalkthroughGridType>
}

export type { WalkthroughGridType, WalkthroughPinType, WalkthroughSectionType }
