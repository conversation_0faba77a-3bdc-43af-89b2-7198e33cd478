import type { SanityImageType } from '../common.types'
import type { SubMenuItem } from '../shared-page-data.types'
import type {
  LocalizedContentHeader,
  LocalizedContentType,
  LocalizedStringContentType,
  LocalizedStringHeader,
} from './section.types'

type PartnerType = {
  stringTitle?: LocalizedStringContentType
  subtitle?: LocalizedContentType
  partnerImage: SanityImageType
  partnerExternalLink: string
} & Omit<LocalizedStringHeader, 'stringSubtitle'> &
  Omit<LocalizedContentHeader, 'title'>
type PartnerSectionType = {
  partnersImage: SanityImageType
  partnersSectionTitle: string
  partnersSubMenuItem: SubMenuItem
  partnersCompanies: Array<PartnerType>
}

export type { PartnerSectionType, PartnerType }
