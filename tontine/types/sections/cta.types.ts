import type { San<PERSON><PERSON><PERSON><PERSON> } from '../common.types'
import type { SharedSectionDataType } from './section.types'

/**
 * Props type for the ctaCard section
 */
type CTACardSectionType = {
  position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  intersectSections: Array<string>
  ctaCardButton: SanityButton
} & SharedSectionDataType

/**
 * type for the Cta reusable card component.
 */
type CtaTemplate = Omit<SharedSectionDataType, 'title' | 'subtitle'> & {
  ctaCardContainer: object
  linkStyle: object
  ctaFlexStyle: object
  imageAndTextStyle: object
  imageContainer: object
  skeletonStyling: object
  titleAndTextStyle: object
  titleStyle: object
  descriptionStyle: object
  ctaButtonStyle: object
  ctaData: CTACardSectionType
  ctaVisible?: boolean
  ctaButtonTextStyle?: object
  infoBannerIsVisible?: boolean
  onCloseButtonClick?: () => void
}

/**
 * type for the options that ctaCard can receive for its absolute position
 */
type CTACardPosition =
  | 'top-right'
  | 'top-left'
  | 'bottom-right'
  | 'bottom-left'
  | 'full-bottom-right'
  | 'full-bottom-left'

type CardPosition = {
  top?: string
  right?: string
  bottom?: string
  left?: string
}

export type { CardPosition, CTACardPosition, CTACardSectionType, CtaTemplate }
