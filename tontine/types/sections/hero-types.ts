import type { BoxContainerProps } from '../../components/common/BoxContainer'
import type { PageDomainType, SanityButton } from '../common.types'
import type { SectionMediaType, SharedSectionDataType } from './section.types'

type HeroProps = {
  buttons?: Array<SanityButton>
  heroVariants?: 'simple' | 'modern' | 'legacy'
} & Omit<SharedSectionDataType, 'icon'> &
  SectionMediaType &
  Omit<BoxContainerProps, 'title'>

type HeroPostProps = {
  isCarousel?: boolean
  isPriority?: boolean
  heroData: HeroProps
} & PageDomainType

type CarouselHeroSectionType = {
  carouseHeroSectionPosts?: Array<HeroProps>
} & PageDomainType

export type { CarouselHeroSectionType, HeroPostProps }
