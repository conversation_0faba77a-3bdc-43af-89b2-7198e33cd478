import type {
  LocalizedContentType,
  LocalizedStringContentType,
  ParentSectionID,
} from './section.types'

type QuestionAndAnswer = {
  question?: LocalizedStringContentType
  answer?: LocalizedContentType
  id?: string
  tags?: Array<string>
}

type FaqCategory = {
  categoryTitle?: LocalizedStringContentType
  faqData: Array<QuestionAndAnswer>
}

type FaqSectionType = {
  faqCategories: Array<FaqCategory>
}

type SearchBarProps = {
  children?: React.ReactNode
  placeholderText?: string
  language?: string
  faqData?: Array<FaqCategory>
} & ParentSectionID

export type { FaqCategory, FaqSectionType, QuestionAndAnswer, SearchBarProps }
