import type { LinkSelectorProps, SanityButton, SlugType } from '../common.types'
import type {
  LocalizedContentType,
  SectionMediaType,
  SharedSectionDataType,
} from './section.types'

type InfoVariants = 'horizontal' | 'vertical'

type InfoBlock = {
  infoBlockButtons?: Array<SanityButton>
  slug: SlugType
} & SharedSectionDataType

type InfoBlockSectionType = {
  infoHubCardList?: Array<InfoBlock>
  layout?: InfoVariants
  infoHubAdditionalInfo?: LocalizedContentType
} & SharedSectionDataType &
  SectionMediaType

type InfoBannerSectionType = Omit<LinkSelectorProps, 'pageSlug'> & {
  infoBannerPageSlug: SlugType
} // pageSlug needs to be omitted here since normal sections don't have pageSlug as SlugType and this is the one fringe case

export type {
  InfoBannerSectionType,
  InfoBlock,
  InfoBlockSectionType,
  InfoVariants,
}
