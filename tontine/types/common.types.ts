import type { TestID } from 'cypress/support/ui-component-ids'
import type { UseEmblaCarouselType } from 'embla-carousel-react'
import type { ElementType } from 'react'

import type { CONSTANTS } from '../data-resource/constants'
import type { STYLE } from '../styles/style'
import type { ShareIdChunk } from './Analytics/AnalyticsObjectIds.types'
import type {
  LocalizedContentHeader,
  LocalizedContentType,
  LocalizedStringContentType,
} from './sections/section.types'
import type { WebSchemasWebsiteWideProps } from './webschema.types'

type PageDomainType = {
  pageDomain: string
}

type LinkSelectorProps = {
  pageSlug: SlugType
  sectionSlug: SlugType
  linkType: 'internal' | 'custom'
  customLink: string
  customParamsButton: boolean
  customParams: string
}

type LinkWithImageType = {
  title: LocalizedStringContentType
  href: string
  linkImage: SanityImageType
  comingSoon?: boolean
}

type SocialMediaIcon = {
  icon: SanityImageType
  url: string
  title: ShareIdChunk
}

type SanityImageType = {
  alt: string
  src: string
  cornerRadius?: number
  rounded?: number
}

type MuxVideoType = {
  playbackId: string
  duration: number
  quality: string
  updatedAt: string
  createdAt: string
  height: number
  width: number
}

type VideoMetadataType = {
  video?: MuxVideoType
  videoThumbnail?: SanityImageType
} & PageDomainType &
  LocalizedContentHeader

type LinkCustomType = {
  linkLabel: string
} & LinkSelectorProps

type ImageObjectFitType = 'cover' | 'contain' | 'fill' | 'none' | 'scale-down'

type SanityButton = {
  buttonLabel?: LocalizedStringContentType
} & LinkSelectorProps

type DynamicMetricsType = {
  returnRate?: ReturnRatesType
  annualYield?: number
}

type SlugType = {
  _type: string
  current: string
}

type PersonType = {
  id: string
  personImage: SanityImageType
  personAlternativeImage?: SanityImageType
  personName: string
  personDescription: string
  personTitle: string
  personSocialLink: Omit<SocialMediaIcon, 'title'>
}

type SlugArray = Array<{
  slug: string
  updatedAt: string
  title: LocalizedContentType | LocalizedStringContentType
}>

type ButtonType = 'button' | 'submit' | 'reset' | undefined

type ExchangeRateType = { dayOfValidity: string; rate: number; source: string }
type ReturnRatesType = {
  [key: string]: ExchangeRateType
}

type ReturnMetricsType = {
  strategy: string
  currency: string
}

type GeorgeCurrencyType = string

type GeorgeType = {
  returnRate?: number
  annualYield?: number
}

type DynamicDataResult = ReturnRatesType & GeorgeType

type DownloadFileData = {
  url: string
  fileName: string
}

type DownloadFileItem = DownloadFileData & { slug?: string }

type IsPreview = boolean | undefined

type EmblaCarouselApi = UseEmblaCarouselType[1]

type ManifestQueryType = {
  description: string
  name: string
  shortName: string
} & PageDomainType

type RSSQueryType = {
  description: string
  shortName: string
  updatedAt: string
}

type ThrottledFunction<T extends Array<unknown>> = (...args: T) => void

type AdditionalComponentAttributes = {
  additionalAttributes?: React.HTMLAttributes<
    | HTMLAnchorElement
    | HTMLButtonElement
    | HTMLTextAreaElement
    | HTMLHeadingElement
  > & { 'data-cy'?: TestID }
  dataTestId?: TestID
  disabled?: boolean
  tabIndex?: number
  as?: ElementType
}

type BreakpointsType = (typeof CONSTANTS.BREAKPOINTS)[number]

type LanguagesType = (typeof CONSTANTS.LANGUAGES)[number]

type Regions = (typeof CONSTANTS.REGIONS)[keyof typeof CONSTANTS.REGIONS]

type MarkdownColorCodesType = keyof typeof STYLE.MARKDOWN_COLOR_CODES

export type {
  AdditionalComponentAttributes,
  BreakpointsType,
  ButtonType,
  DownloadFileData,
  DownloadFileItem,
  DynamicDataResult,
  DynamicMetricsType,
  EmblaCarouselApi,
  GeorgeCurrencyType,
  GeorgeType,
  ImageObjectFitType,
  IsPreview,
  LanguagesType,
  LinkCustomType,
  LinkSelectorProps,
  LinkWithImageType,
  ManifestQueryType,
  MarkdownColorCodesType,
  MuxVideoType,
  PageDomainType,
  PersonType,
  Regions,
  ReturnMetricsType,
  ReturnRatesType,
  RSSQueryType,
  SanityButton,
  SanityImageType,
  SlugArray,
  SlugType,
  SocialMediaIcon,
  ThrottledFunction,
  VideoMetadataType,
  WebSchemasWebsiteWideProps,
}
