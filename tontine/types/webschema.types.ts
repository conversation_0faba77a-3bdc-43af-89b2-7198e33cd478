import type { QuantitativeValue } from 'schema-dts'

type WebSchemaInvestmentOrDepositProps = {
  _type: 'investmentOrDepositSchema'
  investmentOrDepositSchemaName: string
  investmentOrDepositSchemaDescription: string
  investmentOrDepositSchemaMinValue: number
  investmentOrDepositSchemaMaxValue: number
  investmentOrDepositSchemaAnnualPercentageRate: number
  investmentOrDepositSchemaFeesAndCommissionsSpecification: string
  investmentOrDepositSchemaInterestRate: number
  investmentOrDepositSchemaAreaServed: string
  investmentOrDepositSchemaAdditionalType: string
  investmentOrDepositSchemaAward: string
  investmentOrDepositSchemaBrandName: string
  investmentOrDepositSchemaBrandUrl: string
  investmentOrDepositSchemaCategory: string
  investmentOrDepositSchemaLogoUrl: string
  investmentOrDepositSchemaProviderMobility: string
  investmentOrDepositSchemaTermsOfService: string
  investmentOrDepositSchemaAlternateName: string
  investmentOrDepositSchemaUrl: string
  investmentOrDepositSchemaSameAs: Array<string>
  investmentOrDepositSchemaSlogan: string
  investmentOrDepositSchemaAudienceType: string
}

type WebSchemaCorporationProps = {
  _type: 'corporationSchema'
  corporationSchemaName: string
  corporationSchemaDescription: string
  corporationSchemaActionableFeedbackPolicy: string
  corporationSchemaStreetAddress: string
  corporationSchemaAddressLocality: string
  corporationSchemaAddressRegion: string
  corporationSchemaPostalCode: string
  corporationSchemaAddressCountry: string
  corporationSchemaCorrectionsPolicy: string
  corporationSchemaDiversityPolicy: string
  corporationSchemaDiversityStaffingReport: string
  corporationSchemaDuns: string
  corporationSchemaEmail: string
  corporationSchemaEthicsPolicy: string
  corporationSchemaFaxNumber: string
  corporationSchemaFounder: string
  corporationSchemaFoundingDate: string
  corporationSchemaFoundingAddressLocality: string
  corporationSchemaFoundingAddressRegion: string
  corporationSchemaFoundingPostalCode: string
  corporationSchemaFoundingAddressCountry: string
  corporationSchemaGlobalLocationNumber: string
  corporationSchemaKeywords: Array<string>
  corporationSchemaKnowsAbout: string
  corporationSchemaKnowsLanguage: string
  corporationSchemaLegalName: string
  corporationSchemaLeiCode: string
  corporationSchemaLogo: string
  corporationSchemaNumberOfEmployees: QuantitativeValue
  corporationSchemaPublishingPrinciples: string
  corporationSchemaSlogan: string
  corporationSchemaTaxID: string
  corporationSchemaTelephone: string
  corporationSchemaVatID: string
  corporationSchemaAlternateName: string
  corporationSchemaUrl: string
}

type WebSchemasWebsiteWideProps =
  | WebSchemaInvestmentOrDepositProps
  | WebSchemaCorporationProps

export type {
  WebSchemaCorporationProps,
  WebSchemaInvestmentOrDepositProps,
  WebSchemasWebsiteWideProps,
}
