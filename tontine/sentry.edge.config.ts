// This file configures the initialization of Sentry for edge features (middleware, edge routes, and so on).
// The config you add here will be used whenever one of the edge features is loaded.
// Note that this config is unrelated to the Vercel Edge Runtime and is also required when running locally.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import { init } from '@sentry/nextjs'

import { SECURITY_CONSTANTS } from './data-resource/security-constants'
import {
  buildContext,
  sentryDSN,
  sentryDebug,
  sentryEnabled,
  sentrySampleRate,
} from './serverless/keys'

if (sentryEnabled) {
  init({
    environment: buildContext,
    dsn: sentryDSN,
    denyUrls: [...SECURITY_CONSTANTS.IGNORED_EXCEPTION_URLS],

    // Adjust this value in production, or use tracesSampler for greater control
    tracesSampleRate: sentrySampleRate,
    debug: sentryDebug,
  })
}
