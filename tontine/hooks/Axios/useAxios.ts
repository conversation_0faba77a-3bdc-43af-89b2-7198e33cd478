import type { AxiosRequestConfig, AxiosResponse } from 'axios'
import axios from 'axios'
import axiosRetry from 'axios-retry'

interface AxiosWithRetryParams {
  url: string
  retries?: number
  backoff_factor?: number
  config?: AxiosRequestConfig
}

/* Backoff factor sequences example
 * # 2 - 1, 2, 4, 8, 16, 32, 64, 128
 * # 3 - 1.5, 3, 12, 24, 48, 96, 192, 384
 * # 10 - 5, 10, 20, 40, 80, 160, 320, 640
 */

/**
 * Sends an axios request with retry functionality and exponential backoff delay.
 */
export const axiosRequest = async <T>({
  url,
  retries = 0,
  backoff_factor = 1,
  config = { method: 'get' },
}: AxiosWithRetryParams): Promise<AxiosResponse<T>> => {
  const axiosInstance = axios.create()
  axiosRetry(axiosInstance, {
    retries,
    retryDelay: (retryCount: number) => {
      return backoff_factor * 2 ** (retryCount - 1)
    },
  })

  return axiosInstance(url, config)
}
