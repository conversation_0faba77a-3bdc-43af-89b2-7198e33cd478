'use client'

import { useEffect, useState } from 'react'

type IntersectObserverHookProps = {
  ids: string | Array<string> | null
  root: Element | Document | null | undefined
  rootMargin: string
  threshold: number | Array<number>
  additionalElementsToAttach?: Array<HTMLElement> | HTMLElement | null
}

/**
 * Custom hook for observing elements using the Intersection Observer API.
 *
 * - `ids` - IDs of elements to observe.
 * - `root` - The root element to use as the viewport.
 * - `rootMargin` - Margin around the root element.
 * - `threshold` - Threshold(s) for intersection ratio.
 * - `additionalElementsToAttach` - Additional elements to observe.
 */
export const useIntersectObserver = ({
  ids,
  root = null,
  rootMargin = '20px',
  threshold = 0,
  additionalElementsToAttach,
}: IntersectObserverHookProps) => {
  const [targetIntersecting, setTargetIntersecting] = useState(false)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        setTargetIntersecting(entries.some((entry) => entry.isIntersecting))
      },
      { root, rootMargin, threshold }
    )

    // Collect elements from both IDs and direct element references
    const elements: Array<HTMLElement> = []

    // Add elements by ID(s)
    const addById = (target: string | Array<string>) => {
      const targets = Array.isArray(target) ? target : [target]
      targets.forEach((id) => {
        const el = document.getElementById(id)
        if (el) elements.push(el)
      })
    }

    // Add direct element references
    const addElements = (target: HTMLElement | Array<HTMLElement>) => {
      const elementsToAdd = Array.isArray(target) ? target : [target]
      elements.push(...elementsToAdd.filter((el) => el instanceof HTMLElement))
    }

    if (ids) addById(ids)
    if (additionalElementsToAttach) addElements(additionalElementsToAttach)

    // Observe all collected elements
    elements.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [ids, root, rootMargin, threshold, additionalElementsToAttach])

  return { targetIntersecting }
}
