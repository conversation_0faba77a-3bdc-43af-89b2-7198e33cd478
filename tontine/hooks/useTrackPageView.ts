import mixpanel from 'mixpanel-browser'
import { usePathname } from 'next/navigation'
import { useEffect } from 'react'

import { CONSTANTS } from '../data-resource/constants'
import { detectDeviceType } from '../helper-functions/UtilFunctions'
import { shouldTrack } from '../serverless/keys'
import { PageEvent } from '../types/Analytics/AnalyticsEvents.types'

/**
 * Custom hook to track page views.
 *
 * This hook tracks the page view event after a throttle
 * duration to prevent it from being overridden by previous events.
 *
 */
export const useTrackPageView = () => {
  const pathName = usePathname()

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (shouldTrack)
        mixpanel.track_pageview(
          {
            object_value: pathName,
            source: 'website',
            device: detectDeviceType(),
          },
          { event_name: PageEvent.viewed }
        )
    }, CONSTANTS.ANALYTICS_THROTTLE_DURATION + 100)

    return () => clearTimeout(timeoutId)
  }, [pathName])
}
