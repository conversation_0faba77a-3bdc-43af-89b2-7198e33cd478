'use client'

import { useState } from 'react'
/**
 * Hook that toggles visibility of a component
 * @param defaultVisible - Receives a boolean to set the default visibility of the component
 */
export function useHideOnClick(defaultVisible: boolean): [boolean, () => void] {
  const [isVisible, setIsVisible] = useState(defaultVisible)

  const toggleHidden = () => {
    setIsVisible(false)
  }

  return [isVisible, toggleHidden]
}
