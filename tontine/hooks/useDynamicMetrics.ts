import { useEffect, useState } from 'react'

import { replaceDynamicMetrics } from '../helper-functions/UtilFunctions'

/** Fetches dynamic metrics from the API and replaces them in the given content string.
 * If the API call fails, the original content is returned.
 *
 */
export const useDynamicMetrics = (content: string) => {
  const [interpolatedContent, setInterpolatedContent] = useState(content)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await replaceDynamicMetrics(interpolatedContent)
        setInterpolatedContent(result)
      } catch (_error) {
        setInterpolatedContent(content)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [interpolatedContent, content])

  return { metrics: interpolatedContent, isLoading }
}
