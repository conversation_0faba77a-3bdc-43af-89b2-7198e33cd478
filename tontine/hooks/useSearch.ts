'use client'

import { toPlainText } from 'next-sanity'
import { useState } from 'react'

import { searchForOption } from '../helper-functions/UtilFunctions'
import { useLanguage } from '../providers/LanguageContext'
import type { QuestionAndAnswer } from '../types/sections/faq-section.types'

/**
 * Searches an array of objects with the provided `searchBy` array of object keys
 */
export const useSearch = ({
  options,
  searchBy,
}: {
  options: Array<QuestionAndAnswer>
  searchBy: Array<'answer' | 'question' | 'tags'>
}) => {
  const { language } = useLanguage()

  const localizedOptions = options?.map((option) => {
    const answer = option?.answer?.[language] ?? option?.answer?.en
    const question = option?.question?.[language] ?? option?.question?.en
    return {
      ...option,
      answer: answer ? toPlainText(answer) : '',
      question: question ?? '',
    }
  })
  const [foundOptions, setFoundOptions] = useState([{}])
  const [searchQuery, setSearchQuery] = useState('')
  const [nothingFound, setNothingFound] = useState(false)
  const handleSearchQueryChange = (query: string) => {
    setSearchQuery(query)
    const filteredOptions = searchForOption({
      options: localizedOptions,
      searchQuery: query,
      searchBy,
    })
    const filteredOptionsLocalized = filteredOptions?.map((option) => {
      return {
        ...option,
        question: options?.find((e) => e?.id === option?.id)?.question,
        answer: options?.find((e) => e?.id === option?.id)?.answer,
      }
    })
    setFoundOptions(filteredOptionsLocalized)
  }

  const handleNothingFound = (state: boolean) => {
    setNothingFound(state)
  }

  return {
    foundOptions,
    searchQuery,
    setSearchQuery: handleSearchQueryChange,
    nothingFound,
    setNothingFound: handleNothingFound,
  }
}
