'use client'

import { useState } from 'react'

import strings from '../data-resource/strings.json'
import { API } from '../serverless/API'
import { isProd } from '../serverless/keys'
import { axiosRequest } from './Axios/useAxios'
import { useToastHook } from './useToast'

/**
 * Custom hook that triggers a post request to the mail provider,
 * resets the fields, and displays a success or error toast message.
 */
export const useContactUsForm = () => {
  const { showToast } = useToastHook()
  const [isLoading, setIsLoading] = useState(true)
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [email, setEmail] = useState('')
  const [message, setMessage] = useState('')
  const [selectedOption, setSelectedOption] = useState('')

  const formSubmit = async ({
    onFailure,
    token,
  }: {
    onFailure?: () => void
    token?: FormDataEntryValue
  }) => {
    setIsLoading(true)

    try {
      if (!isProd) {
        throw new Error('Not on prod')
      }
      await axiosRequest({
        url: API.verifyTurnstile,
        config: {
          data: { token },
          method: 'post',
        },
      })
      await axiosRequest({
        url: API.contactForm,
        config: {
          method: 'post',
          data: {
            firstName,
            lastName,
            email,
            message,
            selectedOption,
          },
        },
      })

      showToast({
        description: strings.CONTACT_US_TOAST_SUCCESS_DESCRIPTION,
        type: 'success',
        title: strings.CONTACT_US_TOAST_SUCCESS_TITLE,
        duration: strings.CONTACT_US_TOAST_SUCCESS_DURATION,
      })

      setFirstName('')
      setLastName('')
      setEmail('')
      setMessage('')
      setSelectedOption('')
    } catch (_error) {
      onFailure?.()
      showToast({
        description: isProd
          ? strings.MESSAGE_NOT_SENT
          : strings.FORM_STAGING_ERROR_DESCRIPTION,
        type: 'error',
        title: isProd
          ? strings.CONTACT_US_TOAST_ERROR_TITLE
          : strings.FORM_STAGING_ERROR_TITLE,
        duration: strings.CONTACT_US_TOAST_ERROR_DURATION,
      })
    } finally {
      setIsLoading(false)
    }
  }

  return {
    isLoading,
    firstName,
    setFirstName,
    lastName,
    setLastName,
    email,
    setEmail,
    message,
    setMessage,
    selectedOption,
    setSelectedOption,
    formSubmit,
    setIsLoading,
  }
}
