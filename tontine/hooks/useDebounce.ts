'use client'

import { useEffect, useState } from 'react'

/**
 *  Custom hook  that prevents an immediate update on each keydown on a input
 * value It preserves an instance of the initial value for a a certain time
 * period that we declare through @delay parameter. If no key is pressed before
 * the @delay time expires, the value will update to its latest state
 */
export const useDebouncedValidation = (
  validate: (value: string) => { isValid: boolean; errorMessage: string },
  value: string,
  delay: number
) => {
  const [validationResult, setValidationResult] = useState({
    isValid: true,
    errorMessage: '',
  })

  // biome-ignore lint/correctness/useExhaustiveDependencies: <Will cause unnecessary re-renders>
  useEffect(() => {
    // we do this to validate the input on every key stroke in order to disable
    // the submit button while typing
    setValidationResult({
      isValid: true,
      errorMessage: '',
    })
    const timeout = setTimeout(() => {
      setValidationResult(validate(value))
    }, delay)

    return () => clearTimeout(timeout)
  }, [value, delay])

  return validationResult
}
