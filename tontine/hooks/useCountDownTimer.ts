'use client'

import { useEffect, useRef, useState } from 'react'

type CountDownTimerProps = {
  hours: number
  minutes: number
  seconds: number
  onCountdownFinished: () => void
}
/**
 * @description Renders a countdown timer,  with `hours`, `minutes` and
 * `seconds` if provided, if seconds are only provided the timer will start
 * countdown from seconds
 */
export const useCountDownTimer = ({
  hours,
  minutes,
  seconds,
  onCountdownFinished,
}: CountDownTimerProps) => {
  const [[hrs, mins, secs], setTime] = useState([hours, minutes, seconds])
  const countdownFinished = useRef(false) // Track countdown status
  const timerIdRef = useRef<number | undefined>(0) // Reference to the timerId
  // Countdown timer ticks every second
  const tick = () => {
    if (hrs === 0 && mins === 0 && secs === 0) {
      // Checks if the countdown has already finished
      if (!countdownFinished.current) {
        countdownFinished.current = true // Update countdown status
        if (onCountdownFinished) {
          onCountdownFinished()
        }
        clearInterval(timerIdRef.current)
      }
    } else if (mins === 0 && secs === 0) {
      setTime([hrs - 1, 59, 59])
    } else if (secs === 0) {
      setTime([hrs, mins - 1, 59])
    } else {
      setTime([hrs, mins, secs - 1])
    }
  }
  // Runs on every render to tick the timer
  useEffect(() => {
    // we call the window object to access the setInterval id's type of number
    // otherwise it will ask for a node.js type of Timer
    timerIdRef.current = window.setInterval(tick, 1_000)
    // Cleanup function
    return () => {
      clearInterval(timerIdRef.current)
    }
  })

  return { hrs, mins, secs }
}
