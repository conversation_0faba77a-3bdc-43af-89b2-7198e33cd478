import { useCallback } from 'react'

import track from '../app/api/analytics'
import { getAttribute, isExternalLink } from '../helper-functions/UtilFunctions'
import type { AnalyticsProperties } from '../types/Analytics/Analytics.types'
import { SectionEvent } from '../types/Analytics/AnalyticsEvents.types'
import type { NextLinkTrackingProps } from '../types/components/NextLink.types'

/** Custom hook to track link events */
export const useLinkTrackEvent = ({
  href,
  objectId,
  customEvent,
  customValue,
  linkLabel,
  trackHover,
  onClick,
}: NextLinkTrackingProps) => {
  const urlType: AnalyticsProperties['url_type'] = isExternalLink(href)
    ? 'external'
    : 'internal'

  const defaultEvent = trackHover
    ? SectionEvent.link_hovered
    : SectionEvent.link_clicked

  return useCallback(() => {
    onClick?.()
    track({
      event: customEvent ?? defaultEvent,
      properties: {
        object_value: customValue ?? href,
        object_id: objectId,
        ...getAttribute(Boolean(linkLabel), 'label', linkLabel),
        ...getAttribute(
          !customEvent || urlType === 'external',
          'url_type',
          urlType
        ),
      },
    })
  }, [
    onClick,
    customEvent,
    defaultEvent,
    customValue,
    href,
    objectId,
    linkLabel,
    urlType,
  ])
}
