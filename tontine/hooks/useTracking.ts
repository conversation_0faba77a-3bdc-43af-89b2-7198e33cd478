'use client'

import type { Mixpanel, OverridedMixpanel } from 'mixpanel-browser'
import mixpanel from 'mixpanel-browser'
import { useEffect } from 'react'

import {
  isLoadedWindow,
  shouldTrackWithoutConsent,
} from '../helper-functions/UtilFunctions'
import { useAuth } from '../providers/AuthContext'
import {
  isDev,
  mixpanelProxyDomain,
  mixpanelToken,
  shouldTrack,
} from '../serverless/keys'

/**
 * Opts in and out (DEFAULT) of tracking depending if GDPR regulations apply
 */
const optInOutTracking = (
  optIn: boolean,
  mixpanelIn: Mixpanel | OverridedMixpanel
) => {
  if (optIn) {
    mixpanelIn.set_config({ ignore_dnt: true })
    mixpanelIn.opt_in_tracking()
  } else {
    mixpanelIn.opt_out_tracking()
  }
}

/**
 * Initializes mixpanel tracking and handles cookie consent events from
 * "CookieBot"
 */
export function useTracking() {
  const { sendMessageToIframe } = useAuth()

  useEffect(() => {
    if (!shouldTrack || !isLoadedWindow()) return

    const currentHostname = window.location.hostname
    const skipConsentCheck = shouldTrackWithoutConsent(currentHostname)

    mixpanel.init(mixpanelToken, {
      api_host: mixpanelProxyDomain,
      persistence: 'cookie',
      debug: isDev,
      opt_out_tracking_by_default: !skipConsentCheck,
      record_sessions_percent: 1, // https://docs.mixpanel.com/docs/tracking-methods/sdks/javascript#implementation--sampling
      loaded: (mixpanelIn) => {
        if (skipConsentCheck) {
          optInOutTracking(true, mixpanelIn)
          return
        }

        // Regular Cookiebot logic for other domains
        // GDPR tracking check!
        if (
          // @ts-expect-error Cookie bot is not typed properly
          Cookiebot &&
          // @ts-expect-error
          Boolean(Cookiebot?.regulations) &&
          // @ts-expect-error
          Cookiebot?.regulations?.gdprApplies &&
          // @ts-expect-error
          Cookiebot?.consented &&
          // @ts-expect-error
          Cookiebot?.consent?.marketing
        ) {
          const isConsentedToTrack: boolean =
            // @ts-expect-error
            Cookiebot?.consented &&
            // @ts-expect-error
            Cookiebot?.consent?.marketing
          optInOutTracking(isConsentedToTrack, mixpanelIn)
        }

        // None GDPR tracking CHECK!
        if (
          // @ts-expect-error
          Cookiebot &&
          // @ts-expect-error
          Boolean(Cookiebot?.regulations) &&
          // @ts-expect-error
          !Cookiebot?.regulations?.gdprApplies
        ) {
          optInOutTracking(true, mixpanelIn)
          // Hide for good measure
          // @ts-expect-error
          Cookiebot?.hide?.()
        }
      },
    })
  }, [])

  // CookieBot event listeners
  // biome-ignore lint/correctness/useExhaustiveDependencies: <Will cause unnecessary re-renders>
  useEffect(() => {
    if (!isLoadedWindow()) return

    const currentHostname = window.location.hostname
    const skipConsentCheck = shouldTrackWithoutConsent(currentHostname)
    if (!shouldTrack || skipConsentCheck) return

    const onAcceptOrDecline = () => {
      if (!shouldTrack) return
      // Mix panel is classified as marketing cookie Needs to be ignored by TS
      // since this comes from the script itself from cookiebot no other way
      // https://www.cookiebot.com/en/developer/
      const consent: boolean =
        // @ts-expect-error
        Cookiebot?.consented &&
        // @ts-expect-error
        Cookiebot?.consent?.marketing

      optInOutTracking(consent, mixpanel)

      sendMessageToIframe({
        eventId: 'CONSENT_CHANGE',
        // @ts-expect-error
        eventData: { changeConsent: Cookiebot?.consent },
      })
    }

    const onDialogLoad = () => {
      if (!shouldTrack) return
      if (
        // @ts-expect-error
        Cookiebot &&
        // @ts-expect-error
        Boolean(Cookiebot?.regulations) &&
        // @ts-expect-error
        !Cookiebot?.regulations?.gdprApplies
      ) {
        optInOutTracking(true, mixpanel)
        // @ts-expect-error
        Cookiebot?.hide()
      }
    }

    // Only adds event listeners if not on tontineira.com

    // The event is triggered if the user accepts the use of cookies.
    // The event is also triggered if the user has consented at an earlier visit to the website.
    window.addEventListener('CookiebotOnAccept', onAcceptOrDecline)
    // The event is triggered if the user declines the use of cookies by clicking the decline-button
    // in the cookie consent dialog.
    // The event is also triggered if the user already has declined at an earlier visit to the website.
    window.addEventListener('CookiebotOnDecline', onAcceptOrDecline)
    // Fires when the cookie consent banner is displayed to the end user.
    window.addEventListener('CookiebotOnDialogDisplay', onDialogLoad)

    return () => {
      window.removeEventListener('CookiebotOnAccept', onAcceptOrDecline)
      window.removeEventListener('CookiebotOnDecline', onAcceptOrDecline)
      window.removeEventListener('CookiebotOnDialogDisplay', onDialogLoad)
    }
  }, [])
}
