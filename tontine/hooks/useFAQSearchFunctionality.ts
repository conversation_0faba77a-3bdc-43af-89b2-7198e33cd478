'use client'

import { useEffect, useState } from 'react'

import { splitArrayIntoEqualChunks } from '../helper-functions/UtilFunctions'
import type {
  FaqCategory,
  QuestionAndAnswer,
} from '../types/sections/faq-section.types'
import { useSearch } from './useSearch'

/**
 * Custom hook that manages search query state, performs search operations,
 * and handles search result display logic for FAQ search functionality
 */
export const useSearchFunctionality = (faqCategories: Array<FaqCategory>) => {
  const [searchResults, setSearchResults] = useState<Array<QuestionAndAnswer>>()

  // Combining all the questions, answers, and tags into one array, making one flat object
  const combinedArray = faqCategories.flatMap((item) =>
    (item.faqData || []).map((faqItem) => ({
      id: faqItem?.id,
      question: faqItem?.question,
      answer: faqItem?.answer,
      tags: faqItem?.tags,
    }))
  )

  const {
    foundOptions,
    searchQuery,
    setSearchQuery,
    nothingFound,
    setNothingFound,
  } = useSearch({
    options: combinedArray,
    searchBy: ['answer', 'question', 'tags'],
  })

  const handleSearch = (event?: React.FormEvent) => {
    event?.preventDefault()

    if (searchQuery !== '') {
      setSearchResults(foundOptions)
      setNothingFound(foundOptions.length === 0)
    }
  }

  const handleClearInput = () => {
    setSearchQuery('')
    setSearchResults([])
    setNothingFound(false)
  }
  // biome-ignore lint/correctness/useExhaustiveDependencies: <Will cause unnecessary re-renders>
  useEffect(() => {
    if (searchQuery === '') {
      setSearchResults([])
      setNothingFound(false)
    }
  }, [searchQuery])

  const dividedSearchResults = splitArrayIntoEqualChunks<QuestionAndAnswer>(
    searchResults ?? []
  )

  return {
    searchResults: dividedSearchResults,
    searchQuery,
    setSearchQuery,
    nothingFound,
    handleSearch,
    handleClearInput,
  }
}
