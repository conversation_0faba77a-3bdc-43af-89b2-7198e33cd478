import {
  CheckmarkCircleIcon,
  ErrorOutlineIcon,
  InfoOutlineIcon,
  WarningOutlineIcon,
} from '@sanity/icons'

/** A collection of constants used in the application. */
export const CONSTANTS = {
  HIDE_CONTENT_PARAM: 'hideNewsContent',
  DEFAULT_DOMAIN: 'https://tontine.com',
  NEXTJS_IMAGE_DEFAULT_SIZE_PROPERTY:
    '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  ERROR_PAGE_TIMER_SECONDS: 10,
  ERROR_PAGE_TIMER_MINUTES: 0,
  ERROR_PAGE_TIMER_HOURS: 0,
  INFO_BANNER_TIMEOUT: 30_000,
  TONTINE_FII_RATE: 4.83,
  TONTINE_BOL_RATE: 18.56,
  ANNUAL_YIELD_RATE: 6.73,
  EMAIL_VALIDATION_REGEX:
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
  DEBOUNCE_TIME: 500,
  INPUT_DEBOUNCE_TIME: 1_350,
  INPUT_MAX_LENGTH: 40,
  /**
   * Suggested google threshold to detect bot activity. If score is below 0.5 then
   * bot activity is detected
   */
  BOT_ACTIVITY_THRESHOLD: 0.5,
  IMAGE_QUALITY: {
    MINIMUM: 65,

    MEDIUM: 85,
    MAXIMUM: 91,
  },
  IMAGE_COMPRESSION: 9,
  RESIZE_IMAGE_THRESHOLD: 1600,
  RESIZE_IMAGE_SIZES: {
    min: { width: 960, height: 720 },
    max: { width: 1680, height: 1080 },
  },
  EXCLUDE_FILE_FORMATS: ['.svg', '.pdf', '.gif'],
  CAROUSEL: {
    DELAY: {
      HERO: 30_000,
      TESTIMONIALS: 15_000,
    },
    TWEEN_FACTOR: {
      DEFAULT: 0.2,
      SCALE: 0.1,
      PARALLAX: 0.3,
    },
  },
  MY_TONTINE_LITE_PROD: 'https://mytontinelite.netlify.app/',
  MY_TONTINE_LITE_STAGING: 'https://staging-mytontinelite.netlify.app/',
  MY_TONTINE_LITE_DEV: 'http://localhost:8080/',

  TONTINE_COMMUNITY_FORUM: 'https://community.tontine.com/signup/',

  /** Used for retrieving params from the website after redirect and passing them
   * to the webapp
   */
  PARAM_FOR_WEBAPP: 'ref',
  VERIFY_PARAM_FOR_WEBAPP: 'ver',
  COUNTRY_Q: 'country',
  INVESTMENT_STRAT: 'invStrat',
  SEX_Q: 'sex',
  CURRENT_AGE_Q: 'currAge',
  RETIREMENT_AGE_Q: 'retAge',
  TARGET_Q: 'target',
  ONE_TIME_DEPOSIT_Q: 'oneTimeDep',
  MONTHLY_TARGET_Q: 'monthlyTarget',

  ANALYTICS_DESCRIPTIONS: {
    CAROUSEL_SCROLL: 'Shows the item that the user has scrolled to or chosen',
    CONTACT_US_ERROR: 'Contact us error has occurred',
    VIDEO_SEEK: 'The time in seconds to where the video was seeked to',
  },
  ANALYTICS_THROTTLE_DURATION: 200,

  NET_PROMOTER_SCORE: 10,

  REGIONS: {
    EU: 'EU',
    US: 'US',
  },
  LANGUAGES: ['en', 'es', 'pt'],

  TOAST_TIMEOUT: 5_000,
  TOAST_ICONS: {
    success: CheckmarkCircleIcon,
    error: ErrorOutlineIcon,
    warning: WarningOutlineIcon,
    info: InfoOutlineIcon,
  },
  TOAST_VARIANTS: {
    success: 'bg-jade',
    error: 'bg-error-red',
    warning: 'bg-yellow-500',
    info: 'bg-brand',
  },

  BREAKPOINTS: [
    'base',
    'sm',
    'smd',
    'md',
    'xmd',
    'lg',
    'xlg',
    'xl',
    'xml',
    'xxl',
  ],

  DOMAIN_TRACKING_CONFIG: [
    {
      domain: 'ira.tontine.com',
      trackWithoutConsent: true,
    },
    {
      domain: 'tontine.com',
      trackWithoutConsent: false,
    },
  ],
} as const
