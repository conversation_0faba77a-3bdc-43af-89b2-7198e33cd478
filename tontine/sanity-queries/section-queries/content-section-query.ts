import { contentPostQuery } from '../content-post-queries/content-post-queries'

// Query used to fetch data from sanity which is used in the content section.
const contentSectionQuery: string = /* groq */ `
defined(dropdown)=>{
    "postType": dropdown
},
defined(featuredPost)=>{
    featuredPost[0]->{
        ${contentPostQuery}
    },
},
defined(postsArray) => {
    postsArray[]->|order(coalesce(publishDate, _createdAt) desc){
        ${contentPostQuery}
    },
},
`
export { contentSectionQuery }
