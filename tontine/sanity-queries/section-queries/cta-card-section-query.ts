import { linkSelectorQuery } from '../miscellaneous-queries/miscellaneous-queries'

// Query used to fetch data for the CTA card section
const ctaCardSectionQuery: string = /* groq */ `
defined(position) => {position},
defined(ctaCardButton)=>{
  "ctaCardButton": ctaCardButton->{
	"buttonLabel": stringTitle,
	  ${linkSelectorQuery},
  },
},
defined(intersectSections) => {intersectSections},
`
export { ctaCardSectionQuery }
