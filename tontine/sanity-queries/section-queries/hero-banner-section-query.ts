import { linkSelectorQuery } from '../miscellaneous-queries/miscellaneous-queries'

// This query is used to fetch hero banner section data from sanity
const heroBannerSection: string = /* groq */ `
defined(heroVariants) => {heroVariants},
defined(heroSectionButtons)=>{
  "heroSectionButtons":heroSectionButtons[]->{
      "buttonLabel": stringTitle,
      ${linkSelectorQuery},
  },
},
`
export { heroBannerSection }
