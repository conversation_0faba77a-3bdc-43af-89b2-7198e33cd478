import {
  sectionMediaQuery,
  sharedSectionDataQuery,
} from '../common-type-queries/common-types-queries'
import { heroBannerSection } from './hero-banner-section-query'

// Query used to fetch carousel hero section data.
const carouselHeroSectionQuery: string = /* groq */ `
defined(sectionSlug) => {"sectionSlug": slug.current},
defined(carouseHeroSectionPosts)=>{
  "carouseHeroSectionPosts": carouseHeroSectionPosts[]->{
    ${sharedSectionDataQuery}
    ${sectionMediaQuery}
    ${heroBannerSection}
  },
},
`
// Not all fields are using defined() function
export { carouselHeroSectionQuery }
