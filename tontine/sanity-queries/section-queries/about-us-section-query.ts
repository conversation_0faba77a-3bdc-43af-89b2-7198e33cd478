import { sharedSectionDataQuery } from '../common-type-queries/common-types-queries'

// Query used to fetch about us section data from sanity
const aboutUsSectionQuery: string = /* groq */ `
  ${sharedSectionDataQuery}
  defined(aboutUsSectionContent) => {aboutUsSectionContent},
  defined(aboutUsSectionSubtitleCaption) => {aboutUsSectionSubtitleCaption},
  defined(aboutUsSectionAddressIcon) => {
    "aboutUsSectionAddressIcon": {
      "alt": aboutUsSectionAddressIcon.asset->altText,
      "src": aboutUsSectionAddressIcon.asset->url,
    },
  },
  defined(aboutUsSectionAddress) => {aboutUsSectionAddress},

  defined(aboutUsSectionEmailIcon) => {
    "aboutUsSectionEmailIcon": {
      "alt": aboutUsSectionEmailIcon.asset->altText,
      "src": aboutUsSectionEmailIcon.asset->url,
    },
  },

  defined(aboutUsSectionEmail) => {aboutUsSectionEmail},
  defined(aboutUsSectionPhoneIcon) => {
  "aboutUsSectionPhoneIcon": {
    "alt": aboutUsSectionPhoneIcon.asset->altText,
    "src": aboutUsSectionPhoneIcon.asset->url,
  },
},

defined(aboutUsSectionPhoneNumber) => {aboutUsSectionPhoneNumber},
defined(aboutUsSectionButtons)=>{
  "aboutUsSectionButtons":{
    "buttonLabel": aboutUsSectionButtons->buttonLabel,
    "buttonSlug": aboutUsSectionButtons->buttonSlug.current,
  },
},
`
// Update buttons in query so it wont be ... but the actual query.
export { aboutUsSectionQuery }
