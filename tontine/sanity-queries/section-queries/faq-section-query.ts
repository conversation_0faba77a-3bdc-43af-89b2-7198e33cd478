// Query used to fetch data from sanity which is used in FAQ section.

import { linkSelectorQuery } from '../miscellaneous-queries/miscellaneous-queries'

const faqCategoriesFields: string = /* groq */ `
"categoryTitle": categoryTitle,
"faqData": faqQuestionAndAnswer[]->{
    "question": localizedQuestion,
    "answer": localizedAnswer,
    "tags": tags,
    "id": _id,
},
`
const faqSectionQuery: string = /* groq */ `
defined(webSchemaFaqsContext) => {webSchemaFaqsContext},
defined(webSchemaFaqsContextType) => {webSchemaFaqsContextType},
defined(faqCategories)=>{faqCategories[]{${faqCategoriesFields}}},
defined(faqButton)=>{
  "faqButton": faqButton->{
	"buttonLabel": stringTitle,
      ${linkSelectorQuery},
    },
},
defined(placeholderText)=>{placeholderText},
defined(faqAdditionalInfo)=>{faqAdditionalInfo},
`
export { faqSectionQuery }
