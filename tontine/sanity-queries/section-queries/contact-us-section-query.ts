// Query used to fetch data used in contact us section
const contactUsQuery: string = /* groq */ `
defined(placeholderEmailInput) => {placeholderEmailInput},
defined(placeholderNameInput) => {placeholderNameInput},
defined(placeholderSelectInput) => {placeholderSelectInput},
defined(placeholderSurnameInput) => {placeholderSurnameInput},
defined(placeholderTextareaInput) => {placeholderTextareaInput},
defined(submitButtonLabel) => {submitButtonLabel},
defined(disclaimer) => {disclaimer},
defined(selectInputOptions)=>{
    "selectInputOptions":selectInputOptions[],
},
`
export { contactUsQuery }
