import {
  localizedPortableTextQuery,
  sectionMediaQuery,
} from '../common-type-queries/common-types-queries'
import { infoBlockQuery } from '../miscellaneous-queries/miscellaneous-queries'

// Query used to fetch data from sanity which is used in info hub section
const infoHubSectionQuery: string = /* groq */ `
defined(infoHubAdditionalInfo)=>{
    "additionalInfo": infoHubAdditionalInfo {${localizedPortableTextQuery}},
},
defined(layout) => { layout },
${sectionMediaQuery}
defined(infoHubCardList)=>{
	"infoHubCardList": infoHubCardList[]->{
		${infoBlockQuery}
	},
},
`
export { infoHubSectionQuery }
