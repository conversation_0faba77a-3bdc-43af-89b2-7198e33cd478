import { linkSelectorQuery } from '../miscellaneous-queries/miscellaneous-queries'

// Query that fetches data from sanity for the featured section..
const featuredSectionQuery: string = /* groq */ `
defined(isHero) => {isHero},
defined(featuredSectionMediaDirectionToggle) => {featuredSectionMediaDirectionToggle},
defined(featuredSectionElementCollection) => {featuredSectionElementCollection},
defined(featuredSectionButtons)=>{
  "featuredSectionButtons":featuredSectionButtons[]->{
    "buttonLabel": stringTitle,
      ${linkSelectorQuery},
  },
},
defined(featuredSectionSubscribeToggle) => {featuredSectionSubscribeToggle},
`
export { featuredSectionQuery }
