// Query used to fetch data that it's used in subscribe section.
const subscribeQuery: string = /* groq */ `
defined(subscribePlaceholderEmailInput) => {subscribePlaceholderEmailInput},
defined(subscribePlaceholderNameInput) => {subscribePlaceholderNameInput},
defined(subscribePlaceholderSurnameInput) => {subscribePlaceholderSurnameInput},
defined(subscribeCheckboxLabel) => {subscribeCheckboxLabel},
defined(subscribeCheckboxLinkText) => {subscribeCheckboxLinkText},
defined(subscribeCheckboxLink) => {subscribeCheckboxLink},
defined(subscribeSubmitButtonLabel) => {subscribeSubmitButtonLabel},
`
export { subscribeQuery }
