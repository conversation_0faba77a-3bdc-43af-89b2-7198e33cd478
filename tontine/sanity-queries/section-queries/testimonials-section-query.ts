/**
 * This query fetches data from sanity about the testimonial section
 */
const testimonialSectionQuery: string = /* groq */ `
defined(testimonialPosts)=>{
  "testimonialPosts" : testimonialPosts[]{
    defined(testimonialReviewerCompany) => {testimonialReviewerCompany},
    defined(testimonialPostReviewerName) => {testimonialPostReviewerName},
    defined(testimonialReviewerProfession) => {testimonialReviewerProfession},
    defined(reviewerLinkText) => {reviewerLinkText},
    defined(reviewerLinkUrl) => {reviewerLinkUrl},
    defined(localizedTestimonialPost) => {localizedTestimonialPost},
    defined(testimonialReviewerImage)=>{
      "testimonialReviewerImage":{
        "alt": testimonialReviewerImage.asset->altText,
        "src": testimonialReviewerImage.asset->url,
      },
    },
  },
},
`
export { testimonialSectionQuery }
