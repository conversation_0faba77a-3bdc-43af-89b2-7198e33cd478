import { localizedPortableTextQuery } from '../common-type-queries/common-types-queries'

// Query used to fetch data for the markdown section
const markdownSectionQuery: string = /* groq */ `
stringTitle,
defined(styling) => {"textStyling": styling},
defined(position) => {"textPosition": position},
defined(localeMarkdown) => {"markdown": localeMarkdown {${localizedPortableTextQuery}}},
`
export { markdownSectionQuery }
