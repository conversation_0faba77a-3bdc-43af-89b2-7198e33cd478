import {
  localizedPortableTextQuery,
  sharedSectionDataQuery,
} from '../common-type-queries/common-types-queries'
// Query used to fetch data from sanity which is used in glossary section
const glossarySectionQuery: string = /* groq */ `
  "glossaries":glossaries[]->{
	${sharedSectionDataQuery}
    "markdown": localeMarkdown {${localizedPortableTextQuery}},
  },
`
export { glossarySectionQuery }

// defined(glossaries)=>{
//   "glossaries":glossaries[]{
// 	...,
// 	${sharedSectionDataQuery}
//   stringTitle,
//     "markdown": localeMarkdown {${localizedPortableTextQuery}},
//   },
// },
