import { linkSelectorQuery } from '../miscellaneous-queries/miscellaneous-queries'

// Query used to fetch data for the CTA section
const ctaSectionQuery: string = /* groq */ `
defined(ctaAdditionalText) => {ctaAdditionalText},
defined(ctaSectionButtons)=>{
  "ctaSectionButtons": ctaSectionButtons[]->{
    defined(buttonLabel) => {buttonLabel},
    ${linkSelectorQuery},
  },
},
`
export { ctaSectionQuery }
