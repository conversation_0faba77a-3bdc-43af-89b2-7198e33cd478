const imagesQuery = `
"images": array::unique(
  array::compact(
    pageSections[]->{
      "combinedImages": [
        sectionImage.asset->url,
        videoThumbnail.asset->url,
        icon.asset->url,
        ...infoHubCardList[]->.icon.asset->url,
        ...testimonialPosts[].testimonialReviewerImage.asset->url,
        ...carouseHeroSectionPosts[]->.sectionImage.asset->url,
        ...carouselPosts[].carouselPostReviewerImage.asset->url,
        ...partnersCompanies[]->.partnerImage.asset->url,
        ...companiesLogosIcon[]->.imageField.asset->url,
        ...teamMembers[]->.personImage.asset->url,
        ...teamMembers[]->.personAlternativeImage.asset->url,
        ...featuredPost[]->.postImage.asset->url,
        ...postsArray[]->.postImage.asset->url,
      ]
    }.combinedImages[]
  )
)
`

const videosQuery = `
"videos": array::compact(
  pageSections[]->{
    ...sectionVideo{
      ...asset->{
        defined(playbackId) => {"playbackId": playbackId},
        defined(data.duration) => {"duration": data.duration},
        "title": filename
      },
      "thumbnail_loc": coalesce(^.videoThumbnail, ^.sectionImage).asset->url,
      "description": coalesce(pt::text(^.localizedTitle.en), ^.stringTitle.en)
    }
  }
)[defined(@.playbackId)]  // 👈 Key filter added here
`

const contentPostQuery = /* groq */ `
"updatedAt": _updatedAt,
"pageSlug": coalesce(^.pageSlug.current + "/" + coalesce(slug.current, ""), slug.current + "/"),
"images": [postImage.asset->url],
defined(manuscript) => { 
  manuscript {
    asset->{
      originalFilename,
      "updatedAt": _updatedAt,

    },
  },
},
`

const contentPostVideoQuery = /* groq */ `
"videos": array::compact([videoFile{
  ...asset->{
    defined(playbackId)=>{"playbackId": playbackId},
    defined(data.duration)=>{"duration": data.duration},
    "title": filename,
  },
  "thumbnail_loc": coalesce(^.thumbnail, ^.postImage).asset->url,
  "description": coalesce(pt::text(^.localizedTitle.en), pt::text(^.localizedSubtitle.en))
}])
`

const siteMapQuery = /* groq */ `
*[_id == $WEBSITE_TONTINE][0]{
  "pagesOnWebsite": array::unique(
    array::compact(
      [
        homepage->{
          "pageSlug": pageSlug.current,
          "updatedAt": _updatedAt,
          ${imagesQuery},
          ${videosQuery},
        },
        ...pagesOnWebsite[]->{
          "pageSlug": pageSlug.current,
          "updatedAt": _updatedAt,
          ${imagesQuery},
          ${videosQuery},
        },
        ...pagesOnWebsite[]->{
          "sections": pageSections[@->_type == 'contentSection']->postsArray[]->{
          ${contentPostQuery}
          ${contentPostVideoQuery}
          }
        }.sections[],
        ...pagesOnWebsite[]->{
          "sections": pageSections[@->_type == 'contentSection']->featuredPost[]->{
            ${contentPostQuery}
            ${contentPostVideoQuery}
          }
        }.sections[]
      ]
    )
  )
}.pagesOnWebsite
`

export { siteMapQuery }
