const corporationSchemaQuery: string = /* groq */ `
_type,
defined(corporationSchemaName) => {corporationSchemaName},
defined(corporationSchemaDescription) => {corporationSchemaDescription},
defined(corporationSchemaWorkingHours) => {corporationSchemaWorkingHours},
defined(corporationSchemaActionableFeedbackPolicy) => {corporationSchemaActionableFeedbackPolicy},
defined(corporationSchemaStreetAddress) => {corporationSchemaStreetAddress},
defined(corporationSchemaAddressLocality) => {corporationSchemaAddressLocality},
defined(corporationSchemaAddressRegion) => {corporationSchemaAddressRegion},
defined(corporationSchemaPostalCode) => {corporationSchemaPostalCode},
defined(corporationSchemaAddressCountry) => {corporationSchemaAddressCountry},
defined(corporationSchemaCorrectionsPolicy) => {corporationSchemaCorrectionsPolicy},
defined(corporationSchemaDiversityPolicy) => {corporationSchemaDiversityPolicy},
defined(corporationSchemaDiversityStaffingReport) => {corporationSchemaDiversityStaffingReport},
defined(corporationSchemaDuns) => {corporationSchemaDuns},
defined(corporationSchemaEmail) => {corporationSchemaEmail},
defined(corporationSchemaEthicsPolicy) => {corporationSchemaEthicsPolicy},
defined(corporationSchemaFaxNumber) => {corporationSchemaFaxNumber},
defined(corporationSchemaFounder) => {corporationSchemaFounder},
defined(corporationSchemaKeywords)=>{corporationSchemaKeywords},
defined(corporationSchemaFoundingDate) => {corporationSchemaFoundingDate},
defined(corporationSchemaFoundingAddressLocality) => {corporationSchemaFoundingAddressLocality},
defined(corporationSchemaFoundingAddressRegion) => {corporationSchemaFoundingAddressRegion},
defined(corporationSchemaFoundingPostalCode) => {corporationSchemaFoundingPostalCode},
defined(corporationSchemaFoundingAddressCountry) => {corporationSchemaFoundingAddressCountry},
defined(corporationSchemaGlobalLocationNumber) => {corporationSchemaGlobalLocationNumber},
defined(corporationSchemaKnowsAbout) => {corporationSchemaKnowsAbout},
defined(corporationSchemaKnowsLanguage) => {corporationSchemaKnowsLanguage},
defined(corporationSchemaLegalName) => {corporationSchemaLegalName},
defined(corporationSchemaLeiCode) => {corporationSchemaLeiCode},
defined(corporationSchemaLogo) => {corporationSchemaLogo},
defined(corporationSchemaNumberOfEmployees) => {corporationSchemaNumberOfEmployees},
defined(corporationSchemaPublishingPrinciples) => {corporationSchemaPublishingPrinciples},
defined(corporationSchemaSlogan) => {corporationSchemaSlogan},
defined(corporationSchemaTaxID) => {corporationSchemaTaxID},
defined(corporationSchemaTelephone) => {corporationSchemaTelephone},
defined(corporationSchemaVatID) => {corporationSchemaVatID},
defined(corporationSchemaAlternateName) => {corporationSchemaAlternateName},
defined(corporationSchemaUrl) => {corporationSchemaUrl},
`

export { corporationSchemaQuery }
