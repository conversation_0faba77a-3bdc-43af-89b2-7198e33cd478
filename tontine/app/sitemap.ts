import type { MetadataRoute } from 'next'
import { CONSTANTS } from '../data-resource/constants'
import {
  generatePathWithTrailingSlash,
  getAttribute,
  getMuxContentUrl,
} from '../helper-functions/UtilFunctions'
import { fetchSanityData } from '../sanity-queries/query-fetch-function'
import { siteMapQuery } from '../sanity-queries/sitemap-queries'

type SitemapData = Array<{
  pageSlug: string
  updatedAt: string
  images: Array<string>
  videos: Array<{
    playbackId: string
    duration: number
    title: string
    description: string
    thumbnail_loc: string
  }>
  manuscript?: {
    asset: {
      originalFilename: string
      updatedAt: string
    }
  }
}>

function getChangeFrequency(updatedAtStr: string) {
  const updatedAt = new Date(updatedAtStr)
  const now = new Date()
  const diffDays = (now.getTime() - updatedAt.getTime()) / (1000 * 60 * 60 * 24)

  if (diffDays < 7) {
    return 'daily'
  }
  if (diffDays < 30) {
    return 'weekly'
  }
  return 'monthly'
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const pages = await fetchSanityData<SitemapData>({ query: siteMapQuery })

  return pages?.map((page) => {
    // If manuscript exists, treat as PDF
    const isPDF = Boolean(page?.manuscript)
    const pageSlug = isPDF
      ? generatePathWithTrailingSlash({
          segments: [
            page?.pageSlug,
            page?.manuscript?.asset?.originalFilename ?? '',
          ],
        })
      : page?.pageSlug

    const updatedAt = isPDF
      ? (page?.manuscript?.asset?.updatedAt ?? page?.updatedAt)
      : page?.updatedAt

    const priority = isPDF ? 0.7 : 0.8

    return {
      url: generatePathWithTrailingSlash({
        segments: [CONSTANTS.DEFAULT_DOMAIN, pageSlug ?? ''],
        endsWithSlash: true,
      }),
      lastModified: updatedAt,
      changeFrequency: getChangeFrequency(updatedAt),
      priority,
      ...getAttribute(!isPDF, 'alternates', {
        languages: CONSTANTS.LANGUAGES.slice(1).reduce(
          (acc, lang) => {
            acc[lang] = generatePathWithTrailingSlash({
              segments: [CONSTANTS.DEFAULT_DOMAIN, lang, pageSlug ?? ''],
              endsWithSlash: true,
            })
            return acc
          },
          {} as Record<string, string>
        ),
      }),
      images: page?.images,
      videos: page?.videos?.map((video) => ({
        video: video?.playbackId,
        title: video?.title,
        description: video?.description,
        thumbnail_loc: video?.thumbnail_loc,
        content_loc: getMuxContentUrl(video?.playbackId),
        duration: video?.duration,
      })),
    }
  })
}
