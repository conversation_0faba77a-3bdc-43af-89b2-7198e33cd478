'use client'

import { RedirectType, redirect, usePathname } from 'next/navigation'
import { CONSTANTS } from '../data-resource/constants'
import type { LanguagesType } from '../types/common.types'

export default function NotFound() {
  const pathName = usePathname()
  const locale = pathName.split('/')[1] as LanguagesType
  const finalLocale = CONSTANTS.LANGUAGES.includes(locale) ? locale : 'en'
  return redirect(
    finalLocale === 'en' ? '/not-found' : `/${finalLocale}/not-found`,
    RedirectType.replace
  )
}
