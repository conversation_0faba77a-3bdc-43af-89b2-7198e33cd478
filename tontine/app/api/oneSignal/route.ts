import { CONSTANTS } from '../../../data-resource/constants'
import strings from '../../../data-resource/strings.json'
import { API_STATUS } from '../../../serverless/API'
import { logServerless } from '../../../serverless/ApiUtilFunctions'
import {
  contactUsTemplateId,
  defaultRecipientMail,
  defaultRecipientTemplateId,
  oneSignalAppId,
} from '../../../serverless/keys'
import type { ContactUsBodyType } from '../../../serverless/serverless.types'
import { addNewUser, sendEmail } from '../../../serverless/services'

/**
 * Handles the POST request for the contact form submission process.
 *
 * This function processes a contact form submission by verifying turnsitle,
 * adding a new user to OneSignal, and sending emails to both the default recipient and the user.
 * It retrieves user data from the request body, along with location and IP information from request headers.
 *
 * The function performs the following steps:
 * 1. Verifies the turnstile token.
 * 2. Adds the user to OneSignal's user list.
 * 3. Sends an email to the default recipient with the user's submission data.
 * 4. Sends a confirmation email to the user.
 */
export async function POST(req: Request) {
  try {
    const reqFormData = (await req.json()) as ContactUsBodyType
    const { firstName, lastName, selectedOption, email, message } = reqFormData

    const country = req.headers.get('cf-ipcountry') ?? CONSTANTS.REGIONS.EU
    const timezone = req.headers.get('cf-timezone') ?? ''
    const lat = req.headers.get('cf-iplatitude') ?? ''
    const long = req.headers.get('cf-iplongitude') ?? ''

    if (!email) {
      logServerless({
        message: 'Client did not provide email',
        logLevel: 'error',
      })
      return new Response('Client did not provide email', {
        status: API_STATUS.BAD_REQUEST,
      })
    }

    /** Whole flow starts from here */

    await addNewUser({
      email,
      firstName,
      lastName,
      country,
      selectedOption,
      message,
      lat,
      long,
      timezone,
    })

    // After a successful user creation we send a default recipient email
    await sendEmail({
      app_id: oneSignalAppId,
      include_email_tokens: [defaultRecipientMail],
      target_channel: 'email',
      template_id: defaultRecipientTemplateId,
      email_subject: strings.FORM_EMAIL_SUBJECT,
      custom_data: {
        first_name: firstName,
        last_name: lastName,
        email,
        contact_us_message: message,
        contact_us_selected_option: selectedOption,
        country,
      },
      include_unsubscribed: true,
    })

    // Then we send the affirmation email to the user

    await sendEmail({
      app_id: oneSignalAppId,
      include_email_tokens: [email],
      target_channel: 'email',
      template_id: contactUsTemplateId,
      email_subject: strings.FORM_SUBMISSION_CONFIRM_MAIL_SUBJECT,
      include_unsubscribed: true,
    })

    return new Response(
      'Successfully added a new user to OneSignal, sent the submission data to the default recipient, and sent an affirmation email to the new user.',
      { status: API_STATUS.OK }
    )
  } catch (error) {
    logServerless({
      message: 'Unexpected error occurred',
      error,
      logLevel: 'error',
    })
    return new Response('Unexpected error', {
      status: API_STATUS.INTERNAL_SERVER_ERROR,
    })
  }
}
