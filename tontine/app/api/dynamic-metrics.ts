import { axiosRequest } from '../../hooks/Axios/useAxios'
import { API } from '../../serverless/API'
import { logServerless } from '../../serverless/ApiUtilFunctions'
import type {
  DynamicDataResult,
  GeorgeCurrencyType,
  ReturnMetricsType,
} from '../../types/common.types'

// Set a timeout to cancel the request
const REQUEST_TIMEOUT_IN_MS =
  process.env.NODE_ENV === 'development' ? 2_000 : 3_000

async function fetchData({
  api,
  returnMetrics,
  georgeCurrency,
  errorMessage,
}: {
  api: string
  returnMetrics?: ReturnMetricsType
  georgeCurrency?: GeorgeCurrencyType
  errorMessage: string
}) {
  try {
    const { data } = await axiosRequest<DynamicDataResult>({
      retries: 1,
      backoff_factor: 3,
      url: api,
      config: {
        timeout: REQUEST_TIMEOUT_IN_MS,
        params: {
          currency: returnMetrics?.currency,
          residency: george<PERSON>urrency ?? 'USA',
        },
      },
    })

    if (data) {
      return data
    }

    throw new Error(`No data returned while fetching ${errorMessage}`)
  } catch (error) {
    logServerless({
      message: `Could not ${errorMessage}`,
      logLevel: 'error',
      error,
    })
    return undefined
  }
}

/**
 * Fetches the returns rate from the Tontine Trust API.
 */
export async function fetchReturnRate(returnMetrics: ReturnMetricsType) {
  const data = await fetchData({
    api: API.returnRate,
    returnMetrics,
    errorMessage: 'return rate',
  })

  return data?.[returnMetrics?.strategy]?.rate
}

/**
 * Fetches the george yields rate from the Tontine Trust API.
 */
export async function fetchGeorgeYield(georgeCurrency: GeorgeCurrencyType) {
  const data = await fetchData({
    api: API.georgeYield,
    georgeCurrency,
    errorMessage: 'george yield',
  })

  return data?.annualYield
}
