import mixpanel from 'mixpanel-browser'

import { CONSTANTS } from '../../data-resource/constants'
import {
  detectDeviceType,
  throttle,
} from '../../helper-functions/UtilFunctions'
import { logServerless } from '../../serverless/ApiUtilFunctions'
import { shouldTrack } from '../../serverless/keys'
import type { TrackProps } from '../../types/Analytics/Analytics.types'

const trackAPI = async ({ event, properties }: TrackProps) => {
  try {
    if (!event) {
      throw new TypeError('Required event argument not provided')
    }

    if (!properties) {
      throw new TypeError(
        `For event:${event.toString()} Required property: >>properties<< not provided`
      )
    }

    mixpanel.track(event, {
      ...properties,
      source: 'website',
      device: detectDeviceType(),
    })
  } catch (error) {
    logServerless({
      message: 'Could not send analytics data',
      logLevel: 'error',
      error,
    })
  }
}

/**
 * Sends an API request to track user actions. The function is throttled to
 * 200ms by default.
 *
 * If no arguments are provided the function will not track anything.
 */
const track = throttle((trackProperties: TrackProps) => {
  if (shouldTrack) {
    // If no properties provided do not track
    if (trackProperties) {
      // biome-ignore lint/complexity/noVoid: Only exception of this rule needed
      void trackAPI({
        event: trackProperties.event,
        properties: trackProperties.properties,
      })
    }
  }
}, CONSTANTS.ANALYTICS_THROTTLE_DURATION)

export default track
