import { draftMode } from 'next/headers'
import { redirect } from 'next/navigation'

// This activates the Next.js draft feature, which listens for content changes from Sanity.
export async function GET(req: Request) {
  const { searchParams } = new URL(req.url)
  const secret = searchParams.get('secret')
  const slug = searchParams.get('slug')

  if (secret !== 'PREVIEWMODE' || !slug) {
    return new Response('Invalid token', { status: 401 })
  }

  const draft = await draftMode()
  draft.enable()

  redirect(slug)
}
