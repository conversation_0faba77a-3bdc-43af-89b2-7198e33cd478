import { axiosRequest } from '../../../hooks/Axios/useAxios'
import { API, API_STATUS } from '../../../serverless/API'
import { logServerless } from '../../../serverless/ApiUtilFunctions'
import { turnstileSecret } from '../../../serverless/keys'
import type { TurnstileResponse } from '../../../serverless/serverless.types'

/** API endpoint to verify a turnstile token. */
export async function POST(req: Request) {
  try {
    const { token }: { token: string } = await req.json()

    const ip = req.headers.get('cf-connecting-ip') ?? ''
    const idempotencyKey = crypto.randomUUID()

    const config = {
      method: 'post',
      data: {
        response: token,
        secret: turnstileSecret,
        remoteip: ip,
        idempotency_key: idempotencyKey,
      },
    }

    // Perform the initial validation request
    const validationResponse = await axiosRequest<TurnstileResponse>({
      url: API.siteVerify,
      config,
    })

    if (!validationResponse?.data?.success) {
      throw new TypeError(
        `Initial Turnstile validation failed - ${validationResponse?.data?.['error-codes']?.join(' ')}`
      )
    }

    // Perform the subsequent validation request if the first one is successful
    const subsequentResponse = await axiosRequest<TurnstileResponse>({
      url: API.siteVerify,
      config,
    })

    if (!subsequentResponse?.data?.success) {
      throw new TypeError(
        `Subsequent Turnstile validation failed - ${subsequentResponse?.data?.['error-codes']?.join(' ')}`
      )
    }

    return new Response('Success', { status: API_STATUS.OK })
  } catch (error) {
    logServerless({
      message: 'Unexpected error occurred',
      error,
      logLevel: 'error',
    })
    return new Response('Unexpected error', {
      status: API_STATUS.INTERNAL_SERVER_ERROR,
    })
  }
}

/** --- Secret tokens ---

Always passes - success

TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA

Always fails - missing-input-response 

TURNSTILE_SECRET_KEY=2x0000000000000000000000000000000AA

Yields a “token already spent” error - timeout-or-duplicate 

TURNSTILE_SECRET_KEY=3x0000000000000000000000000000000AA

 */
