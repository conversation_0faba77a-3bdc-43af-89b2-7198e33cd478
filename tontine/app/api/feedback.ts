import { getAttribute } from '../../helper-functions/UtilFunctions'
import { axiosRequest } from '../../hooks/Axios/useAxios'
import { API } from '../../serverless/API'
import { logServerless } from '../../serverless/ApiUtilFunctions'
import { isCypress } from '../../serverless/keys'

/** Sends feedback data to the feedback API if turnstile determines user is not bot. */
export const sendFeedback = async ({
  token,
  rating,
  text,
}: {
  rating: number
  text?: string
  token?: FormDataEntryValue
}) => {
  try {
    if (!isCypress) {
      await axiosRequest({
        url: API.verifyTurnstile,
        config: {
          data: { token },
          method: 'post',
        },
      })
    }

    await axiosRequest({
      url: API.feedback,
      config: {
        data: { rating, ...getAttribute(Boolean(text), 'text', text) },
        method: 'post',
      },
    })
  } catch (error) {
    logServerless({
      logLevel: 'error',
      error,
      message: 'Failed to submit feedback',
    })
    throw error
  }
}
