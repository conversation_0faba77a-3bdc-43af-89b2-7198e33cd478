@import "tailwindcss";

html {
  @apply scroll-smooth;
}

@layer colors {
  @theme {
    --color-background-100: #ffffff;
    --color-background-200: #fdfdfb;
    --color-background-300: #fcfbfa;
    --color-background-400: #fbfaf8;
    --color-background: #faf9f6;
    --color-background-600: #c8c7c5;
    --color-background-700: #969594;
    --color-background-800: #646462;
    --color-background-900: #323231;
    --color-background-1000: #000000;

    --color-fg-400: #717477;
    --color-fg: #4d5155;
    --color-fg-600: #3e4144;
    --color-fg-700: #2b2b27;

    --color-grey-100: #e9eaf030;
    --color-grey-200: #f8f8f8;
    --color-grey-300: #e6e6e6;
    --color-grey-350: #dadada;
    --color-grey-400: #cccccc;
    --color-grey-450: #b2b2b2;
    --color-grey: #808080;
    --color-grey-550: #6b7280;
    --color-grey-600: #666666;
    --color-grey-650: #535353;
    --color-grey-700: #4d4d4d;
    --color-grey-800: #343536;
    --color-grey-900: #1a1a1a;

    --color-golden-yellow: #ffcc04;
    --color-yellow: #dfb200;
    --color-jade: #22b573;
    --color-error-red: #e53e3e;

    --color-brand-50: #f0f5fa;
    --color-brand-100: #e3f1ff;
    --color-brand-150: #dceeff;
    --color-brand-200: #a9c8e5;
    --color-brand-300: #7facd9;
    --color-brand-350: #7ebaf7;
    --color-brand-400: #5fa1d6;
    --color-brand-450: #5491cc;

    --color-brand: #2975bf;

    --color-brand-550: #2273bd;
    --color-brand-600: #215e99;
    --color-brand-700: #194673;
    --color-brand-750: #013e59;
    --color-brand-800: #102f4c;
    --color-brand-900: #081726;
  }
}

@layer breakpoints {
  @theme {
    --breakpoint-xxl: 1536px;
    --breakpoint-xml: 1400px;
    --breakpoint-xl: 1280px;
    --breakpoint-xlg: 1100px;
    --breakpoint-lg: 992px;
    --breakpoint-xmd: 880px;
    --breakpoint-md: 768px;
    --breakpoint-smd: 640px;
    --breakpoint-sm: 480px;
    --breakpoint-base: 0px;
  }
}

@layer shadows {
  @theme {
    --single: 1fr;
    --repeat-2: repeat(2, 1fr);
    --repeat-3: repeat(3, 1fr);

    --shadow-sm: 0px 1px 4px rgba(0, 0, 0, 0.1);
    --shadow-smx: 0px 2px 4px rgba(0, 0, 0, 0.2);
    --shadow-md: 0px 2px 5px rgba(0, 0, 0, 0.2);
    --shadow-mdx: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
    --shadow-brand-transparent: 0px 0px 10px 0px #2975bf40;
    --shadow-brand-light: 0px 0px 10px 0px #2975bf70;
    --outline-light: rgba(0, 0, 0, 0.05);

    --drop-shadow-md: 0px 2px 4px rgba(0, 0, 0, 0.5);
  }
}

@layer nav {
  @theme {
    --nav-height: 5.4rem;
    --nav-height-offset: calc(100dvh - 5.4rem);
  }
  .nav-box-shadow {
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.2);
  }
}

@layer skeleton {
  @theme {
    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) forwards infinite;
    @keyframes pulse {
      0% {
        background-color: var(--color-grey-350);
      }
      50% {
        background-color: var(--color-brand-50);
      }
      100% {
        background-color: var(--color-grey-350);
      }
    }
  }
}

@layer toast {
  @theme {
    --animate-toast-enter: toast-enter 300ms cubic-bezier(0.4, 0, 0.6, 1);
    @keyframes toast-enter {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  }
}

@layer components {
  @theme {
    @keyframes fadeIn {
      0% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }
  }

  .roundedImage {
    /* border-radius: var(--cornerRadius); */
    img {
      clip-path: ellipse(var(--cornerRadius) 120%);
    }
  }

  @apply placeholder:select-none;

  .fadeInElement {
    @apply animate-[fadeIn_0.2s_ease-in-out_1.5s_forwards];
  }

  .opacity-start {
    @apply starting:opacity-0 transition-[opacity] duration-300;
  }
  ::selection {
    background: var(--color-brand-100);
  }

  .button {
    &.transparent {
      @apply text-brand outline-brand/80 md:hover:bg-brand-50 md:hover:text-brand md:hover:shadow-brand-transparent text-base font-semibold outline-1;

      &.alternate {
        @apply text-background-100 outline-background-100/80 md:hover:bg-brand-50/90 md:hover:text-brand md:hover:shadow-brand-transparent outline-1;
      }
    }

    &.solid {
      @apply bg-brand text-background-100 md:hover:shadow-brand-light text-base font-semibold shadow-sm md:hover:opacity-90;

      &.inverted {
        @apply bg-background-100 text-brand md:hover:shadow-brand-transparent;
      }
    }

    &.subtle {
      @apply bg-grey-200 text-grey-550 outline-(--outline-light) md:hover:bg-brand-50 md:hover:shadow-brand-transparent text-base font-semibold shadow-sm outline-1;
    }

    &.golden {
      @apply w-38 bg-golden-yellow text-brand-750 md:hover:bg-brand-50 mt-2.5 text-base;
    }
  }

  button,
  .button {
    @apply flex items-center justify-center rounded-full px-6 py-3 transition-all;
  }

  button:not(:disabled) {
    @apply cursor-pointer transition duration-[0.25s] ease-linear;
  }

  button:disabled {
    @apply pointer-events-none grayscale filter;
  }

  .content-section-width {
    @apply mx-auto w-fit sm:w-fit md:w-[85%] md:grid-cols-2 lg:w-[80%] lg:grid-cols-2 xl:w-[80%];
  }
  .section-width {
    @apply mx-auto w-[100%] max-w-[112.5rem] px-8 md:w-[90%] lg:w-[80%];
  }

  .sectionWrapper {
    min-height: var(--nav-height-offset);
  }

  .section,
  section {
    max-width: 125rem;
    margin-inline: auto;
    scroll-margin-top: var(--nav-height);
    &.heading {
      font-weight: 600;
      text-align: center;
      font-size: 1.8rem;
      line-height: 1.875rem;
      color: var(--color-brand);
    }

    &.wrapper {
      margin-block: 2rem;
    }

    &.sub-heading {
      text-align: center;
      font-size: 1.3rem;
      color: var(--description-dark-gray);
    }
  }

  .person {
    box-shadow:
      rgba(0, 0, 0, 0.25) 0px 0.0625em 0.0625em,
      rgba(0, 0, 0, 0.25) 0px 0.125em 0.5em,
      rgba(255, 255, 255, 0.1) 0px 0px 0px 1px inset;
    .aiImage {
      opacity: 0;
    }

    @media only screen and (min-width: 768px) {
      &:hover {
        .aiImage {
          opacity: 1;
        }
      }
    }
  }

  .content-block {
    @apply px-8 md:px-[16.5%];
  }

  .markdown-defaults {
    @apply relative;

    a {
      @apply text-(--color-brand) font-semibold underline;
    }

    p {
      @apply text-(--color-fg) leading-7 md:leading-8;
    }

    h1 {
      @apply text-(--color-fg) text-4xl font-bold leading-9 md:text-5xl md:leading-[2.75rem];
    }

    h2 {
      @apply text-(--color-fg) text-2xl font-bold leading-8 md:text-3xl md:leading-10;
    }

    h3 {
      @apply text-(--color-fg) mt-2 text-2xl font-semibold leading-7;
    }

    h4 {
      @apply text-(--color-fg) mt-2 text-xl font-semibold;
    }

    li {
      @apply text-(--color-fg) my-2 list-none pl-8 ml-3;
      background: url("/assets/images/tontine-bullet-icon.svg") no-repeat;
      @apply bg-[length:17px] bg-[3px_5px];

      li {
        background: unset;
        list-style: circle;
        margin-left: 1.5rem;
        padding-left: 0;
      }
    }

    p:has(+ ul),
    h4:has(+ ul) {
      @apply mt-4;
    }

    svg {
      @apply inline h-6 w-6 md:h-8 md:w-8 xl:h-10 xl:w-10;
    }
  }
}

.video-play-button {
  @apply absolute left-[50%] top-[50%] h-20 w-20 -translate-x-1/2 -translate-y-1/2 transform cursor-pointer rounded-full bg-no-repeat shadow-md;
  background: url("https://cdn.sanity.io/images/hl9czw39/production/d5a5334866bd6d5a0012fb048bdb601f9370d94e-320x320.png")
    no-repeat center;
  background-color: var(--color-grey-100);
  background-size: contain;
}
@layer walkthrough {
  @theme {
    --desktop-columns: 60; /* 960px image width */
    --desktop-rows: 35; /* 560px image height */
    --desktop-cell-size-base: 0.5rem;
    --desktop-cell-size-xl: 0.7rem;
    --desktop-cell-size-xml: 0.8rem;
    --desktop-cell-size-xxl: 1rem;

    --mobile-columns: 20; /* 320px image width */
    --mobile-rows: 40; /* 640px image height */
    --mobile-cell-size-base: 0.9rem;
  }
  .walkthrough-info-content {
    li {
      background: url("/assets/images/tontine-bullet-icon.svg");
      background-repeat: no-repeat;
      background-size: 17px;
      padding-left: 25px;
      list-style: none;
      background-position-y: 3px;
      margin-block: 0.5rem;
    }
  }
}

@utility vertical-text {
  writing-mode: vertical-lr;
  text-orientation: mixed;
}

@utility scrollbar-hidden {
  &::-webkit-scrollbar {
    display: none;
  }
}

@layer embla {
  @theme {
    --embla-spacing: 1rem;
    --embla-size-walkthrough-desktop: 100%;
    --embla-size-walkthrough-mobile: 100%;
    --embla-size-base: 0 0 100%; /* 1 card shown */
    --embla-size-md: 0 0 calc(100% / 2); /* 2 cards shown */
    --embla-size-lg: 0 0 calc(100% / 3); /* 3 cards shown */
  }

  .embla-gradient {
    .left,
    .right {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 13rem;
      z-index: 1;
    }

    .left {
      left: 0;
      background: linear-gradient(to right, white, rgba(255, 255, 255, 0));
    }

    .right {
      right: 0;
      background: linear-gradient(to left, white, rgba(255, 255, 255, 0));
    }
  }
}

/* Used for color variations */
@layer colorPalettes {
  .red {
    @apply bg-red-100 text-red-700;

    &.light {
      @apply bg-red-50 text-red-800;
    }
  }

  .green {
    @apply bg-green-100 text-green-700;

    &.light {
      @apply bg-green-50 text-green-800;
    }
  }

  .blue {
    @apply bg-blue-100 text-blue-700;

    &.light {
      @apply bg-blue-50 text-blue-800;
    }
  }

  .gray {
    @apply bg-gray-100 text-gray-700;

    &.light {
      @apply bg-gray-50 text-gray-800;
    }
  }

  .yellow {
    @apply bg-yellow-100 text-yellow-700;

    &.light {
      @apply bg-yellow-50 text-yellow-800;
    }
  }

  .brand {
    @apply bg-brand-100 text-brand-700;

    &.light {
      @apply bg-brand-50 text-brand-800;
    }
  }
}
