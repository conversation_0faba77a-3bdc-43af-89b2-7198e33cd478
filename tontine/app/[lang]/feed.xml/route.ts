import RSS from 'rss'

import { processContentPosts } from '../../../helper-functions/AppUtilFunctions'
import {
  generatePathWithTrailingSlash,
  getMuxContentUrl,
} from '../../../helper-functions/UtilFunctions'
import {
  rssContentQuery,
  rssFeedQuery,
} from '../../../sanity-queries/miscellaneous-queries/miscellaneous-queries'
import { fetchSanityData } from '../../../sanity-queries/query-fetch-function'
import { websiteDomain } from '../../../serverless/keys'
import type { RSSQueryType } from '../../../types/common.types'
import type { ContentPostFeedData } from '../../../types/sections/content-section.types'

/**
 * Function for generating dynamic RSS feed for content pages
 */
export async function GET() {
  const contentPosts = await fetchSanityData<
    ContentPostFeedData['pagesOnWebsite']
  >({
    query: rssContentQuery,
  })
  const processedPosts = processContentPosts(contentPosts)
  const logo =
    'https://cdn.sanity.io/images/hl9czw39/production/467811f734a8b9285e0df4161d4a500ad076e4cf-335x296.png'
  const { description, shortName, updatedAt } =
    await fetchSanityData<RSSQueryType>({
      query: rssFeedQuery,
    })
  const siteUrl =
    process.env.NODE_ENV === 'production'
      ? websiteDomain
      : 'http://localhost:3000'
  const feedOptions = {
    title: shortName,
    description,
    language: 'en',
    site_url: siteUrl,
    feed_url: `${siteUrl}/feed.xml`,
    image_url: logo,
    pubDate: updatedAt,
    copyright: `All rights reserved - ${shortName} - ${new Date().getFullYear()}`,
  }

  const feed = new RSS(feedOptions)

  processedPosts.map((post) => {
    const commonProperties = {
      title: post.title,
      url: generatePathWithTrailingSlash({
        segments: [siteUrl, post.slug],
        endsWithSlash: true,
      }),
      date: post.date,
      description: post.description,
      custom_namespaces: {
        media: 'http://search.yahoo.com/mrss/',
      },
    }

    const customElements = post.videoFile.playbackId
      ? [
          {
            'media:content': {
              _attr: {
                url: getMuxContentUrl(post.videoFile.playbackId),
                duration: Math.round(post.videoFile.duration),
                width: post.videoFile.width,
                height: post.videoFile.height,
                medium: 'video',
              },
            },
          },
          {
            'media:thumbnail': {
              _attr: {
                url: post.image,
              },
            },
          },
        ]
      : [
          {
            'media:content': {
              _attr: {
                url: post.image,
                medium: 'image',
              },
            },
          },
        ]

    return feed.item({
      ...commonProperties,
      custom_elements: customElements,
    })
  })

  const feedXml = feed.xml({ indent: true })
  // Add media namespace to the RSS feed
  const namespacedFeedXml = feedXml.replace(
    /<rss/,
    '<rss xmlns:media="http://search.yahoo.com/mrss/"'
  )

  return new Response(namespacedFeedXml, {
    headers: {
      rel: 'self',
      'Content-Type': 'application/xml; charset=utf-8',
    },
  })
}
