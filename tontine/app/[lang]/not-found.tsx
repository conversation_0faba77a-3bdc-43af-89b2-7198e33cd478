import { ErrorContent } from '../../components/error-page-content/ErrorContent'
import { fetchPageSEOData } from '../../sanity-queries/query-fetch-function'
import { logServerless } from '../../serverless/ApiUtilFunctions'

export async function generateMetadata() {
  try {
    const seoData = await fetchPageSEOData({
      slug: null,
      preview: false,
      lang: 'en',
    })
    return seoData
  } catch (error) {
    logServerless({
      message: 'SEO data for 404 page not found',
      logLevel: 'error',
      error,
    })
    return {
      title: 'Not Found',
      description: 'The page you are looking for does not exist',
    }
  }
}

/**
 * Error page to which user is redirected when a page is not found
 */
export default function NotFound() {
  return <ErrorContent />
}
