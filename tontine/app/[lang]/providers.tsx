'use client'

import { AuthProvider } from '../../providers/AuthProvider'
import { LanguageProvider } from '../../providers/LanguageProvider'
import { ToastProvider } from '../../providers/ToasterProvider'
import { WebsiteIdProvider } from '../../providers/WebsiteIdProvider'
import type { LanguagesType } from '../../types/common.types'
import { titilliumWebFont } from '../fonts'

/**
 * Wraps every page that is generated in these components
 */
export function Providers({
  children,
  lang,
}: {
  children: React.ReactNode
  lang: LanguagesType
}) {
  return (
    <div className={titilliumWebFont.className}>
      <LanguageProvider initialLang={lang}>
        <WebsiteIdProvider>
          <AuthProvider>
            <ToastProvider>{children}</ToastProvider>
          </AuthProvider>
        </WebsiteIdProvider>
      </LanguageProvider>
    </div>
  )
}
