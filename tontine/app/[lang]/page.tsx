import { cookies, draftMode } from 'next/headers'

import { notFound } from 'next/navigation'
import { PageContent } from '../../components/page-content/PageContent'
import { WebschemasWebsiteWide } from '../../components/web-schema/WebschemasWebsiteWide'
import { getWebsiteId } from '../../helper-functions/UtilFunctions'
import {
  fetchPageSEOData,
  fetchSanityData,
} from '../../sanity-queries/query-fetch-function'
import { homePageQuery } from '../../sanity-queries/sanity-queries'
import { logServerless } from '../../serverless/ApiUtilFunctions'
import type { HomePageData, TParams } from '../../types/pages.types'

export async function generateMetadata({ params }: { params: TParams }) {
  const { lang } = await params
  try {
    const { isEnabled } = await draftMode()
    const seoData = await fetchPageSEOData({
      slug: null,
      preview: isEnabled,
      lang,
    })
    return seoData
  } catch (error) {
    logServerless({
      message: 'SEO data for homepage not found',
      logLevel: 'error',
      error,
    })
    return {
      title: 'Not Found',
      description: 'The page you are looking for does not exist',
    }
  }
}

const Home = async () => {
  const websiteId = await getWebsiteId(cookies)

  const pageData = await fetchSanityData<HomePageData>({
    query: homePageQuery,
    websiteIdCookie: websiteId,
  })

  if (!pageData?.homepage) {
    notFound()
  }

  return (
    <>
      <PageContent
        pageData={pageData?.homepage?.pageSections}
        pageDomain={pageData?.pageDomain}
      />

      <WebschemasWebsiteWide webSchemaData={pageData?.webschemasOnWebsite} />
    </>
  )
}

export default Home
