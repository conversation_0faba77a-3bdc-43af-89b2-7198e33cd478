'use client'

import { createContext, useContext } from 'react'

export const AuthContext = createContext(
  {} as {
    isAuth?: boolean
    login: (session: object) => void
    logout: () => void
    listenForWebappEvent: () => void
    sendMessageToIframe: (params: {
      eventId: 'TERMINATE_REF_SESSION' | 'CONSENT_CHANGE'
      eventData?: object
    }) => void
  }
)

export const useAuth = () => {
  return useContext(AuthContext)
}
