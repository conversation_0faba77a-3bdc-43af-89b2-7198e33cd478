'use client'

import { createContext, useContext } from 'react'

import { defaultWebsiteId } from '../serverless/keys'

type WebsiteIdContextType = {
  websiteId: string
  isLoaded: boolean
  setWebsiteId: (newWebsiteId: string) => void
}

export const WebsiteIdContext = createContext<WebsiteIdContextType>({
  websiteId: defaultWebsiteId,
  isLoaded: false,
  // biome-ignore lint/suspicious/noEmptyBlockStatements: <explanation>
  setWebsiteId: () => {},
})

export const useWebsiteId = (): WebsiteIdContextType => {
  const context = useContext(WebsiteIdContext)
  return context
}
