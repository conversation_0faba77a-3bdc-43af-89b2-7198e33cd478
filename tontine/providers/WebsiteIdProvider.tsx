'use client'

import { getCookie, setCookie } from 'cookies-next'
import type { ReactNode } from 'react'
import { useEffect, useState } from 'react'

import { cookieWebsiteId, defaultWebsiteId } from '../serverless/keys'
import { WebsiteIdContext } from './WebsiteIdContext'

/** The WebsiteIdProvider component is responsible for managing the websiteId.
 * It sets the websiteId by default to the defaultWebsiteId and updates the cookie
 * if the websiteId changes.
 *
 * The provider also provides a setWebsiteId function that can be used to update
 * the websiteId and update the cookie.
 *
 * Additionally, the provider sets the websiteId to the value stored in the cookie
 * if the cookie exists.
 *
 * The provider only updates the cookie if the websiteId changes.
 */
export const WebsiteIdProvider = ({ children }: { children: ReactNode }) => {
  const [websiteId, setWebsiteIdState] = useState<string>(defaultWebsiteId)
  const [isLoaded, setIsLoaded] = useState(false)

  const setWebsiteId = (newWebsiteId: string) => {
    if (websiteId !== newWebsiteId) {
      setWebsiteIdState(newWebsiteId)
      setCookie(cookieWebsiteId, newWebsiteId)
    }
    setIsLoaded(true)
  }

  // biome-ignore lint/correctness/useExhaustiveDependencies: Will cause unnecessary re-renders
  useEffect(() => {
    const initializeWebsiteId = async () => {
      const websiteIdCookie = await getCookie(cookieWebsiteId)
      setWebsiteId(websiteIdCookie ?? defaultWebsiteId)
    }

    initializeWebsiteId()
  }, [])

  return (
    <WebsiteIdContext.Provider value={{ websiteId, isLoaded, setWebsiteId }}>
      {children}
    </WebsiteIdContext.Provider>
  )
}
