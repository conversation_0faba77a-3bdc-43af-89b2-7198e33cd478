'use client'

import mixpanel from 'mixpanel-browser'
import { usePathname, useRouter } from 'next/navigation'
import type React from 'react'
import { useState } from 'react'

import { CONSTANTS } from '../data-resource/constants'
import { isLoadedWindow } from '../helper-functions/UtilFunctions'
import { isProd } from '../serverless/keys'
import { AuthContext } from './AuthContext'
import { myTontineLiteOrigin } from './MTLOrigins'

type EventId =
  | 'SUCCESS_VERIFY'
  | 'REDIRECT'
  | 'TERMINATE_SUCCESS'
  | 'TERMINATE_WEB_SESSION'
  | 'ASK_FOR_CONSENT_COOKIE'

const storageKey = 'refIsAuth'
const logoutKey = 'hasLoggedOut'

const origins = [
  CONSTANTS.MY_TONTINE_LITE_DEV,
  CONSTANTS.MY_TONTINE_LITE_STAGING,
  CONSTANTS.MY_TONTINE_LITE_PROD,
]

const allowedOrigins = [
  ...origins,
  ...origins.map((origin) => origin.slice(0, -1)),
]

/**
 * Checks if the local storage has a session object
 */
const checkForSession = () => {
  if (isLoadedWindow()) {
    return Boolean(localStorage?.getItem(storageKey))
  }
  return undefined
}

/** Returns the contentWindow object of the referral or the tontinator iFrame
 * based on which iframe is on the page.
 */
const getIframeWindow = () => {
  const referralWindow = document?.querySelector(
    '#iframe-referral'
  ) as HTMLIFrameElement

  const tontinatorWindow = document?.querySelector(
    '#iframe-tontinator'
  ) as HTMLIFrameElement

  const iframeWindow =
    referralWindow?.contentWindow ?? tontinatorWindow?.contentWindow

  return iframeWindow
}

/**
 * Sends a message to the iframe tontinator or referral
 */
const sendMessageToIframe = ({
  eventId,
  eventData,
}: {
  eventId: 'TERMINATE_REF_SESSION' | 'CONSENT_CHANGE'
  eventData?: object
}) => {
  const iframeWindow = getIframeWindow()

  if (iframeWindow) {
    iframeWindow.postMessage(
      {
        payload: { eventId, eventData },
      },
      myTontineLiteOrigin
    )
  }
}

/**
 * @note Does not do actual authentication in any shape or form!
 * It is just a simple bad code to make sure webapp and website are synced
 */
export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [isAuth, setIsAuth] = useState(checkForSession())
  const router = useRouter()
  const location = usePathname()

  /**
   * Creates a session object and sets the UI state to `auth`
   */
  const login = (session: object) => {
    if (isLoadedWindow() && getIframeWindow()) {
      // Checks if user has pressed the logout button
      // if they have then, the login process will be stopped
      // and a terminate session will be issued for the
      // mtl to delete the auth cookies
      if (localStorage?.getItem(logoutKey)) {
        localStorage.removeItem(logoutKey)
        sendMessageToIframe({ eventId: 'TERMINATE_REF_SESSION' })
        setIsAuth(false)
        return
      }

      localStorage?.setItem(storageKey, JSON.stringify(session))
      setIsAuth(true)
    }
  }

  /**
   * Makes an API call to the backend to delete the auth token,
   * and issues a terminate event to the MTL.
   * The event is issues `onFinally`, even if the API responds with 400,
   * the event will be issued for now
   */
  const logout = () => {
    if (isLoadedWindow() && getIframeWindow()) {
      localStorage?.removeItem(storageKey)
      setIsAuth(false)
      sendMessageToIframe({ eventId: 'TERMINATE_REF_SESSION' })
      // replaces the current path to get rid of the verify token from URL
      // on the tontinator path
      if (location.includes('/')) {
        router.replace(location)
      }
      return
    }

    // used to signal to stop with login
    // if the user has clicked the button on some page
    // where the tontinator is not loaded
    localStorage.setItem(logoutKey, 'true')
    setIsAuth(false)
  }

  /**
   * Listens for messages sent from the webapp
   */
  const listenForWebappEvent = () => {
    if (isLoadedWindow()) {
      window.onmessage = (
        // Expected message from webapp
        event: MessageEvent<{
          source: 'MTL'
          // Only event Id issues from the webapp
          payload: {
            eventId: EventId
            eventData?: {
              path: string
              liteAuthToken?: string
            }
          }
        }>
      ) => {
        const allowOrigin = allowedOrigins.includes(
          // Not sure if this is also needed on staging?
          event?.origin
        )

        // Only process allowed origins
        if (allowOrigin) {
          // ADD NEW EVENT IDs from here
          if (event?.data?.payload?.eventId === 'SUCCESS_VERIFY') {
            // Create a session object
            const session = {
              source: event?.data?.source,
              eventId: event?.data?.payload?.eventId,
              origin: event?.origin,
            }
            login(session)
          }

          if (event?.data?.payload?.eventId === 'REDIRECT') {
            const redirectPath = event?.data?.payload?.eventData?.path
            router.push(redirectPath ?? '')
          }

          if (event?.data?.payload?.eventId === 'TERMINATE_WEB_SESSION') {
            setIsAuth(false)
            localStorage?.removeItem(storageKey)
          }

          if (event?.data?.payload?.eventId === 'ASK_FOR_CONSENT_COOKIE') {
            event?.source?.postMessage(
              {
                payload: {
                  eventId: 'CONSENT_COOKIE',
                  eventData: {
                    track: isProd
                      ? mixpanel.has_opted_in_tracking()
                      : undefined,
                  },
                },
              },
              {
                targetOrigin: event?.origin,
              }
            )
          }
        }
      }
    }
  }

  return (
    <AuthContext.Provider
      value={{
        isAuth,
        login,
        logout,
        listenForWebappEvent,
        sendMessageToIframe,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}
