'use client'

import { createContext, useContext } from 'react'

import { CONSTANTS } from '../data-resource/constants'
import type { LanguagesType } from '../types/common.types'

type LanguageContextType = {
  language: LanguagesType
  isLoaded: boolean
  setLanguage: (language: LanguagesType) => void
}

export const LanguageContext = createContext<LanguageContextType>({
  language: CONSTANTS.LANGUAGES[0],
  isLoaded: false,
  // biome-ignore lint/suspicious/noEmptyBlockStatements: <explanation>
  setLanguage: () => {},
})

export const useLanguage = () => {
  const context = useContext(LanguageContext)
  return context
}
