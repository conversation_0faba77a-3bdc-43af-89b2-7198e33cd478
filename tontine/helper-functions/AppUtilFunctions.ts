import type { StaticPageParamData } from '../types/pages.types'
import type { ContentPostFeedData } from '../types/sections/content-section.types'
import { generatePathWithTrailingSlash } from './UtilFunctions'

/**
 * Processes the given pages and returns an array of objects.
 * Each object contains the slug of the page and, if available and additional object for each of the nested slugs.
 */
export function processPages(pages: StaticPageParamData) {
  return pages?.pagesOnWebsite
    .filter((page) => page?.pageSlug)
    .flatMap((page) => {
      const slugArray = page?.slugArray ?? []
      return [
        { slug: [page.pageSlug] },
        ...(Array.isArray(slugArray) && slugArray.length > 0
          ? slugArray.map((item) => ({
              slug: [page.pageSlug, item?.slug],
            }))
          : []),
      ]
    })
}

/**
 * Processes content posts to generate a list of formatted post data.
 *
 * This function takes an array of pages and processes each page to generate a
 * list of content post objects. Each content post object contains the slug, title,
 * description, and date of the post.
 */
export function processContentPosts(
  pages: ContentPostFeedData['pagesOnWebsite']
) {
  return pages.flatMap((page) => [
    ...(Array.isArray(page.subPages) && page.subPages.length > 0
      ? page.subPages.map((item) => ({
          slug: generatePathWithTrailingSlash({
            segments: [page.pageSlug, item.slug],
          }),
          title: item.title,
          description: item.subtitle,
          date: item.updatedAt,
          image: item.postImageUrl,
          videoFile: {
            playbackId: item.videoFile?.playbackId,
            duration: item.videoFile?.duration,
            width: item.videoFile?.width,
            height: item.videoFile?.height,
          },
        }))
      : []),
  ])
}
