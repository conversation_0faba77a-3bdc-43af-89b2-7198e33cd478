import { access, writeFile } from 'fs/promises'

import { logServerless } from '../serverless/ApiUtilFunctions'
import type { DownloadFileItem } from '../types/common.types'
import type { PageWithImages } from '../types/pages.types'
import type { ContentPost } from '../types/sections/content-section.types'
import {
  convertSanityURLToNext,
  generatePathWithTrailingSlash,
} from './UtilFunctions'

/**
 * Maps an array of ArticleCardItem objects to an array of DownloadFileItem objects.
 * - `articles` An array of ArticleCardItem objects.
 *
 * Only articles with a defined `researchSlug` will be included in the output.
 *
 * The `url` and `originalFilename` properties of the DownloadFileItem objects will be
 * taken from the `researchManuscript.asset` property of the corresponding ArticleCardItem.
 */
const mapResearchPDFs = (
  articles: Array<ContentPost>
): Array<DownloadFileItem> => {
  return articles
    ?.filter(
      (article) => article?.slug.current !== undefined && article?.manuscript
    )
    .map((article) => ({
      url: article?.manuscript?.asset.url ?? '',
      fileName: article?.manuscript?.asset.originalFilename ?? '',
      slug: article?.slug?.current,
    }))
}

/**
 * Processes an array of image data and writes it to a JSON file.
 * - `inputArray` - An array of pages containing the page slug and the urls of the images on that page.
 * - `directory` - The directory where the JSON file will be written to.
 */
async function processAndWriteImageJSON({
  pagesArray,
  directory,
}: {
  pagesArray: PageWithImages['pagesOnWebsite']
  directory: string
}) {
  try {
    const filteredObjects = pagesArray?.filter(
      ({ images }) => images?.length > 0
    )
    const transformedObjects = filteredObjects?.map(({ slug, images }) => ({
      slug:
        slug === null
          ? '/'
          : `${generatePathWithTrailingSlash({ segments: [slug], leadingSlash: true })}`,
      images: images?.map((image) => convertSanityURLToNext(image)),
    }))

    await writeFile(directory, JSON.stringify(transformedObjects, null, 2), {
      encoding: 'utf8',
    })
    await access(directory)
    logServerless({
      message: `Successfully wrote images ${directory}`,
      logLevel: 'success',
    })
  } catch (error) {
    logServerless({
      message: `Failed to write images to ${directory}`,
      logLevel: 'error',
      error,
    })
  }
}

export { mapResearchPDFs, processAndWriteImageJSON }
