import path from 'path'

import type { AxiosResponse } from 'axios'
import { access, mkdir, rm as rmdir, writeFile } from 'fs/promises'

import { axiosRequest } from '../hooks/Axios/useAxios'
import { logServerless } from '../serverless/ApiUtilFunctions'
import type { DownloadFileData, DownloadFileItem } from '../types/common.types'

/**
 * Deletes directory and all of it's contents
 * - `dirPath` - Relative directory path, without process.cwd() or __dirname.
 */
async function clearDirectory(dirPath: string) {
  try {
    const absoluteDirPath = path.join(process.cwd(), dirPath)
    // Check if the directory exists
    await access(absoluteDirPath)
    // If the directory exists, remove it and all its contents
    await rmdir(absoluteDirPath, { recursive: true })
    logServerless({
      message: `Successfully wiped directory: ${dirPath}`,
      logLevel: 'success',
    })
  } catch (_error) {
    // Create directory if there's an error
    logServerless({
      message: `Directory: ${dirPath} does not exist, skip wipe.`,
      logLevel: 'info',
    })
  }
}

/**
 * Asynchronously fetches a file and writes it to a directory.
 * - `data` An object of type DownloadFileData representing the file data to use.
 * - `directory` Relative directory path, without process.cwd() or __dirname.
 */
async function fetchAndWriteFile({
  data,
  directory,
}: {
  data: DownloadFileData
  directory: string
}): Promise<void> {
  try {
    const dir = path.join(process.cwd(), directory)

    await mkdir(dir, { recursive: true })

    const filePath = path.join(dir, data.fileName)

    const response: AxiosResponse<ArrayBuffer> = await axiosRequest({
      retries: 2,
      backoff_factor: 2,
      url: data.url,
      config: {
        responseType: 'arraybuffer',
      },
    })

    await writeFile(filePath, Buffer.from(response.data))

    await access(filePath)
  } catch (error) {
    logServerless({
      message: `Failed to write file ${data.fileName} to ${directory}`,
      logLevel: 'error',
      error,
    })
  }
}

/**
 * Fetches and writes an array of files.
 * - `items` An array of DownloadFileItem objects representing the files to download.
 * - `directory` Relative directory path, without process.cwd() or __dirname.
 *
 * If a `slug` is provided in a DownloadFileItem, the file will be written to a subdirectory with that name. If no `slug` is provided, the file will be written directly to the specified directory.
 */
async function fetchAndWriteFiles({
  items,
  directory,
}: {
  items: Array<DownloadFileItem>
  directory: string
}): Promise<void> {
  const promises = items?.map((item) => {
    const filePath = item?.slug ? path.join(directory, item.slug) : directory
    return fetchAndWriteFile({
      data: {
        url: item.url,
        fileName: item.fileName,
      },
      directory: filePath,
    })
  })

  const results = await Promise.allSettled(promises ?? [])

  results?.forEach((result, index) => {
    if (result.status === 'rejected') {
      logServerless({
        message: `Couldn't process file: ${items?.[index]?.fileName}`,
        logLevel: 'error',
        error: result.reason,
      })
    }
  })
}

export { clearDirectory, fetchAndWriteFile, fetchAndWriteFiles }
