# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: Deploy studio and schemas

on:
  push:
    branches:
      - 'production-sanity'

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [22.x]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      - name: Cache root dependencies
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-root-node-modules-
            ${{ runner.os }}-
      - name: Cache sanity-studio dependencies
        uses: actions/cache@v4
        with:
          path: sanity-studio/node_modules
          key: ${{ runner.os }}-sanity-node-modules-${{ hashFiles('sanity-studio/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-sanity-node-modules-
            ${{ runner.os }}-
      - name: Install Project dependencies with CI
        run: npm ci
      - name: Install Node dependencies with CI
        run: cd sanity-studio && npm ci
      - name: Lint schemas using biome & check types
        run: npm run lint && npm run check-types:sanity
      - name: Check code formatting using prettier
        run: npm run format
      - name: Build studio, deploy studio and schemas
        env:
          SANITY_AUTH_TOKEN: ${{ secrets.SANITY_AUTH_TOKEN }}
        run: cd sanity-studio && npx sanity deploy
      - name: Deploy schemas
        env:
          SANITY_AUTH_TOKEN: ${{ secrets.SANITY_AUTH_TOKEN }}
        run: cd sanity-studio && npx sanity graphql deploy --dataset production
