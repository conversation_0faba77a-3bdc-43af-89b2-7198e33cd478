import axios from 'axios'
import {
  getDailyRebuildErrorMessage,
  logServerless,
} from '../../tontine/serverless/ApiUtilFunctions'

export async function handler() {
  try {
    logServerless({
      message: 'Daily rebuild function initiated.',
      logLevel: 'info',
    })
    const buildHook = process?.env?.BUILD_HOOK
    const siteId = process?.env?.SITE_ID
    const apiKey = process?.env?.NETLIFY_API_TOKEN

    if (!buildHook) return getDailyRebuildErrorMessage('Build hook')
    if (!siteId) return getDailyRebuildErrorMessage('Site ID')
    if (!apiKey) return getDailyRebuildErrorMessage('API Key')

    logServerless({
      message: 'Attempting to get previous builds info...',
      logLevel: 'info',
    })
    const buildsResponse = await axios.get(
      `https://api.netlify.com/api/v1/sites/${siteId}/builds`,
      {
        headers: {
          Authorization: `Bearer ${apiKey}`,
        },
      }
    )
    if (buildsResponse?.data?.[0]?.created_at) {
      const lastBuildTime = new Date(buildsResponse.data[0].created_at)
      logServerless({
        message: `Getting build info successful, most recent build time was: ${lastBuildTime}`,
        logLevel: 'success',
      })
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
      if (lastBuildTime > oneDayAgo) {
        logServerless({
          message: 'Build has happened in the last 24 hours, aborting...',
          logLevel: 'info',
        })
        return {
          statusCode: 200,
        }
      }
    } else {
      logServerless({
        message:
          'Could not get previous builds info, will attempt to initiate build',
        logLevel: 'warn',
      })
    }

    logServerless({
      message: 'Attempting to initiate build...',
      logLevel: 'info',
    })
    const { status } = await axios.post(buildHook)
    logServerless({
      message: `Successfully initiated build hook, server responded with status: ${status}.`,
      logLevel: 'success',
    })

    return {
      statusCode: status,
    }
  } catch (error) {
    logServerless({
      message: 'Could not run scheduled build',
      logLevel: 'error',
      error,
    })
    return {
      statusCode: 500,
    }
  }
}
